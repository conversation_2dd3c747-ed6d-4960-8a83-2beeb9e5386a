{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985175b001eadfc4504853e0cb7c7b4ded", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839de721396e1e51af59345bdd3090949", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981402e39902e777828e0651177e62acfe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe13b13ef50577728391ab988c2a535", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981402e39902e777828e0651177e62acfe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PhoneNumberKit/PhoneNumberKit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PhoneNumberKit/PhoneNumberKit.modulemap", "PRODUCT_MODULE_NAME": "PhoneNumberKit", "PRODUCT_NAME": "PhoneNumberKit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b856f3152e1adf7255bcd2cc25e98d26", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98621bc1dcc7cbb4c902670ed92736dff6", "guid": "bfdfe7dc352907fc980b868725387e98756a80c583f05cfccfff91474ffed95e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c240a7433dac5a6a0d9547a1bd030e0d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f9f28345a0d7fee7894c030bb63c2940", "guid": "bfdfe7dc352907fc980b868725387e9855fe45c0a8ad9deff61275e46d4016e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864f304af6981568aa9764f2213d33dda", "guid": "bfdfe7dc352907fc980b868725387e982ef6ae3f1f382bee47cc950a8fa370e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b348ef40c2ec272276d567c1c2391352", "guid": "bfdfe7dc352907fc980b868725387e987dde96f7bf5252bcfafdb838df16142a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982acd4b844a02887c22fd786a817a1571", "guid": "bfdfe7dc352907fc980b868725387e98758a84c6104b528c9339910bdcfc6e43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d1d941055beff19ed711d909125628", "guid": "bfdfe7dc352907fc980b868725387e9806cc03b5a83d13a8ff88bc1aa4d517db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc9ad4158ab6cde53654c35a4e76202c", "guid": "bfdfe7dc352907fc980b868725387e98258a8c841d9ebe16fa405c225100e3de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d4b37356988d597e570d888da3dd1ac", "guid": "bfdfe7dc352907fc980b868725387e989a0ba3d49e0b37d7675bbb49d6e9c662"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966c965f991d4ce63d52d8f7e92fe725", "guid": "bfdfe7dc352907fc980b868725387e98f28a32bd6b9326c9000b3301d0696b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c67e90b767c3b69d44f85c363848b190", "guid": "bfdfe7dc352907fc980b868725387e987a574a5d779e99772e907d0959e55530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfe68b580d1a904018cb7ebb876aca41", "guid": "bfdfe7dc352907fc980b868725387e98fcb54087fe44a58df8f2a18953c2b882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c003e69cd73398ac9371e9e5f73d8b7b", "guid": "bfdfe7dc352907fc980b868725387e98b921c65f7cb183b57e8576455169a6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d8e7080aafc299a39e7bc9b277ea1e", "guid": "bfdfe7dc352907fc980b868725387e98bc1bf2b71a50450fe7591482e55df7e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e23afc90d21e668a634c82e27e4cf4", "guid": "bfdfe7dc352907fc980b868725387e98a1217478ddeee967e04f383f067bd6ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5e2e197e763713a064eb1b1a8007811", "guid": "bfdfe7dc352907fc980b868725387e985cef31bccc9b7611d68ff76418f6b2ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ebbf6eaad650e10a63c1f255f5438b", "guid": "bfdfe7dc352907fc980b868725387e98fa017208ff8379345438e11c29eafeab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d16be2958ef365941732d5be26e6e08", "guid": "bfdfe7dc352907fc980b868725387e98c2df62b801cc3c4277ac44872f425845"}], "guid": "bfdfe7dc352907fc980b868725387e9878f621e9388fe5a858e097688ed8535c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98765aecfd5daeab02415ba177d10dcbf6"}], "guid": "bfdfe7dc352907fc980b868725387e984d539aa99eb8ebe3e94ad2939025b7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987905ad1776a2ba394baa87a422315477", "targetReference": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10e0cd9e2ea8087836951aafb2d3250", "guid": "bfdfe7dc352907fc980b868725387e9895f50298dc4926ec37c3e9a692a0bfcb"}], "guid": "bfdfe7dc352907fc980b868725387e98e7c2415f3f6ad0ff62b20bdc8cba18d1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9830efee903e29d8dc896f9a9a5aa8ca9d", "name": "PhoneNumberKit-PhoneNumberKitPrivacy"}], "guid": "bfdfe7dc352907fc980b868725387e985c9de23706ec6ffbd0536cc5484b3896", "name": "PhoneNumberKit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f3bf2315f331e6c08a312dfd64b37cff", "name": "PhoneNumberKit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}