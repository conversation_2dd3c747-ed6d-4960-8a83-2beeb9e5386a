{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f644e7780d46ac180094169d176209ba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989c237773631f99c259d1da0f09f9cf76", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833a0edc1f05c9dc10f2be7bba29f8b64", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbf2867cc302a58c2f5db3b52714f6b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833a0edc1f05c9dc10f2be7bba29f8b64", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980eae261ffc1b8ff0c69aeadfe92dd4c1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98de5a2481a6b45156c42fa7d54bb0c4f9", "guid": "bfdfe7dc352907fc980b868725387e98853157c61604cdca856c8f2d5c5abb8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5f62aed5cbc9503b167684fe1dddba7", "guid": "bfdfe7dc352907fc980b868725387e982226593e336f46b3db503a0b14eb1379", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981577a26d007630a9cd14ef3f3fe77e0e", "guid": "bfdfe7dc352907fc980b868725387e98de6a2f4da802c6815f4d2bb482e99391"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987379b5a61a29cf742f14710c2e65ade1", "guid": "bfdfe7dc352907fc980b868725387e98c75e7082514608a49c32fb9d004a1a92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a9468211ecc5c9f7d9ccebbecfdb10", "guid": "bfdfe7dc352907fc980b868725387e98e8d2f200ff75cde20b7ffb6defea50ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98187be1ceaa05d2d32508aaed9d592fa3", "guid": "bfdfe7dc352907fc980b868725387e9889b2e9fa2c499bc259f39b4584f9ee10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f10efd1c9a5039c276ebd4c14b35feca", "guid": "bfdfe7dc352907fc980b868725387e980f2cec2cf4bcff34fa83883cd704ed28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ae2182d2369633ba32be464709b8ef7", "guid": "bfdfe7dc352907fc980b868725387e98be1e3d802ace64c79e7adaf73bfed99f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f527778cb5641a6ac1a14da1b5382c8", "guid": "bfdfe7dc352907fc980b868725387e98a178fd87b1ebfa59a13492e7f4787ccc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b25bb2be4953addfeed0b6ae28d75ca0", "guid": "bfdfe7dc352907fc980b868725387e98d61f79d50cdc6c2c771d20b888f4cfa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e00123976df674366ffa8984a4b6cbb1", "guid": "bfdfe7dc352907fc980b868725387e9887754a948e739e7d1bb1e8f877fa5d7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98064f0124f449b96ab676b3f4210bed93", "guid": "bfdfe7dc352907fc980b868725387e98a463282a2a250683352339efb942982c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b06dc330292c5ec496b5f0dc09b428", "guid": "bfdfe7dc352907fc980b868725387e98d4dc41b0cce55220f47a1df545882a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2149e5057e8079eda6dc9700674f78", "guid": "bfdfe7dc352907fc980b868725387e980293d2bf389475a5dfbe8f983d6a3f37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba0561131a552ea953075c3d3a98e4df", "guid": "bfdfe7dc352907fc980b868725387e980eeb4792c15ef5a11cdad10229c36b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833f73ad493efb312e82e71455ee90947", "guid": "bfdfe7dc352907fc980b868725387e98fe458b4055497aefedf7a01b9b89a67e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb22de6dc7007ac47a5133a924d5bd17", "guid": "bfdfe7dc352907fc980b868725387e98440fa8baff4deb9ebcb56941832a44a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d428be97c36c52f706749cf9254f2a79", "guid": "bfdfe7dc352907fc980b868725387e984666269d83fcfd576e49364bc13adbdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b3583c7b811b464228bb0714c34fc6", "guid": "bfdfe7dc352907fc980b868725387e982d7ebba8e69d9ff3275cf78753bdd9f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23ae99d5ce97f1a7791bb44734dd1d5", "guid": "bfdfe7dc352907fc980b868725387e984ec6f78030c75745365ebf2dc7617de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4930cae0220cc2559bdf5f6910e6fa2", "guid": "bfdfe7dc352907fc980b868725387e981f7a86d383b7a474205dbf2f9e55ae9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a6e38ed843386d82795e674dca089aa", "guid": "bfdfe7dc352907fc980b868725387e98ede0306b8ff0342ed589180d454569b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98606263a47b432b9934ceab976751fda0", "guid": "bfdfe7dc352907fc980b868725387e9887acaaa52ff557aa33bc18e306fd499c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982eff85196a3b55f47b87366184750ba9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98473b6b571d8d10a9dac64bf0cbaffe05", "guid": "bfdfe7dc352907fc980b868725387e98da7551f9b126102d3e928b7a2e7010fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9cebad8942e717c767e018c13c04cf4", "guid": "bfdfe7dc352907fc980b868725387e9813011567fa674f01d95fe17ad56cd027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaa63e8d2579d077a9f112b37c6d0f8b", "guid": "bfdfe7dc352907fc980b868725387e989a6baec9e6b58bc3774b326528446803"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddd13b59133429c95ef963bb1bfd64c8", "guid": "bfdfe7dc352907fc980b868725387e9898030a78a282aae5de222a2cb5a55bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7fe365e23f0786619dba7e0182704e2", "guid": "bfdfe7dc352907fc980b868725387e98894b08877e159d5208530b7203c4bb1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4517fc00dea3dcc5f9d4b9df2aace5", "guid": "bfdfe7dc352907fc980b868725387e984399981ce7b9a05e2205fb80547af2ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985421425a40dc834c7354d6ef5ac0d7bc", "guid": "bfdfe7dc352907fc980b868725387e984794ec61a3b8d08b571ce216f9c00de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0a821e5abe55e70832d864a8c192483", "guid": "bfdfe7dc352907fc980b868725387e98bd2423b261cd3e2254ce22dde9b29dd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98301d657020ea3f49d59075623c9e6d2c", "guid": "bfdfe7dc352907fc980b868725387e98e8778dfc2a863fb3c33c52a85c3d511b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adab28e332923480fd33995eda9da4fb", "guid": "bfdfe7dc352907fc980b868725387e98662f1531ecb13ba6f0a4cc861f207c0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ca5297296c6e6e6d63cab27e90a814", "guid": "bfdfe7dc352907fc980b868725387e9873caecce6c9b93582d2cbfe75801169a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989012cd8e4c1b5ca9d6db5de8f2dea48e", "guid": "bfdfe7dc352907fc980b868725387e9833c2764ad88644be8c10cd6664cc69b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b8f6886c10d1c4e3aa34699f05f6ae", "guid": "bfdfe7dc352907fc980b868725387e981c38898227dfc4b30df6f8a0f7ea90fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c72150772955a3ac8e4b096d02fcd21", "guid": "bfdfe7dc352907fc980b868725387e980439b2d210a6f113393657f63b4f8e47"}], "guid": "bfdfe7dc352907fc980b868725387e986596f0bb2cfc9a86582fa0600aa08880", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98c71b3038322c9b448bf2a212c3c576d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e98c18bb0d622cfdbab1482e31b49d8f17e"}], "guid": "bfdfe7dc352907fc980b868725387e9827fd2774ac50212bc1278d9f6737cb73", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a5b82e2c4e48a641135b34ccafae589f", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98c27b88402e49ee37a736f6a176d492fd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}