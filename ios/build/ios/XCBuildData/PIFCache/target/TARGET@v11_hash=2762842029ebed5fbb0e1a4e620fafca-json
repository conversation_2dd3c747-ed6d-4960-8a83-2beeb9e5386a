{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98af0593ad1cf2b61151f5b8a65e9fa526", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d6cfe6be28fc9f5a81a821484dcecf1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98464aa413e02fc051533c8b40eee5afa5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98515d8e22e5d9a48cc7651d71b18abd97", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98464aa413e02fc051533c8b40eee5afa5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d150f2f86a20c7b0552e70df76176c80", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987c7b1a7abd15a57d1161419d00c14e92", "guid": "bfdfe7dc352907fc980b868725387e98e20a97a35b68e4b61cb92587b7456adc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a0630511d73bc6cc927691ad2da834d", "guid": "bfdfe7dc352907fc980b868725387e9885cebe3c80310ec2160a838ee1e4972b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834d727f6904c1157321c84246fbc0851", "guid": "bfdfe7dc352907fc980b868725387e9873eeaddc8ac9bc6fdb7ff99ce0b2d825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0856dd5023b0cfb436202da57e979fe", "guid": "bfdfe7dc352907fc980b868725387e9891a1dbf7b7173677b19b8574b50b3700", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98766db2661c5846ebd17f5412c2296675", "guid": "bfdfe7dc352907fc980b868725387e98fc10a95b125b27a216526327a4241aa3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98441cd58d3920f1a072316d506408765d", "guid": "bfdfe7dc352907fc980b868725387e98cc1981f99af51382534431590c4fd65e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea8c67d1c962d13f7c891f53015c3e3", "guid": "bfdfe7dc352907fc980b868725387e981412a98ab7cd95eef01e9c2974a07e40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe64d7ba1ab5f1c95d5c4e750f1d360e", "guid": "bfdfe7dc352907fc980b868725387e98dde8170aaeca27b8c9d8885b46a5e327", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba325d4d70fadc2c0c2ba897003c150f", "guid": "bfdfe7dc352907fc980b868725387e981db898630ece9234d7a4e9f83431ac12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6291e7d9a1978918677dd61eb80701b", "guid": "bfdfe7dc352907fc980b868725387e985e7dd0d2ce3f6730f28d9db660754709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da261acc912efe34b96a1d51e2263e06", "guid": "bfdfe7dc352907fc980b868725387e98a882eb1e57659fb626fdbfbbc39f1263", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986464841972f11a6b3fd2245f229eedfd", "guid": "bfdfe7dc352907fc980b868725387e98bed9a4674d2c7d6f095cce1c8a4bdd2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e53b28cf7e6ca235d273bb96780e2b3", "guid": "bfdfe7dc352907fc980b868725387e98b7d946eb4fe3f2ed550f23635f5860e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa5f2331781d985cced2dad3a42803e8", "guid": "bfdfe7dc352907fc980b868725387e988f27c2083d75722ac805f12f6cda7ab4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838cb4b52cd01155afe97a28dc7420bf3", "guid": "bfdfe7dc352907fc980b868725387e98298286afc85b6768f78de8380ebc1d1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c2df52a6811ddc75488566b22bccb8", "guid": "bfdfe7dc352907fc980b868725387e986a541d15790bf44496d1b13e9551ecbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c14e0cf6d83800ab5c2a295128cd5baa", "guid": "bfdfe7dc352907fc980b868725387e9863e73dec8eb1e6a1c4e7cc6b34282e7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cfc094c665a858aa42168f60eebb313", "guid": "bfdfe7dc352907fc980b868725387e98642f6be6d02744ef5d7e16dc64a72fc8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98540bf3a57639c4e6d4106f82906b975e", "guid": "bfdfe7dc352907fc980b868725387e983372890a1a8600fca3c70be921335bf8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897b92d0b5c8d73562f5061e5bdece26b", "guid": "bfdfe7dc352907fc980b868725387e980d61a45d7a1285a62f65b9d0ec425910", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abcb704ea407d285772938599c8a0016", "guid": "bfdfe7dc352907fc980b868725387e98e717ffbb8f1a468c2d9aa323a880fa7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beae7240fe754ad019e38ad6ce237a09", "guid": "bfdfe7dc352907fc980b868725387e985e998d1a947272ecb7373c53f305324e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff9f426a9204b0e54b8e1a8d4f108f3", "guid": "bfdfe7dc352907fc980b868725387e981da876120a7cd87b7617f47d0c43ce8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f34983c71b4b80a2c24a28731389d724", "guid": "bfdfe7dc352907fc980b868725387e9873697ee9b0c646e1f768bc2845d29722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e943eafcaef099329c0101d24cea627a", "guid": "bfdfe7dc352907fc980b868725387e98c7615516fe73032c51e7ada33e86cf89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae014cf9639d6fb48bf2ebc2fb08435", "guid": "bfdfe7dc352907fc980b868725387e9853cd3cbd6f0fcd59c8c131b4e67f220b"}], "guid": "bfdfe7dc352907fc980b868725387e980f2bb3eadfdd73fc28ccf0f3cc70b220", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815793aae8eb91f1b89269dccc484be59", "guid": "bfdfe7dc352907fc980b868725387e98b5cef454246780f200673c81ac5c9cf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f35b0500456f8cb95221b51bd2b8e5eb", "guid": "bfdfe7dc352907fc980b868725387e98dd7562298c0ddf20836b0b4eb2fdd9cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983baf0d353ec3445da793db843f363c96", "guid": "bfdfe7dc352907fc980b868725387e9857c3b2a432f0267e9944821c1a769d38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d214fbc45e045ee0b0f8b2a6cab111bc", "guid": "bfdfe7dc352907fc980b868725387e98888087fd0fc903952853d16241e6d92e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cad05f4eb8ff668c659bc9400306647", "guid": "bfdfe7dc352907fc980b868725387e98ead86c103c5f69c5d575ce0ff175f47f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6fc9bcd4c5ee7476019f07a68ef9b0d", "guid": "bfdfe7dc352907fc980b868725387e98b8c5907b4e61f28a6bc4694abb9e46dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cbea5623ef26b49d87aaebfbba31897", "guid": "bfdfe7dc352907fc980b868725387e98dcd5fd3eade5cdd0b4a80f1bd0dd0232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bea325f4563198eda775fb1b60036b5a", "guid": "bfdfe7dc352907fc980b868725387e98556131037fbe40e66a90a6b1afdec45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989947085552522544f6f5cecdba0e30de", "guid": "bfdfe7dc352907fc980b868725387e98a817c4f69d95ac02a758cfd04fbc8783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f12b683cffc754c6a97204f599359921", "guid": "bfdfe7dc352907fc980b868725387e98408486055df7717a2925fd9215b36bd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df739b23aff2cb5d3a5cf89862a4306", "guid": "bfdfe7dc352907fc980b868725387e98fd4ade0f68d35656d3b5985b772a6d2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98399513d9ea034c94f308e8bcf5d89b92", "guid": "bfdfe7dc352907fc980b868725387e98f3fc97e48d257522d45c8180f83ca67e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98873f7aa5a671b8a21d386d34f804ec8b", "guid": "bfdfe7dc352907fc980b868725387e98c6d3a9a1b0731fe9b5a9fd8c71d750c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebee4ef535e71cbef950d5b9d24aafa8", "guid": "bfdfe7dc352907fc980b868725387e98d3cc8872f47b36fbf7bed17945782dce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed6cb23dd77fce019c99918b55c8c80", "guid": "bfdfe7dc352907fc980b868725387e984c49345103e19ad9bab31464b3e40894"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857da2e99ee2a6d563fb35d9d375f0100", "guid": "bfdfe7dc352907fc980b868725387e98711b60e93a6354560c1b77caf24fe36c"}], "guid": "bfdfe7dc352907fc980b868725387e989d350743e44cb1acddbecbb08d15b16c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e981a6a96a58a05167b30d1727ac4a48aa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e98af30bdf508c452b8f464a8d8860e6db7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e98188cb25e358faf3e3b76a05d9e4e7c44"}], "guid": "bfdfe7dc352907fc980b868725387e987340ebd83f38793265d8df28f442eaf8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c7b71c2d17f3097c075ce9576324a456", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e985e1a9ad7a97daec7ffaa317e0e137be4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}