{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983630544e8b49ebd31487c49dae5f639b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849e3463aaa31232a7b63a29ce2fb32fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849e3463aaa31232a7b63a29ce2fb32fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9890bfc31dd55d020fa8f86db6a07fe61f", "guid": "bfdfe7dc352907fc980b868725387e981edfd38698068c109e5f814f5e4a6055", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885c0e3197b5fa7f7f5cf92a5c0b74e67", "guid": "bfdfe7dc352907fc980b868725387e98e44ea0d59edc9cf326f461c4edc8b6b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f3cf33e501c59c5124bbbe7aaf7528", "guid": "bfdfe7dc352907fc980b868725387e9808054ff88657f5f4a9121eb08482dd42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a85d1c69d219763956baf857b645711", "guid": "bfdfe7dc352907fc980b868725387e98c63dcbb1d49e192d4215b43dfcc573eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e520feab8ebd90ac1dada16c7bb5da79", "guid": "bfdfe7dc352907fc980b868725387e98b5cd957363787598d6f7aea4947494bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad0fe86333a072a083d4a8ad30283eee", "guid": "bfdfe7dc352907fc980b868725387e98d9ff6e51462c6aa74f3c8bb07b3a17a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392f178462de32aafd284232e040bf57", "guid": "bfdfe7dc352907fc980b868725387e98f0b81eccdf6f9f4354e1b5fc3b7069e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f97c39e133fbb4a4b0cf7b027ea4e44", "guid": "bfdfe7dc352907fc980b868725387e982f69314735d4a464362113b79c4a5d8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829640ec5e17636cbf0570045845123e7", "guid": "bfdfe7dc352907fc980b868725387e984363a2ba622225fbe85f9b02e4113775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98769d089661a8b2e60dbfbc13827cd17f", "guid": "bfdfe7dc352907fc980b868725387e98a992b9e90700201958c967c403aabb39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb3f560e19190fd30e41fe8c4c99795f", "guid": "bfdfe7dc352907fc980b868725387e98359d111b1a1af5f00f64f4399e6c18a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af5ac41d6f56c7b1938cbcf9a598fa7", "guid": "bfdfe7dc352907fc980b868725387e98e9ef437827e6d243b4695d7ea82ea05f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983380a0b1b8752d7f01598140312c95fe", "guid": "bfdfe7dc352907fc980b868725387e98fbd31237d7e080da5ab0719e8b1740d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd461adff439cc7b2a7c5dacde937737", "guid": "bfdfe7dc352907fc980b868725387e9884c53c68a4b506556a1947ff82c1daf4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd6eb1035db3b96df27562d9823de894", "guid": "bfdfe7dc352907fc980b868725387e984587bd174efca234b3aec913ed68ceb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860200c731d02d7e3ec6a28a4dbb562a2", "guid": "bfdfe7dc352907fc980b868725387e9825c32461fbb2aaa4e12b9443a7425752", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815ff93fca63c3824fb0a707be1187508", "guid": "bfdfe7dc352907fc980b868725387e98f66a7ba79f63abbb1f9be5aa6632f488", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803030ab6d8bde8f75b631621a60ded74", "guid": "bfdfe7dc352907fc980b868725387e9812a9d052cc584b524cfccd7c10554f2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d89b29c82c96b97f29446f0d99ea10b2", "guid": "bfdfe7dc352907fc980b868725387e985934ed61617fcd2150cc180e1b99986c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f401f61f8c15dcc3d8194b28e5420b8", "guid": "bfdfe7dc352907fc980b868725387e9825f99b1272f44ae4dfdea94ee4f74373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899522addef32093afbd52da01776d14a", "guid": "bfdfe7dc352907fc980b868725387e9877701f66c8ff3229ca49f963f1b5ba3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd0b3d8e8e2668d5ca3e1259d2d0e51a", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837eccdfd6b3053fb40538bc525cc5361", "guid": "bfdfe7dc352907fc980b868725387e986f21906108394e559b5f20f7b1b2b7c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edda11567d2661ad8ed6c981a1bbdd46", "guid": "bfdfe7dc352907fc980b868725387e983577603b1f8bf82fa87b1f0d8f22ba8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b073c41bad13eeb4761ce7b68b28a405", "guid": "bfdfe7dc352907fc980b868725387e98dee846baf0ac46e7ac436eec838e500e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e692b462b6a207e44974c40ae62dcbf0", "guid": "bfdfe7dc352907fc980b868725387e987c7b231934ce09ccf3bb4d6f09388bc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b12522f6e78d0b8bedcb257025b60034", "guid": "bfdfe7dc352907fc980b868725387e98629984a243136509c4f8eb078ee5ae35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abee876c4075c80294923a72e2b7c735", "guid": "bfdfe7dc352907fc980b868725387e9824c51163076c8c96c78611917cd1dc98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98939a8a814b59c739e23813de17eb00b5", "guid": "bfdfe7dc352907fc980b868725387e9801b181447883d020411d92431c305571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc4a787ed45ccd57896605722ec207c", "guid": "bfdfe7dc352907fc980b868725387e98ccc265a5f52077e3b1c5c7ba958a6922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0149df5c50fb7c41d4da4f10d85b8d", "guid": "bfdfe7dc352907fc980b868725387e9860185781668f0073f944bada52aff903"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}