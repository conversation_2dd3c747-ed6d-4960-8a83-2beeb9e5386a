{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c58bc453353d992933a50498b7597dae", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c640a381f482dd9de903c2a5d95f35a7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c640a381f482dd9de903c2a5d95f35a7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c8fa02db260a6a8ef8e7dd31a76c509", "guid": "bfdfe7dc352907fc980b868725387e980eb9d23368b95b4f0923cd46a43136d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eadb052c5e184d1d28b55f3b2d4446bb", "guid": "bfdfe7dc352907fc980b868725387e98f457cef344e960dcd5e86a14876a724f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98305599a7bbd849341434f58aeb8d6cb4", "guid": "bfdfe7dc352907fc980b868725387e98448d0ae29de8f3c224c2dcfdec1456de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c87e5444fc3d0d1fd488acaee084659", "guid": "bfdfe7dc352907fc980b868725387e98813b7079aa7de27e6db954b27ad09619", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811d827d4df7412144d613c437ce9fc29", "guid": "bfdfe7dc352907fc980b868725387e981ea8f363f0722babcaed14c60409bf02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d94acbc820d7ab38d88a9566beed40", "guid": "bfdfe7dc352907fc980b868725387e98e7ac745c3ef27b02fac012aa73444821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f19432f927ac01d33cd9b89462d41ef", "guid": "bfdfe7dc352907fc980b868725387e985678ecb94e589405999c12157a408775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981299b60aaa743776bf6db6d52ae0e553", "guid": "bfdfe7dc352907fc980b868725387e98cd87b467ebfeb07dfeb1385499e6cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ed0c5f442fac49a60a1c8d0b2a5b6ce", "guid": "bfdfe7dc352907fc980b868725387e9871c163114fc9cc28ecff5b1b2d0a4de2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98581625882a502a247b985ab2de34fda9", "guid": "bfdfe7dc352907fc980b868725387e981978a187afff967da6262086dc0e6fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675aea28ac29d29d1fe640ae247bb1f3", "guid": "bfdfe7dc352907fc980b868725387e9826c907de3610a0743fa9d834fe3a26ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a94f1895819ffb1bd6b830670036f14", "guid": "bfdfe7dc352907fc980b868725387e980aab54a36f4355218c04e0f9e0c22709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed17f40575f820560d4ddc217ae7f4fa", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1806bcade1d422328aa84640df1b87", "guid": "bfdfe7dc352907fc980b868725387e98d24acab5f9f5227c374c09fc08c9e695", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e28763117ccf54fbd4cdd0c27eaa17c8", "guid": "bfdfe7dc352907fc980b868725387e98eed3f758e63ab4fdfc010074b9de804b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d468808a91e2ffbea879f183aa80a9b8", "guid": "bfdfe7dc352907fc980b868725387e981f08693bd23a6d005b3df15e8e699fde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3275075d07a2d57044f536d72826173", "guid": "bfdfe7dc352907fc980b868725387e98bf1362e319e49dc96debe2ff1acb9025", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a47981b3422e50dbd5fb5d3f5924b536", "guid": "bfdfe7dc352907fc980b868725387e985b2090fddc5dd999ec75f69196ebf8ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e63824510bc489e1a95e8e614f57a035", "guid": "bfdfe7dc352907fc980b868725387e98232fcc544ef53e9da9ad40243b28e5a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985402e0dfe5fc5cb7b25578a9bb27939f", "guid": "bfdfe7dc352907fc980b868725387e98a63597065bd83c36166c1f6aeac7172a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3ab5c0eeb3436fccae2287a0b923488", "guid": "bfdfe7dc352907fc980b868725387e9829db91ca625511289316787d6d10ea44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f47c53650a32a2b87467c2c9bb146f67", "guid": "bfdfe7dc352907fc980b868725387e98c0b06c2b33df0dfa02bfa09f9932011c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98187e8544f4c01e8a399577015f1aa5af", "guid": "bfdfe7dc352907fc980b868725387e9851ba8fa6ddb817e605d2665626d9d641", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c31cf83a5912eb6842dbfd9b263d7151", "guid": "bfdfe7dc352907fc980b868725387e98277468ca209872376e5f206f84332670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccfe601f414c5f728620ebd1451c06b8", "guid": "bfdfe7dc352907fc980b868725387e98e9a6f3c2565cf9a05dd3d353eaa7a4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2d8d7319d07e8a63cded15adf116c7", "guid": "bfdfe7dc352907fc980b868725387e986111772b572857d7961a008265fc355c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d02b6fa88213b88829ad81a699b18836", "guid": "bfdfe7dc352907fc980b868725387e983996f9c15e1fbf682fcc40770fd256d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e03dba70ea886065b34105d4ffd7122", "guid": "bfdfe7dc352907fc980b868725387e9858a8497e775e00416ce705748e767ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827401a316e9d3259e0c8229947ffe4e2", "guid": "bfdfe7dc352907fc980b868725387e98c6bee8141090191321d7771c3a1f978b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc83b6f97052697ad9dcb3d1bdcaa942", "guid": "bfdfe7dc352907fc980b868725387e981cebb2289f1bb2bf7dee62eea371ce0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98210d6d7e49aa67df7454de90924c89a5", "guid": "bfdfe7dc352907fc980b868725387e98f1f6bd36d03eee3c0bdc3bc98e83ea19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806531fc7b6f48e5b97f0a93f4f3d5219", "guid": "bfdfe7dc352907fc980b868725387e98a32f894597a07a2a5504a8b9bcfdabdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855fd54bf07f63bb0f0e163af2afb12d5", "guid": "bfdfe7dc352907fc980b868725387e9800ac44543ad413e32dda38d808c2afef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836154cf5680cb726645bf378ab0849f7", "guid": "bfdfe7dc352907fc980b868725387e980a3b558904dbc56f2153ae21439d6437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98738b1c782e774211e56184ecbbc77716", "guid": "bfdfe7dc352907fc980b868725387e9803ab6e9df07f67a9975e5c61b861ce4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98befb4c6bb023b79361280b9b6ede6b1c", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98217aee706b8fa32b6f8c2d37b37489f3", "guid": "bfdfe7dc352907fc980b868725387e984a502a85d82ab7ae770f404ac3bb3b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd508abdda8d701cbb385c50232c1392", "guid": "bfdfe7dc352907fc980b868725387e984c79dfb7fcb3ff07f9037801d36c7134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981886d2be6510aba314b661040aab388f", "guid": "bfdfe7dc352907fc980b868725387e98832efc148e497d7aa81cf3800da27235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f56af6320ed1e14c9b50a34e1a4db4", "guid": "bfdfe7dc352907fc980b868725387e988b056d871bf720cc83d9b2c7291eec07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98900a13c493c79793648ddecc6d061f7c", "guid": "bfdfe7dc352907fc980b868725387e98afb0626ba205a690e15081763b4adef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e3caca036d43031d9072a3d799763d8", "guid": "bfdfe7dc352907fc980b868725387e98cf6232b3e1482ebfb128206cef66a17c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf6174b9e8a1d6a0d6f9ce16d18d1e93", "guid": "bfdfe7dc352907fc980b868725387e98c76565777e1dccbddf6eca764d227a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bce6ebc93964ccf6a7da379447b982de", "guid": "bfdfe7dc352907fc980b868725387e98dba12151ab617cdd659014f8477b0bfb"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}