{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da144acbc0fb917c525ab3bad9991609", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819f5bd9a5ba60406cdc5480da8ab03ca", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847b8e038e9cfe9b8659da4b067d6cead", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f638875ee4cd364c2be35c3fbb9ee4a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847b8e038e9cfe9b8659da4b067d6cead", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a1ebb7bacbdc8ee64eea5124814cdfe", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bb8b6e00dbb547d6fab898c2a56629d", "guid": "bfdfe7dc352907fc980b868725387e9818a0999c319542de8831d520c225c898", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b98d78d6b63d49385e1d362b648fc51", "guid": "bfdfe7dc352907fc980b868725387e98434067f1cf6001b8629304d18306c81d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989150792adcd65b509726d943070aba7b", "guid": "bfdfe7dc352907fc980b868725387e987b90f55a6cbf65ce6f642bb8dcb412f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3da339f6a509a724645f819dc23f34c", "guid": "bfdfe7dc352907fc980b868725387e989c5f9bacb36abba92102b4ec155c1f6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf8b79d8c0d9a7a55aa2d50682cd058", "guid": "bfdfe7dc352907fc980b868725387e98b269b118392df3fc85ec69a1ee9bdc53", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ccaab96e42f6bcb68f7835edb1ca70c4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d7a1ba11c89fee51a5881d7fda340b26", "guid": "bfdfe7dc352907fc980b868725387e9810369281a0c388bb48299a6d3316ad2d"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9827e97f5803f730599ad3e1bbd7f32ae2", "guid": "bfdfe7dc352907fc980b868725387e9834a48d4f5849ce60cc004aebfddb599d"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fb664a5ffef80fb05d2654ec3fb8444e", "guid": "bfdfe7dc352907fc980b868725387e98ffe837d61dd517d3088b7bdaf765e3e5"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98659fb9cb4c297e6441d330852d995d8e", "guid": "bfdfe7dc352907fc980b868725387e981dfd82cf92754dedac5b4c0e8ac46c31"}], "guid": "bfdfe7dc352907fc980b868725387e98341c9231094a42bdcc4c36b838d118cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98913ffdaad549198ad0fd3cea8bf58291"}], "guid": "bfdfe7dc352907fc980b868725387e9801f556ca04b81a8cf4703ee57b7294c4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fb210a44f3fbcdc80b916ecb5581cec5", "targetReference": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6"}], "guid": "bfdfe7dc352907fc980b868725387e982277a200cddb70b2178afca03f3013c7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6", "name": "nanopb-nanopb_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98edeb236a6bea2a184984d344e4936f7f", "name": "nanopb.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}