{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a9152119bff2c4b4e356e74f15b6a672", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/fluttertoast", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "fluttertoast", "INFOPLIST_FILE": "Target Support Files/fluttertoast/ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "fluttertoast_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a8a400714dc96e7ac449be6681bbd5b0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f15f4c9bde6b274a12897101d324cbc8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/fluttertoast", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "fluttertoast", "INFOPLIST_FILE": "Target Support Files/fluttertoast/ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "fluttertoast_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98eda38af9e9dcb9d5bafbe448b4b6cf3b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f15f4c9bde6b274a12897101d324cbc8", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/fluttertoast", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "fluttertoast", "INFOPLIST_FILE": "Target Support Files/fluttertoast/ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "fluttertoast_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987766fc4a8b069a02f283dfa532b768cb", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984cee64575a69f22a782e19ccbbc5328f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fae1a5079879c46952b1e3fbc4ee3b63", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9875c8c8cabf0447decb700594aa12c449", "guid": "bfdfe7dc352907fc980b868725387e9822d4812df14cccd25a2df813cd34de7d"}], "guid": "bfdfe7dc352907fc980b868725387e983bbb4eb1e14c969fb22b2079b1cc27ba", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985739272bce418ef50bd06c859612bad5", "name": "fluttertoast-fluttertoast_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989d4bb598ca0a92e1d0f3a4ef0157bf7e", "name": "fluttertoast_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}