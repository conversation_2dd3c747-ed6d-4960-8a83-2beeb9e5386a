{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873f02750d10f3560ad2d2d8c74e7b81a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9872235571e52c89e0388ace76aa19e322", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841a0937a1a916ff8569e38bd256a26c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9819093961f464990ae1030b88a1592602", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841a0937a1a916ff8569e38bd256a26c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813164c1b066d94b7538a83b2ac8c8608", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d5b756e294b529041f094e2021d23f7c", "guid": "bfdfe7dc352907fc980b868725387e98b1cb41e33233be6af5d3ffb2cc9292c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2916833ae879433230c979faff9ec5b", "guid": "bfdfe7dc352907fc980b868725387e9882eeffb341a4733c0ed0c1e0d8605681"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815e64f0a8b3e75a0a178dd7bf34974c6", "guid": "bfdfe7dc352907fc980b868725387e98b762c4bf355a9bb394f5157f6f86c2a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987679043fb0d885faac6325464c050dcb", "guid": "bfdfe7dc352907fc980b868725387e98475317979441f654db2557fe09179317"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b77a990a7b7d52f0df93bd751da4bdd0", "guid": "bfdfe7dc352907fc980b868725387e98418858290fd59b5d9f25af70e4f1943e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aaf753911253553308d8f4a698da5e8", "guid": "bfdfe7dc352907fc980b868725387e9863eb27034a18e58f924f64df750bae69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b309b024ed1f0e02d72dbd49218900", "guid": "bfdfe7dc352907fc980b868725387e98f0619497c3b4a0b35983f16d871b9bd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d34baed0933bd257844ec20162efc8", "guid": "bfdfe7dc352907fc980b868725387e98c934ae3788e304a887c4edade1398dc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfae99d04dbf8b5589cf58a80ae76ea4", "guid": "bfdfe7dc352907fc980b868725387e981735336f77ff1412c97e17fc9a5b550d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575752a423d3679045c79333840c4e07", "guid": "bfdfe7dc352907fc980b868725387e98fa4454572657c26742b80d1a820f4a70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4ef951bbc9b16180539a2d2a935bd7c", "guid": "bfdfe7dc352907fc980b868725387e980fb87044933e3494626833f24ea57628", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953c8e43a5b794a6601de5290e34c980", "guid": "bfdfe7dc352907fc980b868725387e989962dec20c05a727431a2f821ac8ebb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc664476f152d050c9949a90b66756f", "guid": "bfdfe7dc352907fc980b868725387e986328f6b29a1e40cb906b3ed4f29c9f91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a45ba82f7025458e3d885d2a664e069f", "guid": "bfdfe7dc352907fc980b868725387e986def43f81fcc7cb7b780ccf2ad560555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6d721ea2e449fca7baaa5ff11f1b10b", "guid": "bfdfe7dc352907fc980b868725387e98bee1932690c7e69d5a4b62a56415ef4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0f38c0a5208ee524b7a6a40ec11d83", "guid": "bfdfe7dc352907fc980b868725387e9849a4cf16e3de0c2df607ba7123cb0e81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98945a3f29169f53943069823297a4d36b", "guid": "bfdfe7dc352907fc980b868725387e9808f7fd82eff7c0e2026cf8143b40952f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ff12722f450d3b3167dcc2d0a7d4283", "guid": "bfdfe7dc352907fc980b868725387e988f3fd0124ffe2f311cb0953386b26622"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed6f341c0b5f487cb919d45bdff6f37", "guid": "bfdfe7dc352907fc980b868725387e983d6ff0ba1fe33b97dd7ac91050a8ade7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e184757462ab2bfc1dcd7a815c509afa", "guid": "bfdfe7dc352907fc980b868725387e98dcb4bd7b830e11e60b10265601de34ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cfa4f77aded0b07475be5840f0de0c0", "guid": "bfdfe7dc352907fc980b868725387e98eb3a2885083f87785c3aa6943d8f33d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986221a65aec518bda3de1f11175c2760a", "guid": "bfdfe7dc352907fc980b868725387e98a46f8fe3304695da3997723d2e49a8c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7db2c2c803798e501c1a5edabba2759", "guid": "bfdfe7dc352907fc980b868725387e989169873df33777ca1b440c3e82e796bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2611d1dd7af81ca388c2f3347f98b83", "guid": "bfdfe7dc352907fc980b868725387e98c9a6de442156cd921135ff2a107d5eb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887345aa17522ca641af2ac8f06f74810", "guid": "bfdfe7dc352907fc980b868725387e98bd8d2cb7c09d010938e9974cb7106b6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168ff0c48c34705253e41a411fe45f02", "guid": "bfdfe7dc352907fc980b868725387e98c1c7ecb9eef68ed09696098018a79a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b76206c75896c5573b472a583667e9c", "guid": "bfdfe7dc352907fc980b868725387e98d96d9c72a58ec1418ab87b700a5bb691"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c8ab10b0571287efbea5e813c74690", "guid": "bfdfe7dc352907fc980b868725387e983600b4730174225ab43746277d08c75f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de4a2448375ca120828c2034e425b60", "guid": "bfdfe7dc352907fc980b868725387e98c2b61fa17a1db40fc8c190a02bb924a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0bd3c8ce453b6472909b5d2d20b41b", "guid": "bfdfe7dc352907fc980b868725387e981f778b84128b3bd75223e2faf58c3846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875d8e738117a6ba7c35627875599188f", "guid": "bfdfe7dc352907fc980b868725387e98cd1cfa07f866684fa232aa992c3d3c44"}], "guid": "bfdfe7dc352907fc980b868725387e98e94a8ce8a2c478586a8bbb039817156e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c99d0aa95ffcf956fbb9b86c6bf2686", "guid": "bfdfe7dc352907fc980b868725387e98a5860cbe8bfeffe308524f3a07a0cd82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98325d3d2bc1be5c5b7b7c37b6d3436b39", "guid": "bfdfe7dc352907fc980b868725387e983f9434fa5d332de25f473fd87ef9c04f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956990b513620d9c9edbede90a889aa7", "guid": "bfdfe7dc352907fc980b868725387e98f2e418bc6975611b1249dc8b379c0b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9a916d307a920c094b8e4c68be49f8e", "guid": "bfdfe7dc352907fc980b868725387e985fd05ccd3c43ed6a9ab10fd1ed0eb4c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a591d3e5f954030595b81bbe3c8cca13", "guid": "bfdfe7dc352907fc980b868725387e98304ed16333a47fef3f4c34d8f9adacf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4bc5c9b3f16b04601cb8facbccd540", "guid": "bfdfe7dc352907fc980b868725387e98875d4dae0678fa07db9f0f8421eb3cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44fa354af2f43e49f80ffce307aa578", "guid": "bfdfe7dc352907fc980b868725387e98a3616d3cc74294040b3b386e120df048"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cfacae74a2ded1be66424d08ca730e4", "guid": "bfdfe7dc352907fc980b868725387e984d04487b5c34e8ce43de0b74a3f2d4a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af281ede596ed8ce73be59ae95f4857f", "guid": "bfdfe7dc352907fc980b868725387e987812f4c2a73c8af884fd7b87f85e02d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988230cedd59e7e66fccc24fd95b9b7607", "guid": "bfdfe7dc352907fc980b868725387e98bbb04141ad992db4fbb42b321e71294f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980762aa9682130a2e1419ec67ad935263", "guid": "bfdfe7dc352907fc980b868725387e988d023c14c3e9c583baab9f153811bce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98985b596eb5200675190e2a76b8dac396", "guid": "bfdfe7dc352907fc980b868725387e98b8cfd6ce41980346af201cf72a58dbd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbbbad90e64137426d5d7750c7b72f42", "guid": "bfdfe7dc352907fc980b868725387e98a992fa2d0607ae87a603c929c5b9ff21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6d4700a39e9b69e49607347785f847", "guid": "bfdfe7dc352907fc980b868725387e98d3b7a346c12a3035c486aeb4c9996d84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cff54788d6265472d6cb1786c224372", "guid": "bfdfe7dc352907fc980b868725387e98549efa6bdb3e202a00a42db905fcb97d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809f8e4a0d4d04be26659083884b0b7d7", "guid": "bfdfe7dc352907fc980b868725387e9862af5577f471557a07b25dfb3957bcb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dec5b5834a3d3cc87ab200dfa53fc73d", "guid": "bfdfe7dc352907fc980b868725387e98870092d775bc2e1e79f93e5acfe5b28c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd664d520c703f142867765abee30528", "guid": "bfdfe7dc352907fc980b868725387e986ece0e84eea0ccf47e3fc64f0bd76d3a"}], "guid": "bfdfe7dc352907fc980b868725387e9815328980d714e422c550b32b7a40525f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98e5039239747a07d13fc8bb32a794daa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e987ca128d692ed74c712b98e805aa3b697"}], "guid": "bfdfe7dc352907fc980b868725387e982b2729ea48b8f936ce08440922f8be92", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987acd385d9e53f465848b93ab0f1c2050", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98b87c3aa1a36017cab955100a3b617e17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}