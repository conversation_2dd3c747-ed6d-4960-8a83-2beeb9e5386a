{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8ce0be61e4efd8e42cd72e954681201", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f29791f936ffeeab5cd08f39304798a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f29791f936ffeeab5cd08f39304798a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f852684a897456bf16028340db9b93da", "guid": "bfdfe7dc352907fc980b868725387e98fde1266a3cbeb2387eca315a29b956b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f695aebffd6436bc44345fbc4f1b30f2", "guid": "bfdfe7dc352907fc980b868725387e980d81ce22815e581f738b740b9be82df3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984015ee10aae9a46e0c5625124868300d", "guid": "bfdfe7dc352907fc980b868725387e98d03928281fde63c866e3c5f56d8aebf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c591d96bcb4f0840319f2f1a88c8906", "guid": "bfdfe7dc352907fc980b868725387e9880608433b3bc911542e75846113f96df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99b46125c519d7af740a919a3f19338", "guid": "bfdfe7dc352907fc980b868725387e98e703d60fcb6c658f7f8b1d083ae20127", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898a417bf8655ecf747836f065cdda968", "guid": "bfdfe7dc352907fc980b868725387e982fabfb02ffaefc1653ee320b4642a5b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12b6ca932b101a4ece7502734d087f3", "guid": "bfdfe7dc352907fc980b868725387e985f5e9e45104e687a83d73f75841899f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b56568542947e2f7b2ed1eb700d052", "guid": "bfdfe7dc352907fc980b868725387e9846660565bff3b5d3a041d625367b2492", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e2b43a18ea01ff11ec4ab90a9e60200", "guid": "bfdfe7dc352907fc980b868725387e98e61f79a3f9820b38d5ee7926a024c985", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc1a549883b34b2a781006e0a010eea", "guid": "bfdfe7dc352907fc980b868725387e98896c25c4c6d0fb8324dc0b4fee8cf7a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f43eb09f052c9a60642466549cab402d", "guid": "bfdfe7dc352907fc980b868725387e98faf5377cb2109b12d99b5ee0a70d4f65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb432c5a63382b62cd589b340c63f26", "guid": "bfdfe7dc352907fc980b868725387e98ecc1104824c844dcd0de6da330bf59d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f65b03b4a70d678421257b35abab725a", "guid": "bfdfe7dc352907fc980b868725387e98e3c0ad036e69201060ac06170d38f509", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b2a70c119d71234ff88a75486b871d", "guid": "bfdfe7dc352907fc980b868725387e986d1ea00d8c42512d43efff03ea163539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a87c563f81d8343f768a6b26fabd441", "guid": "bfdfe7dc352907fc980b868725387e983ff251c5640764c60e3b4ebe291dd35b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ab1a45cd9472b863f3d4b06f87e175", "guid": "bfdfe7dc352907fc980b868725387e98e1c2d823b21f2fbc22110e24493b628b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053c43df0497bbd2b4e92efeeecdd37e", "guid": "bfdfe7dc352907fc980b868725387e9819a58a3d592bd8a3c83f9588222cea4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48078f6e2707f30a75a3a4edbace3f4", "guid": "bfdfe7dc352907fc980b868725387e988536fcaf720f25055c5cf22f2f7e16e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98445d0b66d5ac2bad24c245615b5d854e", "guid": "bfdfe7dc352907fc980b868725387e98fb87df7b9bca68c93c8e6bb6b986cc70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbdc8b0689403fef0d01dfe20329e9ab", "guid": "bfdfe7dc352907fc980b868725387e98efd4640328b24ccefec8bf72ce2349c9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9dffb0b622e42037a04114d4b31f35d", "guid": "bfdfe7dc352907fc980b868725387e987795a216303c82397a5e1dd33ad30da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adaaa2e28002e554cb2132301cf0e28b", "guid": "bfdfe7dc352907fc980b868725387e986785d17e53ba24c8e7e20eb0925e5ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98871769aa28781c3c2d06ce2eea49da61", "guid": "bfdfe7dc352907fc980b868725387e98e93d81db85c5b67850e534c0cec7b059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c09e1d35d7544beee1442803278950", "guid": "bfdfe7dc352907fc980b868725387e989995f411a0f5cbcc8864563b4260b363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98032ffdb6c09cc92936e396d0309ea062", "guid": "bfdfe7dc352907fc980b868725387e9804d72ed45f5eac089558baf7c29443db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be27c55d57845bc3567b6eaf32c52220", "guid": "bfdfe7dc352907fc980b868725387e98bea62292c81ebd34b3b77676394ebb03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f02516fe054273a577d96d60e1a29b3", "guid": "bfdfe7dc352907fc980b868725387e980e4457cbe7d07be6b2e63d86b6f30038"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998923308aff4eed59ff111a3a77fe7a", "guid": "bfdfe7dc352907fc980b868725387e98c559b262c99b95388db3cc0188fe3e9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98897808daf80a384d70a41ce51cfb85e7", "guid": "bfdfe7dc352907fc980b868725387e988f1c2f9f38a8971a44696694f473d7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cc314f3943b6cb606b1840f78e4aaa7", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983da4632bc893a0180b07d8b021d21006", "guid": "bfdfe7dc352907fc980b868725387e987446a53c9beed3d99946077648e987de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98423799a36e80742dfaa2cff11e29c860", "guid": "bfdfe7dc352907fc980b868725387e98294182e6f59862494590317cc9296551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0d490f88d1fdab651bab02f39675007", "guid": "bfdfe7dc352907fc980b868725387e985debc93a15d8af7fefc9cc082a52aac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a9c2e3a0b18c84fd825c739fb873afd", "guid": "bfdfe7dc352907fc980b868725387e985af1746bca85c672d9cad0aa2be3b903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98097ce78b54106834bade951aa50750ef", "guid": "bfdfe7dc352907fc980b868725387e989d80a9ddd977023ba19a6998d0e831a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a6e23f9b78ba8e2ff80b7713d67795", "guid": "bfdfe7dc352907fc980b868725387e9807d0f98efefea15ab6e0dd5bf094b4f3"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}