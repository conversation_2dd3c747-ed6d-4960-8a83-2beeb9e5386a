{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e893c0cd893b6c71ba58edb74fe09961", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e19228be12252edf38dd6ca51b6df219", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d6ad1314078883ec71ce7be6740730b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98641391e496a4a37f3ef5b39b0f291421", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d6ad1314078883ec71ce7be6740730b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cab47b6636e5c33377ce749e179bf40d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ec9f2c092076c38c20dc9d90f46faf21", "guid": "bfdfe7dc352907fc980b868725387e98a3aecb2067b7ea581e8574efa04b567d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b697b8794c2286cedcef098345e436b9", "guid": "bfdfe7dc352907fc980b868725387e981ea29f833aa0dda8bc7ab2bb78e6c0b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaddd9d90ce28dd0388f1505ba42f427", "guid": "bfdfe7dc352907fc980b868725387e9876175a44f0183a52ef7bf6b805440dc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba5ec1d134add38e3ce7ed3f53299c0d", "guid": "bfdfe7dc352907fc980b868725387e985c1dfba3bad6a462658b3c17a9fb686e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b72ac491362127507f124632e0133a4b", "guid": "bfdfe7dc352907fc980b868725387e989bf2af2c6f69b0d9f9a5931a424cb2cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858ff8226972f14d589c6e06c0373009d", "guid": "bfdfe7dc352907fc980b868725387e988d2be310926bf8da28ec061ff92f3751", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32ef6dd3c342135177b213d0a46773f", "guid": "bfdfe7dc352907fc980b868725387e98d20cef43c498c94273e0bb84111ec129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d05ca28c7e679570667214845d6ac4", "guid": "bfdfe7dc352907fc980b868725387e98d76a51d826cde3235da18f333ae701e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b445ed5b8a01cb95aae57718bb95cb5d", "guid": "bfdfe7dc352907fc980b868725387e98f5583ad7a34dbaa73a77e2aed9f54590", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d9e6722a2e17c77a571240ad53675a5", "guid": "bfdfe7dc352907fc980b868725387e98cab18d408301c355b585974b70da1f62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894146942f1d2a5b94143c24302e9fd36", "guid": "bfdfe7dc352907fc980b868725387e98fea294816f2513e1be22924921b9ca99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883010b2c710db394fd7416630d675ee8", "guid": "bfdfe7dc352907fc980b868725387e9838fdad7d7a0297327e6ecb76edfdfe09", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2dc82ab8ed911bd330f7f2eae217f7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f90ebc5136555416e6002414278941e2", "guid": "bfdfe7dc352907fc980b868725387e98f725d4e8e04b01e297b4f883c6405f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a467dd5cc8b8529f2929372a1f6b3ed", "guid": "bfdfe7dc352907fc980b868725387e989993350d40fd5c55e738a25c0b1ad91a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98383e550fede8f51915ecfdcf9d7d3887", "guid": "bfdfe7dc352907fc980b868725387e98eaa7a79ec21127a14401648afdcf52db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e8fba5db0892557fb6eceb5e284a3e3", "guid": "bfdfe7dc352907fc980b868725387e98f5a376592f0f7179ae5ec7a2e73ae3dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8e957c1b035d733dd9864d63fac9cc", "guid": "bfdfe7dc352907fc980b868725387e9882ed80b0c2b6253388aa32bd556834e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982108008066df81819284c60ad347217f", "guid": "bfdfe7dc352907fc980b868725387e98168fb07d7e182b25b8ee3097ec37a5d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98510cdb1ebc2a7fcc56c5a19ac572510d", "guid": "bfdfe7dc352907fc980b868725387e98d6829ea6b179a80872afa36b474ab699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c696ecd358dcd39de5bec35f0c0950a", "guid": "bfdfe7dc352907fc980b868725387e984b574aeaee4bf692cc947955032e953b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c303191cf3e577cdc163f01599fb9e", "guid": "bfdfe7dc352907fc980b868725387e984ad2a419b86bd1cc394ad9e66d992347"}], "guid": "bfdfe7dc352907fc980b868725387e98da8caa0468a6494c539bcc9a3f56496f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980d562db8451c55dccc31ac9670df4bb5"}], "guid": "bfdfe7dc352907fc980b868725387e989c121c0173be5443faa5b2120d803293", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c44defa1c51e6619c9bc20d8804a8b84", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e984c8ab30283e75361b6a65cb914f55e53", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}