{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987cbec203dc6d742c901dfd2a9f8dd5c8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d127321b73c635803b779b66d5b18f0e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98db539bbd9760e485f2e922d584714638", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989401ea074435b012a4b145925f990f79", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98db539bbd9760e485f2e922d584714638", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e4d96bfd8ca35a22b96462c5b41704b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984db987e0650c9e9f9b2e0be4394bf003", "guid": "bfdfe7dc352907fc980b868725387e98b719506dabb13e9052b6226f5bb7e58a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f73acd49e79a8bab61b538815a88de12", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e3c2f6e2510fd88686c62e678feb9689", "guid": "bfdfe7dc352907fc980b868725387e983de95e026583ff06f20f6afb5788be18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98465bf5f0145bec7aaca33435f3a31159", "guid": "bfdfe7dc352907fc980b868725387e987077c807e34aa7710139ee470449dd30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98367f418d9a7edaee13c0ad174d4f2050", "guid": "bfdfe7dc352907fc980b868725387e98c07a0156566c0ae717bc4949f105c126"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac72ecdf90f082e9e157a8dfe93cc839", "guid": "bfdfe7dc352907fc980b868725387e98557d7fad078a4430144ed3f65716515e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f285c933e39492b2b0500bf2591e85cd", "guid": "bfdfe7dc352907fc980b868725387e986c110d14915c06b26c3834ffd77b8708"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262589f32d06886f54b78315b81f5783", "guid": "bfdfe7dc352907fc980b868725387e984608f96c82738632eb8ca5e6d05bec44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b9a77585221a3844361d21a0b0381d3", "guid": "bfdfe7dc352907fc980b868725387e986a5bb1e1f620667751c1d726e2d2e6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b9c918ef5f07f380b877421f795176f", "guid": "bfdfe7dc352907fc980b868725387e98e0556df360d3cf7edb40c4bb08d84aeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d81e73e320c73ddec1166e2fae54307", "guid": "bfdfe7dc352907fc980b868725387e980c28809487a082a2c6f08d4801e98bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866904a458d1cf6cb1df4c52a43ea55cb", "guid": "bfdfe7dc352907fc980b868725387e985e3a3c009f9404036d58267a1f0b2177"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7877dab8818a0d6ffa38a7dd6b4c86", "guid": "bfdfe7dc352907fc980b868725387e985ac7c6db8ded701185436642dea6ea0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f739607467f56e11000230f060a7b752", "guid": "bfdfe7dc352907fc980b868725387e9850463abca80f37ef842c4814b267f6b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ecfdde741245846e7bbce40fc6ec644", "guid": "bfdfe7dc352907fc980b868725387e982cdaafcb25b467208a803fa8b4a5af00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c274ec753aa916b5ab1ac50be541072d", "guid": "bfdfe7dc352907fc980b868725387e984b1d11a5c18dfedf1e5932151c090ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5110fe1bb3f365103a94cb188fc1e9c", "guid": "bfdfe7dc352907fc980b868725387e98d063a93d8584e5c8c1c845bfbcb9263d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a173512e51029474b8081592b0fc236b", "guid": "bfdfe7dc352907fc980b868725387e988291b0c3a5c19181c83600cc56a12f79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98165b9e01aaaf54ff867c00b4d0e57389", "guid": "bfdfe7dc352907fc980b868725387e984f17fe99ead24773b84be4869ea3ef26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b4acdec4de6f6dfb604ab38416e9db", "guid": "bfdfe7dc352907fc980b868725387e98a315b4c58956a09bab01faf4430b00ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5eb199789691a47d235079ab27e19e3", "guid": "bfdfe7dc352907fc980b868725387e98a1d52444556f70c9dc2c153a42c1bbe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e3859789e2f92cecd9564d759ca6121", "guid": "bfdfe7dc352907fc980b868725387e980d431698327f76e047ab5d7618041e89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4bfe353e21c966906a74c08e552d454", "guid": "bfdfe7dc352907fc980b868725387e98a1537146a16ad408b9ced3f103a113d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce79bec5ab60fb0d465a268cb6aafef", "guid": "bfdfe7dc352907fc980b868725387e98651e4a12f1d63bcf12cd4086ec2528cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbeaa33f931d3835a7e30eb18d8ea4e0", "guid": "bfdfe7dc352907fc980b868725387e98242c2199913c1f00a451e54bc730f40b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98466d8bb61326f0a31c4e6f81519075ca", "guid": "bfdfe7dc352907fc980b868725387e98af7248fd9c84b9070e05c62e893f52d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e38d610cf54cf9b995cde801ccf445e", "guid": "bfdfe7dc352907fc980b868725387e98ea904e3521c2d504b1d0f106b54b003a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981720e139e97a3fea4086d7bee91d0864", "guid": "bfdfe7dc352907fc980b868725387e98ced7c5471268740a663bcf45c0f9dced"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987febab73a57ba105a3550470358c8069", "guid": "bfdfe7dc352907fc980b868725387e98cb752ab5379a9b0111fe0a8d1c86bc36"}], "guid": "bfdfe7dc352907fc980b868725387e9893662e77f36c518c93d04e98f4022bb2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98709db05cbcba8a38b5fff878acd3dc96", "guid": "bfdfe7dc352907fc980b868725387e9833f100dc535f5843033fce76d5b48088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986296ed536311979929249249546219ab", "guid": "bfdfe7dc352907fc980b868725387e98c0c721400f7d3528dc1853e9b6b9cbc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e989a8361418b8fb2b85d66be7a4fd7c4f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee0e87b873ad96ad0a06a562ccbc5f5", "guid": "bfdfe7dc352907fc980b868725387e98e7e41ad7ef7b8a96f2ba42c79c3d32e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e980c8c784ffd13c37b6675a3c60bdf8d35"}], "guid": "bfdfe7dc352907fc980b868725387e987d02bc2d7a90df49b4b7662e6068e08c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983f7e73b0031145041fad9ff3f1c06c53", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e983551f6a44a35bdc2744d0956251e5789", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}