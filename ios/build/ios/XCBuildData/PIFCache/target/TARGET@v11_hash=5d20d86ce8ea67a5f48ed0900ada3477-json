{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98431ff223a0e4fd3ad5a2a1e2cd06349f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827114570ef4f753ecb5b153f73415f1e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879596656238bbd9ac247a756e94c0206", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f821bfab11464fab03eddabad3bf0d04", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879596656238bbd9ac247a756e94c0206", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846e7211b058ca702480892af1e09aa83", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b31a01e1edb2f7af2d7ded2d6fa7339e", "guid": "bfdfe7dc352907fc980b868725387e9874d26eae5fde93fff12a1a06e58ee948", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98272632e06f9f5b4c13fcd217a2c7b096", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980167bbcfcfe59020cd033b5b14208669", "guid": "bfdfe7dc352907fc980b868725387e98862b3ce13c8236adb8b8c2dbd80a51d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9dd67ab0c13c7fda2470ecf47fadc50", "guid": "bfdfe7dc352907fc980b868725387e980a20d1ff4a60ed2ed677fee8a8f32b7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a05da87a6f3fcf32e792f9c9860b58", "guid": "bfdfe7dc352907fc980b868725387e98ae464dcbefa03d13ffae00886bb72816"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ceb7794a85e7bcca093768cea8872d8", "guid": "bfdfe7dc352907fc980b868725387e980c4ed958043c85a4ad4f7a805e59679e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ceea3eddaddf1c5ee5016acdb1eebf1", "guid": "bfdfe7dc352907fc980b868725387e98490d315d725cbf8eea833f5d0b32d77e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb660a164c32205cb7f9b2db547057d1", "guid": "bfdfe7dc352907fc980b868725387e98edceb6bf475f4afcf2b2506c515d5215"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862e3d5e096da7a6adbb2ea1e663c9ada", "guid": "bfdfe7dc352907fc980b868725387e98c9dcf0badaa5f8d86351013e23a45d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825676364f8ab0cb8d782563841da113c", "guid": "bfdfe7dc352907fc980b868725387e98523f7268e132499fff2df1274c169c65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d8da6fb618d31b62f6a1c24015ce68", "guid": "bfdfe7dc352907fc980b868725387e98f67ff294ebae488d3e6da83c8ac104d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0b8b09177006b81b37cb754a6d2a42", "guid": "bfdfe7dc352907fc980b868725387e98f114d4a128882d50a03edb466b1ecf67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865c14c9ec4a3aef64c1815dec06315f0", "guid": "bfdfe7dc352907fc980b868725387e98d4e33a8ea13b2a7c984d843862e0b422"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986997e3742c6b8b093d42ad42ba60efe1", "guid": "bfdfe7dc352907fc980b868725387e98272e341e993b0b64468ee2c54914e93d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344eff3b634b1d4d806c35ae9167d244", "guid": "bfdfe7dc352907fc980b868725387e987b82a2a461f1934100cb48a1182ae746"}], "guid": "bfdfe7dc352907fc980b868725387e98ba40bb926cdea1772c73dc17b43b4caa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e983a7b8f64609a7e6ebc5ed06d8b95c5e8"}], "guid": "bfdfe7dc352907fc980b868725387e98bf18fb5361133b58cbfc06d707b014b2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987e018342181216148c025832f9ea8195", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9876dcc67574b51daabed340fc83ec2639", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}