{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fbc3ab90c264db73838ed56121dcca6d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983fa900cdcea9f41e23fab0d52c9ae700", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed23bf18c1ac9542c9ee4c5c0be19909", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865213897d461e463f575cfed52c439b6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed23bf18c1ac9542c9ee4c5c0be19909", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982535a2ac4c3ac3c037edf916636b8db3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983df6373e7651461575a5fcc1ad06872b", "guid": "bfdfe7dc352907fc980b868725387e988c960173ec850db60a7c5917b24c02dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebf425209219a17d10c30bce5784cb65", "guid": "bfdfe7dc352907fc980b868725387e98af41237c44ce8c2c89fa34c6be59391c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b034260cda24618b82809fd038162c", "guid": "bfdfe7dc352907fc980b868725387e98e84b825f976b6998db99368812f50178", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a2276341365f8ffc358a503aced5b77", "guid": "bfdfe7dc352907fc980b868725387e98ab4027b293284144e6504f7fefc45c00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9154475edbc7d9944d004fa96725729", "guid": "bfdfe7dc352907fc980b868725387e98dea43e7427ec6adc009f7a8267856525", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876bcb54021e20fc5c934a4aeea8f3a0c", "guid": "bfdfe7dc352907fc980b868725387e980566fd93f29a660340fc467a1220d1cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db42b855bf4f13c6a2f79e1f04212dce", "guid": "bfdfe7dc352907fc980b868725387e98d7b8cf674f7ee413a175f87d3fcf9ee8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89b47c598df7f454cb851608e8443c6", "guid": "bfdfe7dc352907fc980b868725387e98c3a53953e7dbab85fc91e7a3e7a074d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad84089890c49d8748de93e62a0b0399", "guid": "bfdfe7dc352907fc980b868725387e980254a047703b888c0d29e694d70b75db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616ab7cf762895cdba44fbd35c76f4c3", "guid": "bfdfe7dc352907fc980b868725387e980c83a4fa29253bdcb3e7f05a14f57856", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815ce79a926d8ceb636517d4bbbfae537", "guid": "bfdfe7dc352907fc980b868725387e981933f3e1cfeea45f6f2c0c80587696a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98332cd21da5c9a5fe4bf8dbcd3eaf99fc", "guid": "bfdfe7dc352907fc980b868725387e98d9d3d59403e12115ad3df32c5a1bbfe8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4da22cb9113becfa412532564666e8", "guid": "bfdfe7dc352907fc980b868725387e9853ab155c9ac3c50dd56baf9ae48282cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b929479c90000956ac128141a3faec27", "guid": "bfdfe7dc352907fc980b868725387e98787f3ebd6586ff3c0ccd5eea6e6faf51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10acabb646a3bf143834d4af1c98034", "guid": "bfdfe7dc352907fc980b868725387e98851bf84802d475daff03519a680133b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee75b7a83d17ecde46e58849d07a51c", "guid": "bfdfe7dc352907fc980b868725387e985dee75dbf9069aaebfae28596a8d11f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98608ca4e5763f94a5960170c364248599", "guid": "bfdfe7dc352907fc980b868725387e98caa3e16748f2c4456a7a4d5c6182d1d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e28be492371dc32e5fd4988981afe2", "guid": "bfdfe7dc352907fc980b868725387e98709714c659dd6a165441f27c39b6a3c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb02649f2299738ffb311297354f86d3", "guid": "bfdfe7dc352907fc980b868725387e9859e4b59037a89404d3ed23d1de17011d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ece3c73f061cd9993a1f05b708e4856", "guid": "bfdfe7dc352907fc980b868725387e988f7b6dd1e9288137f320e921ae623648", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803bb09698e5383399bc185ceffac95d1", "guid": "bfdfe7dc352907fc980b868725387e9841321173d2b480a625f1e680f8a01ba3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989990703a0e3bb943ff63facbd7760106", "guid": "bfdfe7dc352907fc980b868725387e98a52a7d2fec31a5065259090bc44f9e3c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985fb2de23c334e9a138ccde3f14b7ac0d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dbbc6cce92ad72b00ed70fe9d4e980e0", "guid": "bfdfe7dc352907fc980b868725387e98e1f0629c6d331d1318404f742a6239f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989827ea7c8ecf08f1a3ef0247eaf33b4a", "guid": "bfdfe7dc352907fc980b868725387e980db79bc5a6010678e3659c2629cc873e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5213ee8a10bc6a24b0f1cdd460e2321", "guid": "bfdfe7dc352907fc980b868725387e98e9196696e694f2b9f29295163449550c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c828c8585bf09755a0f73d98a035997", "guid": "bfdfe7dc352907fc980b868725387e98d624af17afd6c93b907a93fb9f1e5586"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d822e8f5524e92853a7c34d3dd3c95", "guid": "bfdfe7dc352907fc980b868725387e984e5543963d96189e6dd61d8958d4a7c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca658067705d51e51501cc1272a73eb", "guid": "bfdfe7dc352907fc980b868725387e984360d9397688db52b877b3a54ff55315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd7e87039b3198f856ca51e81e4ac9bc", "guid": "bfdfe7dc352907fc980b868725387e98936116c2fc4dbdc3a54c689e9dc09745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8248789a365bb154402c91f50fca8ee", "guid": "bfdfe7dc352907fc980b868725387e980048f7607c20076a0e91730c87ce11a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f273fe391c2b6e7a7195048a60542bd", "guid": "bfdfe7dc352907fc980b868725387e98701f162f612029b97b64c576b5ce6df7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8559c20fe654f6833a5c34ad198063", "guid": "bfdfe7dc352907fc980b868725387e988c695d0173023d185794006b63435a19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ed1c67d114993e1545b8661a090786", "guid": "bfdfe7dc352907fc980b868725387e9873760bd3b6409a1cf6d44a7af17fe0da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801dcfc710f725bb878a0d9dd776fb8f7", "guid": "bfdfe7dc352907fc980b868725387e9844a30d7534dd318f71776f02dd3614a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d35f7b97235fbe3c9c90d1e6b241f2", "guid": "bfdfe7dc352907fc980b868725387e98ad8cd72b57b299231d38033e99d115bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984302c1ed817f88a394df3e24661f6273", "guid": "bfdfe7dc352907fc980b868725387e98158e28e3af28c43f792393c0f2e809c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984119d3655ad2b3b169d979c83fc32cf6", "guid": "bfdfe7dc352907fc980b868725387e989532dbea7aae11328dc97ca104e9cad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243fa3f7a3f3e64ffa2666cfe80f1cb5", "guid": "bfdfe7dc352907fc980b868725387e98fb3dd93582abeeb8f1d1e06a2eaa54cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d9cfe05331648a0cb2b9d88969b51e3", "guid": "bfdfe7dc352907fc980b868725387e9858c81d0fdb3a4f0b7bed9fa83c88570b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49851085200c3eee8b57e0d786fd5b4", "guid": "bfdfe7dc352907fc980b868725387e984e689f22fd55598a37b05d5e7b3ffb09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c045196f84c2fc87b92b31bb01e24d", "guid": "bfdfe7dc352907fc980b868725387e98d18bc6102a8a0b02353e65f397b160c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3fd27e61ada37056010b7b65f830a37", "guid": "bfdfe7dc352907fc980b868725387e98f2840f32a70e13a80462a8ee61774638"}], "guid": "bfdfe7dc352907fc980b868725387e9840af6f9851c3b07346285d80092c9f61", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9821a39b5bfcd2b7b356cb2a6a6c7dd2c3"}], "guid": "bfdfe7dc352907fc980b868725387e982f3f3491ebee763a97dbb9f88d1ae4ff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988eeea49134b7bc023b04073fbf33db8b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98c2c722882483171da7f099f8cc413bab", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}