<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>A<PERSON></string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>com.cybertrons.akobasma1</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Ako Basma</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<!-- Permission Usage Descriptions (required by various plugins such as geolocator, image_picker, permission_handler, etc.) -->
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This app needs access to your location to show where you are on the map and improve task accuracy.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>This app needs access to your location even in the background to keep your location up to date.</string>
		<key>NSCameraUsageDescription</key>
		<string>This app uses the camera so you can capture images and upload them.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This app needs photo library access so you can select existing images and save downloads.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>The microphone is required when recording videos from the camera.</string>
		<key>NSUserTrackingUsageDescription</key>
		<string>Your identifier will be used to deliver a better and personalised experience.</string>
		<!-- Required for Google Maps / any other PlatformView implementations used by Flutter on iOS -->
		<key>io.flutter.embedded_views_preview</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
	</dict>
</plist>
