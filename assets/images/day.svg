<svg width="40" height="20" viewBox="0 0 40 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1414_588)">
<rect x="40" y="20" width="40" height="20" rx="10" transform="rotate(-180 40 20)" fill="#E6F4F8"/>
<g filter="url(#filter0_f_1414_588)">
<circle cx="20" cy="10.8571" r="14.8571" transform="rotate(-180 20 10.8571)" fill="#8ACCE0"/>
</g>
<g filter="url(#filter1_f_1414_588)">
<circle cx="26" cy="10.8572" r="14" transform="rotate(-180 26 10.8572)" fill="#54B5D2"/>
</g>
<g filter="url(#filter2_f_1414_588)">
<circle cx="32.8572" cy="10.8572" r="14" transform="rotate(-180 32.8572 10.8572)" fill="#B0DDEA"/>
</g>
<circle cx="35.0003" cy="15.8577" r="5.85714" transform="rotate(-180 35.0003 15.8577)" fill="#E6F4F8"/>
<ellipse cx="23.1431" cy="21.0008" rx="7.71429" ry="7.57143" transform="rotate(-180 23.1431 21.0008)" fill="#353638" fill-opacity="0.08"/>
<ellipse cx="14.4283" cy="18.8577" rx="6.71429" ry="6.85714" transform="rotate(-180 14.4283 18.8577)" fill="#353638" fill-opacity="0.08"/>
<circle cx="32.0001" cy="16.2863" r="5.42857" transform="rotate(-180 32.0001 16.2863)" fill="#EDEDED"/>
<circle cx="22.8569" cy="19.9994" r="6.28571" transform="rotate(-180 22.8569 19.9994)" fill="#EDEDED"/>
<circle cx="12.2857" cy="19.2857" r="6.28571" transform="rotate(-180 12.2857 19.2857)" fill="#EDEDED"/>
<circle cx="6.28557" cy="25.7143" r="6.28571" transform="rotate(-180 6.28557 25.7143)" fill="#EDEDED"/>
</g>
<defs>
<filter id="filter0_f_1414_588" x="-1.7142" y="-10.8571" width="43.4285" height="43.4285" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.42857" result="effect1_foregroundBlur_1414_588"/>
</filter>
<filter id="filter1_f_1414_588" x="5.14286" y="-9.99996" width="41.7143" height="41.7143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.42857" result="effect1_foregroundBlur_1414_588"/>
</filter>
<filter id="filter2_f_1414_588" x="12" y="-9.99996" width="41.7143" height="41.7143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.42857" result="effect1_foregroundBlur_1414_588"/>
</filter>
<clipPath id="clip0_1414_588">
<rect x="40" y="20" width="40" height="20" rx="10" transform="rotate(-180 40 20)" fill="white"/>
</clipPath>
</defs>
</svg>
