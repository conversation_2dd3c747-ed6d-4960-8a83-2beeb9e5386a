<svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-1.42857" y="-2.42857" width="21.7143" height="21.7143"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.71px);clip-path:url(#bgblur_0_1414_195_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_dd_1414_195)" data-figma-bg-blur-radius="3.42857">
<circle cx="9.42861" cy="8.42861" r="7.42857" transform="rotate(-180 9.42861 8.42861)" fill="url(#paint0_linear_1414_195)"/>
</g>
<defs>
<filter id="filter0_dd_1414_195" x="-1.42857" y="-2.42857" width="21.7143" height="21.7143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.285714" dy="0.285714"/>
<feGaussianBlur stdDeviation="0.428571"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.47451 0 0 0 0 0.643137 0 0 0 0 0.992157 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1414_195"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.285714" dy="-0.285714"/>
<feGaussianBlur stdDeviation="0.285714"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.639216 0 0 0 0 0.760784 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1414_195" result="effect2_dropShadow_1414_195"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1414_195" result="shape"/>
</filter>
<clipPath id="bgblur_0_1414_195_clip_path" transform="translate(1.42857 2.42857)"><circle cx="9.42861" cy="8.42861" r="7.42857" transform="rotate(-180 9.42861 8.42861)"/>
</clipPath><linearGradient id="paint0_linear_1414_195" x1="4.14289" y1="2.57146" x2="15.2857" y2="14.8572" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD338"/>
<stop offset="1" stop-color="#FF9D42"/>
</linearGradient>
</defs>
</svg>
