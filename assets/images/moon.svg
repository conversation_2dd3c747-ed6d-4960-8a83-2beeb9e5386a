<svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-1.42857" y="-2.42857" width="21.7143" height="21.7143"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.71px);clip-path:url(#bgblur_0_1414_194_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_dd_1414_194)" data-figma-bg-blur-radius="3.42857">
<circle cx="9.42861" cy="8.42861" r="7.42857" transform="rotate(-180 9.42861 8.42861)" fill="url(#paint0_linear_1414_194)"/>
</g>
<g filter="url(#filter1_i_1414_194)">
<circle cx="10.5714" cy="12.8568" r="1.71429" transform="rotate(-180 10.5714 12.8568)" fill="#E6F4F8"/>
</g>
<g filter="url(#filter2_i_1414_194)">
<circle cx="11.8571" cy="7.85671" r="2.42857" transform="rotate(-180 11.8571 7.85671)" fill="#E6F4F8"/>
</g>
<g filter="url(#filter3_i_1414_194)">
<circle cx="7.14279" cy="6.28537" r="1.14286" transform="rotate(-180 7.14279 6.28537)" fill="#E6F4F8"/>
</g>
<defs>
<filter id="filter0_dd_1414_194" x="-1.42857" y="-2.42857" width="21.7143" height="21.7143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.285714" dy="0.285714"/>
<feGaussianBlur stdDeviation="0.428571"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.196078 0 0 0 0 0.592157 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1414_194"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.285714" dy="-0.285714"/>
<feGaussianBlur stdDeviation="0.285714"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.137255 0 0 0 0 0.137255 0 0 0 0 0.341176 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1414_194" result="effect2_dropShadow_1414_194"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1414_194" result="shape"/>
</filter>
<clipPath id="bgblur_0_1414_194_clip_path" transform="translate(1.42857 2.42857)"><circle cx="9.42861" cy="8.42861" r="7.42857" transform="rotate(-180 9.42861 8.42861)"/>
</clipPath><filter id="filter1_i_1414_194" x="8.85718" y="11.1425" width="3.71418" height="3.7143" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.285714" dy="0.285714"/>
<feGaussianBlur stdDeviation="0.571429"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.823529 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1414_194"/>
</filter>
<filter id="filter2_i_1414_194" x="9.42847" y="5.4281" width="5.14289" height="5.14289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.285714" dy="0.285714"/>
<feGaussianBlur stdDeviation="0.571429"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.823529 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1414_194"/>
</filter>
<filter id="filter3_i_1414_194" x="6" y="5.14246" width="2.57136" height="2.57148" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.285714" dy="0.285714"/>
<feGaussianBlur stdDeviation="0.571429"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.74902 0 0 0 0 0.823529 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1414_194"/>
</filter>
<linearGradient id="paint0_linear_1414_194" x1="4.14289" y1="2.57146" x2="15.2857" y2="14.8572" gradientUnits="userSpaceOnUse">
<stop stop-color="#E9F0FF"/>
<stop offset="1" stop-color="#E0E9FE"/>
</linearGradient>
</defs>
</svg>
