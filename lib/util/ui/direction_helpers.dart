import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:solar_icons/solar_icons.dart';

/// Utility class for handling directional UI elements and RTL/LTR support
class DirectionHelpers {
  static IconData getBackArrowIcon(BuildContext context) {
    final textDirection = Directionality.of(context);
    return textDirection == TextDirection.ltr
        ? SolarIconsOutline.altArrowLeft
        : SolarIconsOutline.altArrowRight;
  }

  static IconData getForwardArrowIcon(BuildContext context) {
    final textDirection = Directionality.of(context);
    return textDirection == TextDirection.ltr
        ? SolarIconsOutline.altArrowRight
        : SolarIconsOutline.altArrowLeft;
  }

  /// Get appropriate chevron icon for dropdowns based on text direction
  static IconData getDropdownChevronIcon(BuildContext context) {
    final textDirection = Directionality.of(context);
    return textDirection == TextDirection.ltr
        ? HugeIcons.strokeRoundedArrowDown01
        : HugeIcons.strokeRoundedArrowDown01;
  }

  static AlignmentDirectional getStartAlignment() {
    return AlignmentDirectional.centerStart;
  }

  static AlignmentDirectional getEndAlignment() {
    return AlignmentDirectional.centerEnd;
  }

  static TextAlign getStartTextAlign() {
    return TextAlign.start;
  }

  static TextAlign getEndTextAlign() {
    return TextAlign.end;
  }

  static EdgeInsetsDirectional fromSTEB(
    double start,
    double top,
    double end,
    double bottom,
  ) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  static EdgeInsetsDirectional symmetric({
    double vertical = 0.0,
    double horizontal = 0.0,
  }) {
    return EdgeInsetsDirectional.symmetric(
      vertical: vertical,
      horizontal: horizontal,
    );
  }

  static EdgeInsetsDirectional only({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return EdgeInsetsDirectional.only(
      start: start,
      top: top,
      end: end,
      bottom: bottom,
    );
  }

  /// Check if current direction is RTL
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }

  /// Check if current direction is LTR
  static bool isLTR(BuildContext context) {
    return Directionality.of(context) == TextDirection.ltr;
  }

  static BorderRadiusDirectional getDirectionalBorderRadius({
    double topStart = 0.0,
    double topEnd = 0.0,
    double bottomStart = 0.0,
    double bottomEnd = 0.0,
  }) {
    return BorderRadiusDirectional.only(
      topStart: Radius.circular(topStart),
      topEnd: Radius.circular(topEnd),
      bottomStart: Radius.circular(bottomStart),
      bottomEnd: Radius.circular(bottomEnd),
    );
  }

  /// Get appropriate main axis alignment for Row widgets
  static MainAxisAlignment getStartMainAxisAlignment() {
    return MainAxisAlignment.start;
  }

  static MainAxisAlignment getEndMainAxisAlignment() {
    return MainAxisAlignment.end;
  }

  /// Get appropriate cross axis alignment for Column widgets
  static CrossAxisAlignment getStartCrossAxisAlignment() {
    return CrossAxisAlignment.start;
  }

  static CrossAxisAlignment getEndCrossAxisAlignment() {
    return CrossAxisAlignment.end;
  }
}
