import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/app_theme.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/toast/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:solar_icons/solar_icons.dart';

void showBottomPopup(
  BuildContext context,
  Widget child,
) {
  final appTheme = AppTheme.of(context);

  showModalBottomSheet(
      context: context,
      backgroundColor: appTheme.colors.backgroundContainer,
      useRootNavigator: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(40))),
      isScrollControlled: true,
      useSafeArea: true,
      builder: (ctx) {
        // return DraggableScrollableSheet(
        //   expand: false,
        //   initialChildSize: 0.7,
        //   minChildSize: 0.5,
        //   maxChildSize: 0.8,
        //   builder: (context, scrollController) {
        //     return ForgotPasswordPage(scrollController: scrollController);
        //   },
        // );
        return child;
      });
}

// Widget buildLoading(BuildContext context) {
//   final appTheme = AppTheme.of(context);

//   return Column(
//     crossAxisAlignment: CrossAxisAlignment.center,
//     mainAxisAlignment: MainAxisAlignment.center,
//     children: [
//       // this was used to add logo image in loading components.
//       // FractionalTranslation(
//       //   translation: const Offset(0.05, 0),
//       //   child: const ImageContainer(
//       //     url: null,
//       //     placeholderAsset: Assets.logoImage,
//       //     height: 60,
//       //     width: 60,
//       //   ).animate(onPlay: (controller) {
//       //     controller.repeat();
//       //   }).shimmer(
//       //       duration: 1.seconds, color: AppColors.primary.withOpacity(0.25)),
//       // ),
//       // const SizedBox(height: 12),
//       JumpingDots(
//         verticalOffset: -5,
//         radius: 7,
//         color: colors(context).primary,
//       ),
//     ],
//   );
// }

/// shows themed snackbar
void showAppSnackbar(BuildContext context,
    {required String title, required String type, String? text}) {
  if (!context.mounted) return;
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;

  final primaryColor = switch (type) {
    'success' => colors.success,
    'error' => colors.error,
    'info' => colors.warning,
    String() => colors.error,
  };
  final backgroundColor = switch (type) {
    'success' => colors.successContainer,
    'error' => colors.errorContainer,
    'info' => colors.warningContainer,
    String() => colors.errorContainer,
  };
  final icon = switch (type) {
    'success' => FontAwesomeIcons.solidCircleCheck,
    'error' => FontAwesomeIcons.solidCircleXmark,
    'info' => FontAwesomeIcons.solidCircleQuestion, // or circleinfo ?
    String() => FontAwesomeIcons.solidCircleXmark,
  };

  Flushbar(
    animationDuration: 500.milliseconds,
    duration: const Duration(seconds: 3),
    // titleText: Text(
    //   title,
    //   style: textStyles(context).titleSmall,
    // ),
    flushbarPosition: FlushbarPosition.TOP,
    flushbarStyle: FlushbarStyle.FLOATING,
    margin: const EdgeInsets.symmetric(horizontal: 16),
    messageText:
        // text != null
        //     ?
        Text(
      title,
      style: textStyles.body.copyWith(color: primaryColor),
    ),
    icon: Icon(
      icon,
      color: primaryColor,
      size: 24,
    ),
    shouldIconPulse: false,
    //     : null,
    backgroundColor: backgroundColor,
    borderRadius: BorderRadius.circular(12),

    leftBarIndicatorColor: primaryColor,
  ).show(context);

  // ScaffoldMessenger.of(context).showSnackBar(
  //   SnackBar(
  //     elevation: 10,

  //     backgroundColor: Colors.black,
  //     content: widget ??
  //         Column(
  //           // crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             const SizedBox(height: 20),
  //             Text(
  //               text,
  //               style: AppTextStyle.title,
  //             ),
  //           ],
  //         ),
  //     // showCloseIcon: true,
  //     // closeIconColor: Color(0xffF8FAFC),
  //   ),
  // );
}

/// dialog box in large device.. (600w) and bottom sheet otherwise
Future<T?> showAdaptivePopup<T>(
  BuildContext context,
  Widget Function(BuildContext, ScrollController?) builder, {
  bool scrollable = false,
  EdgeInsets? contentPadding,
  bool drag = true,
  double topRadius = 40.0,
  bool useAppbar = false,
  bool isDismissible = true,
  String? appBarTitle,
  double initialChildSize = 0.9,
  double minChildSize = 0.7,
  bool fullScreen = false,
  required bool useRootNavigator,
}) async {
  final width = MediaQuery.sizeOf(context).width;
  final dialogMode = width > 600;
  const horizontalPadding = 22.0;
  const verticalPadding = 24.0;
  const appbarSpacing = 8.0;

  // Capture theme values early
  final surfaceColor = colors(context).surface;
  final themeColorScheme = Theme.of(context).colorScheme;

  Widget buildContent(BuildContext ctx, Widget content) {
    return Container(
      clipBehavior: Clip.hardEdge,
      margin: dialogMode ? const EdgeInsets.all(24) : null,
      decoration: BoxDecoration(
          color: surfaceColor,
          borderRadius: BorderRadius.vertical(
              top: Radius.circular(topRadius),
              bottom: !dialogMode ? Radius.zero : Radius.circular(topRadius))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (useAppbar)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: AppBar(
                title: appBarTitle != null ? Text(appBarTitle) : null,
                backgroundColor: Colors.transparent,
              ),
            ),
          Flexible(
            child: Padding(
              padding: contentPadding ??
                  EdgeInsets.fromLTRB(
                      horizontalPadding,
                      useAppbar ? appbarSpacing : verticalPadding,
                      horizontalPadding,
                      verticalPadding),
              child: content,
            ),
          ),
        ],
      ),
    );
  }

  if (!dialogMode) {
    return await showModalBottomSheet(
        context: context,
        enableDrag: !isDismissible ? false : drag,
        backgroundColor: surfaceColor,
        isDismissible: isDismissible,
        useRootNavigator: true,
        shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.vertical(top: Radius.circular(topRadius))),
        isScrollControlled: true,
        useSafeArea: true,
        builder: (ctx) {
          if (scrollable) {
            return DraggableScrollableSheet(
                expand: fullScreen,
                initialChildSize: fullScreen ? 1.0 : initialChildSize,
                minChildSize: fullScreen ? 1.0 : minChildSize,
                maxChildSize: fullScreen ? 1.0 : 1.0,
                builder: (ctx, sc) {
                  return buildContent(ctx, builder(ctx, sc));
                });
          } else {
            return buildContent(ctx, builder(ctx, null));
          }
        });
  } else {
    return await showDialog(
        context: context,
        useRootNavigator: true,
        useSafeArea: true,
        barrierDismissible: isDismissible,
        builder: (ctx) {
          return AlertDialog(
            contentPadding: const EdgeInsets.all(0),
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            content: SizedBox(
                width: MediaQuery.sizeOf(context).width,
                child: buildContent(ctx, builder(ctx, null))),
          );
        });
  }
}

Future<T?> showAppDialog<T>(
  BuildContext context,
  Widget Function(BuildContext, ScrollController?) builder, {
  bool scrollable = false,
  EdgeInsets? contentPadding,
  bool drag = true,
  double dialogRadius = 16,
  bool useAppbar = false,
  bool isDismissible = true,
  String? appBarTitle,
  Color? backgroundColor,
  required bool useRootNavigator,
}) async {
  final appTheme = AppTheme.of(context);
  const horizontalPadding = 12.0;
  const topPadding = 12.0;
  const bottomPadding = 12.0;
  const appbarSpacing = 4.0;

  // Capture theme values early
  final surfaceColor = backgroundColor ?? appTheme.colors.backgroundContainer;

  Widget buildContent(BuildContext ctx, Widget content) {
    return Container(
      clipBehavior: Clip.hardEdge,
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
          color: surfaceColor,
          // border: Border.all(color: appTheme.colors.strokeColor),
          borderRadius: BorderRadius.circular(dialogRadius)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (useAppbar)
            AppBar(
              title: appBarTitle != null ? Text(appBarTitle) : null,
              backgroundColor: Colors.transparent,
            ),
          Flexible(
            child: Padding(
              padding: contentPadding ??
                  EdgeInsets.fromLTRB(
                      horizontalPadding,
                      useAppbar ? appbarSpacing : topPadding,
                      horizontalPadding,
                      bottomPadding),
              child: content,
            ),
          ),
        ],
      ),
    );
  }

  return await showDialog(
      context: context,
      useRootNavigator: useRootNavigator,
      useSafeArea: true,
      barrierDismissible: isDismissible,
      barrierColor: const Color.fromRGBO(0, 0, 0, 0.6),
      builder: (ctx) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(0),
          insetPadding: EdgeInsets.all(16),
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          content: SizedBox(
              width: MediaQuery.sizeOf(context).width,
              child: buildContent(ctx, builder(ctx, null))),
        );
      });
}

Widget customErrorText(BuildContext context, String? text) {
  final appTheme = AppTheme.of(context);

  return Text(
    text ?? "Error",
    style: appTheme.textStyles.body3
        .copyWith(color: appTheme.colors.error, fontSize: 12),
    textAlign: TextAlign.start,
  ).animate().fadeIn();
}

/// items
/// [
///   {
///     'icon': IconData or Widget, // Optional icon to display
///     'label': String, // Required text label
///     'value': T?, // Optional value to return when selected
///     'onPressed': void Function()?, // Optional callback when selected
///     'enabled': bool?, // Optional, defaults to true
///     'color': Color?, // Optional color for icon and text, defaults to secondaryText
///   }
/// ]
///
Future<T?> showLocalContextMenu<T>({
  required BuildContext context,

  ///specify for showing menu originating from another widget.
  GlobalKey? buttonKey,

  ///specify for showing menu from a point.. from gesture detector.
  Offset? pointOffset,
  required List<Map<String, dynamic>> items,
  double? maxWidth,
  Offset Function(Offset buttonOffset, Size buttonSize)? positionShift,
  Color? backgroundColor,
  BorderRadius? borderRadius,
  EdgeInsets? menuPadding,
  bool showDivider = false,
  TextStyle? textStyle,
  void Function(dynamic)? onItemPress,
}) async {
  if (pointOffset == null && buttonKey == null) {
    throw ArgumentError('Either buttonKey or pointOffset must be provided.');
  }
  final appTheme = AppTheme.of(context);

  PopupMenuItem<T?> buildItem(Map<String, dynamic> item, int index) {
    final icon = item['icon'];
    final label = item['label'] as String;
    final value = item['value'] as T?;
    final onPressed = item['onPressed'] as void Function()?;
    final enabled = item['enabled'] as bool? ?? true;
    final color = item['color'] as Color? ?? appTheme.colors.secondaryText;
    final iconColor =
        item['iconColor'] as Color? ?? appTheme.colors.secondaryText;

    // Create the onTap callback
    void Function()? onTapCallback;
    if (onPressed != null) {
      // If item has its own onPressed, use it
      onTapCallback = onPressed;
    } else if (onItemPress != null) {
      // Otherwise, if general callback is provided, use it with value or label
      final itemValue = value ?? label;
      onTapCallback = () => onItemPress(itemValue);
    }
    // If neither exists, onTapCallback remains null

    return PopupMenuItem<T?>(
      value: value,
      enabled: enabled,
      onTap: onTapCallback,
      padding: EdgeInsets.all(0),
      child: Container(
        height: showDivider ? kMinInteractiveDimension : null,
        decoration: showDivider && items.length > 1
            ? BoxDecoration(
                border: Border(
                    top: index != 0
                        ? BorderSide(
                            width: 0.25, color: appTheme.colors.strokeColor)
                        : BorderSide.none,
                    bottom: index != items.length - 1
                        ? BorderSide(
                            width: 0.25, color: appTheme.colors.strokeColor)
                        : BorderSide.none),
              )
            : null,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        child: Row(
          children: [
            if (icon != null) ...[
              if (icon is IconData)
                Icon(
                  icon,
                  size: 24,
                  color: iconColor,
                )
              else if (icon is Widget)
                icon
              else
                const SizedBox.square(dimension: 24),
              const SizedBox(width: 8),
            ],
            Flexible(
              child: Text(
                label,
                style: (textStyle ??
                        appTheme.textStyles.body.copyWith(fontSize: 18))
                    .copyWith(color: color, height: 1.2),
                textScaler: TextScaler.noScaling,
              ),
            ),
          ],
        ),
      ),
    );
  }

  RelativeRect getPosition() {
    if (pointOffset != null) {
      return RelativeRect.fromLTRB(
        pointOffset.dx,
        pointOffset.dy,
        MediaQuery.of(context).size.width - pointOffset.dx,
        MediaQuery.of(context).size.height - pointOffset.dy,
      );
    }
    final renderObject =
        buttonKey?.currentContext!.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Navigator.of(context).overlay!.context.findRenderObject() as RenderBox;
    final translation = renderObject.getTransformTo(null).getTranslation();
    final offset = Offset(translation.x, translation.y);
    final shiftedOffset = positionShift?.call(offset, renderObject.size) ??
        offset.translate(0, renderObject.size.height + 8);

    final rect = renderObject.paintBounds.shift(shiftedOffset);
    return RelativeRect.fromRect(rect, Offset.zero & overlay.size);
  }

  final value = await showMenu<T?>(
    context: context,
    position: getPosition(),
    color: backgroundColor ?? appTheme.colors.backgroundContainer,
    shape: RoundedRectangleBorder(
      borderRadius: borderRadius ?? BorderRadius.circular(16),
    ),
    menuPadding: menuPadding ??
        EdgeInsets.symmetric(horizontal: 8, vertical: showDivider ? 15 : 16),
    constraints: BoxConstraints(
      maxWidth: maxWidth ?? 240,
    ),
    items: items.indexed.map((v) => buildItem(v.$2, v.$1)).toList(),
  );

  return value;
}

// FlCountryCodePicker buildCountryPicker(BuildContext context) {
//   return FlCountryCodePicker(
//     showDialCode: true,
//     showSearchBar: true,
//     favoritesIcon: const Icon(
//       Icons.star,
//       color: Colors.transparent,
//     ),
//     favorites: ['CA', 'US'],

//     title: const Padding(
//       padding: EdgeInsets.fromLTRB(24, 24, 24, 10),
//       child: Text('Choose Country Code', style: AppTextStyle.title),
//     ),
//     // filteredCountries: _yourFilters,
//     countryTextStyle: AppTextStyle.menuTitle,
//     dialCodeTextStyle:
//         AppTextStyle.menuTitle.copyWith(color: AppColors.primary),
//     searchBarDecoration: InputDecoration(
//       hintText: 'Search Here',
//       enabledBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(16),
//         borderSide: const BorderSide(
//           color: Color(0xff384860),
//           width: 1,
//         ),
//       ),
//       focusedBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(16),
//         borderSide: const BorderSide(
//           color: AppColors.primary200,
//           width: 2,
//         ),
//       ),
//       errorBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(16),
//         borderSide: BorderSide(
//           color: Theme.of(context).colorScheme.error,
//           width: 2,
//         ),
//       ),
//       focusedErrorBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(16),
//         borderSide: const BorderSide(
//           color: Colors.red,
//           width: 2,
//         ),
//       ),
//       errorStyle: AppTextStyle.title
//           .copyWith(fontSize: 12, color: Theme.of(context).colorScheme.error),
//       hintStyle: AppTextStyle.subtitle,
//       contentPadding: const EdgeInsets.only(
//         top: 16,
//         left: 16,
//         right: 16,
//         bottom: 16,
//       ),
//     ),
//   );
// }

void unfocus() {
  FocusManager.instance.primaryFocus?.unfocus();
}

Future<dynamic> showLocalPickerMenu({
  required BuildContext context,

  ///specify for showing menu originating from another widget.
  GlobalKey? buttonKey,

  ///specify for showing menu from a point.. from gesture detector.
  Offset? pointOffset,
  List<String> allowedTypes = const ['image', 'video', 'pdf', 'any'],
  bool allowMultiple = false,
  double maxSizeInMB = 5.0,
  double maxHeight = 1024,
  double maxWidth = 1024,
  int imageQuality = 60,
}) async {
  final appTheme = AppTheme.of(context);
  final items = <Map<String, dynamic>>[];
  final fileAllowed = allowedTypes.contains('any');

  final allowImages = fileAllowed || allowedTypes.contains('image');
  final allowVideos = fileAllowed || allowedTypes.contains('video');
  final allowBoth = fileAllowed || (allowImages && allowVideos);
  final strings = AppLocalizations.of(context)!;
  // Build menu items based on allowed types
  if (allowImages) {
    items.add({
      'icon': SolarIconsOutline.camera,
      'label': strings.pickerTakePhoto,
      'value': 'camera_image',
      'iconColor': appTheme.colors.primary,
    });
  }

  if (allowVideos) {
    items.add({
      'icon': SolarIconsOutline.videocameraRecord,
      'label': strings.pickerRecordVideo,
      'value': 'camera_video',
      'iconColor': appTheme.colors.primary,
    });
  }

  if (allowBoth) {
    items.add({
      'icon': SolarIconsOutline.gallery,
      'label': strings.pickerChooseFromGallery,
      'value': 'gallery_media',
      'iconColor': appTheme.colors.primary,
    });
  } else {
    if (allowImages) {
      items.add({
        'icon': SolarIconsOutline.gallery,
        'label': strings.pickerChooseFromGallery,
        'value': 'gallery_image',
        'iconColor': appTheme.colors.primary,
      });
    }
    if (allowVideos) {
      items.add({
        'icon': SolarIconsOutline.videoLibrary,
        'label': strings.pickerChooseFromGallery,
        'value': 'gallery_video',
        'iconColor': appTheme.colors.primary,
      });
    }
  }

  if (allowedTypes.contains('pdf') || allowedTypes.contains('any')) {
    items.add({
      'icon': SolarIconsOutline.fileText,
      'label': strings.pickerChooseFile,
      'value': 'file',
      'iconColor': appTheme.colors.primary,
    });
  }

  // Show the context menu
  final selectedAction = await showLocalContextMenu<String>(
    context: context,
    buttonKey: buttonKey,
    pointOffset: pointOffset,
    // showDivider: true,
    items: items,
  );

  if (selectedAction == null) return null;

  final picker = ImagePicker();
  List<File> pickedFiles = [];

  try {
    if (selectedAction.startsWith('camera')) {
      final XFile? media;
      if (selectedAction.endsWith('image')) {
        media = await picker.pickImage(
          source: ImageSource.camera,
          maxHeight: maxHeight,
          maxWidth: maxWidth,
          imageQuality: imageQuality,
        );
      } else {
        media = await picker.pickVideo(source: ImageSource.camera);
      }
      if (media != null) {
        pickedFiles.add(File(media.path));
      }
    } else if (selectedAction.startsWith('gallery')) {
      if (selectedAction == 'gallery_media') {
        if (allowMultiple) {
          final medias = await picker.pickMultipleMedia(
            maxHeight: maxHeight,
            maxWidth: maxWidth,
            imageQuality: imageQuality,
          );
          pickedFiles.addAll(medias.map((f) => File(f.path)));
        } else {
          final media = await picker.pickMedia(
            maxHeight: maxHeight,
            maxWidth: maxWidth,
            imageQuality: imageQuality,
          );
          if (media != null) pickedFiles.add(File(media.path));
        }
      } else if (allowMultiple) {
        if (selectedAction.endsWith('image')) {
          final images = await picker.pickMultiImage(
            maxHeight: maxHeight,
            maxWidth: maxWidth,
            imageQuality: imageQuality,
          );
          pickedFiles.addAll(images.map((f) => File(f.path)));
        } else {
          // No multi-video picker, so we pick one
          final video = await picker.pickVideo(source: ImageSource.gallery);
          if (video != null) pickedFiles.add(File(video.path));
        }
      } else {
        final XFile? media;
        if (selectedAction.endsWith('image')) {
          media = await picker.pickImage(
            source: ImageSource.gallery,
            maxHeight: maxHeight,
            maxWidth: maxWidth,
            imageQuality: imageQuality,
          );
        } else {
          media = await picker.pickVideo(source: ImageSource.gallery);
        }
        if (media != null) {
          pickedFiles.add(File(media.path));
        }
      }
    } else if (selectedAction == 'file') {
      FileType type = FileType.any;
      List<String>? extensions;
      if (allowedTypes.contains('pdf') && !allowedTypes.contains('any')) {
        type = FileType.custom;
        extensions = ['pdf'];
      }
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: extensions,
        allowMultiple: allowMultiple,
      );
      if (result != null) {
        pickedFiles
            .addAll(result.paths.where((p) => p != null).map((p) => File(p!)));
      }
    }
  } catch (e) {
    showAppSnackbar(context, title: 'Error picking file: $e', type: 'error');
    return null;
  }

  if (pickedFiles.isEmpty) return null;

  // Validate file sizes
  final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  final validFiles = <File>[];
  int removedCount = 0;

  for (final file in pickedFiles) {
    final fileSize = await file.length();
    if (fileSize <= maxSizeInBytes) {
      validFiles.add(file);
    } else {
      removedCount++;
    }
  }

  if (removedCount > 0) {
    if (allowMultiple) {
      showAppSnackbar(
        context,
        title: '$removedCount file(s) were too large and not included.',
        type: 'info',
      );
    } else {
      showAppSnackbar(
        context,
        title: 'The selected file is too large (max ${maxSizeInMB}MB).',
        type: 'error',
      );
      return null;
    }
  }

  if (validFiles.isEmpty) return null;

  return allowMultiple ? validFiles : validFiles.first;
}
