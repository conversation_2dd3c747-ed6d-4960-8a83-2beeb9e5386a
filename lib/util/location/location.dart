import 'dart:ui';
import 'package:ako_basma/providers/api/keys.dart';
import 'package:flutter/foundation.dart';

import 'package:dio/dio.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../util/hive/hive_util.dart';
import '../../components/form/location_selector.dart';

/// For location related stuff.
/// Geocoding, Places api, Geolocator, etc.
/// Enable appropriate apis and update api keys in constants.
/// Configure geolocator & locations permission handler also.

final dio = Dio();

/// search for locations with a text
/// uses places/textSearch v2
Future<List<LocationId>> searchLocation(
  String query, {
  CancelToken? cancelToken,
}) async {
  final response = await dio.get(
    'https://maps.googleapis.com/maps/api/place/textsearch/json',
    queryParameters: {
      "query": query,
      'key': mapsKey,
    },
    options: Options(
      preserveHeaderCase: true,
      headers: {
        'Content-Type': 'application/json',
        'X-Goog-Api-Key': mapsKey,
        'X-Goog-FieldMask':
            'places.displayName,places.formattedAddress,places.location',
      },
    ),
    cancelToken: cancelToken,
  );

  if (response.statusCode == 200) {
    final data = response.data;
    if (data['status'] == 'OK' && data['results'].isNotEmpty) {
      final place = (data['results'] as List).map((e) => LocationId(
            label: e['name'],
            address: e['formatted_address'],
            hasAddressPlaceholder:
                ((e['formatted_address'] as String?) ?? "").trim().isEmpty,
            latlng: LatLng(
              e['geometry']['location']['lat'],
              e['geometry']['location']['lng'],
            ),
          )); // Assuming you want the first result
      return place.toList();
      // Extract details directly from the Text Search response
    } else {
      print('No places found matching the search query');
      return [];
    }
  } else {
    // print('Text Search API error:', data);
    // return [];
    throw ('Error while fetching search results!');
  }
}

/// Geocoding function, to get address for latlng.
/// uses google geocode
Future<String?> getAddress(LatLng latLng, Locale? locale) async {
  final response = await dio
      .get('https://maps.google.com/maps/api/geocode/json', queryParameters: {
    'key': mapsKey,
    if (locale != null) 'language': locale.languageCode,
    'latlng': '${latLng.latitude},${latLng.longitude}'
  });

  if (response.statusCode == 200) {
    String formattedAddress = response.data["results"][0]["formatted_address"];
    print("response ==== $formattedAddress");
    return formattedAddress;
  }
  return null;
}

/// Obtains device location,
/// uses geolocator
Future<LatLng?> getCurrentLocation({
  LocationAccuracy accuracy = LocationAccuracy.high,
  Duration? timeLimit,
}) async {
  bool serviceEnabled;
  LocationPermission permission;

  // Test if location services are enabled.
  serviceEnabled = await Geolocator.isLocationServiceEnabled();
  if (!serviceEnabled) {
    // Location services are not enabled don't continue
    // accessing the position and request users of the
    // App to enable the location services.
    if (kDebugMode) {
      print('Location services are disabled');
    }
    return null; // Return null instead of Future.error to prevent exceptions
  }

  permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.denied) {
      // Permissions are denied, next time you could try
      // requesting permissions again (this is also where
      // Android's shouldShowRequestPermissionRationale
      // returned true. According to Android guidelines
      // your App should show an explanatory UI now.
      if (kDebugMode) {
        print('Location permissions are denied');
      }
      return null; // Return null instead of Future.error
    }
  }

  if (permission == LocationPermission.deniedForever) {
    // Permissions are denied forever, handle appropriately.
    if (kDebugMode) {
      print('Location permissions are permanently denied');
    }
    return null; // Return null instead of Future.error
  }

  // When we reach here, permissions are granted and we can
  // continue accessing the position of the device.
  try {
    // Enhanced location settings for better accuracy
    final LocationSettings locationSettings = LocationSettings(
      accuracy: accuracy,
      distanceFilter:
          10, // Minimum distance (in meters) a device must move before an update
      timeLimit:
          timeLimit ?? const Duration(seconds: 15), // Timeout after 15 seconds
    );

    final pos = await Geolocator.getCurrentPosition(
      locationSettings: locationSettings,
    );

    final loc = LatLng(pos.latitude, pos.longitude);

    // Validate that we got reasonable coordinates (not 0,0 or other invalid values)
    if (pos.latitude == 0.0 && pos.longitude == 0.0) {
      if (kDebugMode) {
        print('Invalid location coordinates (0,0) received');
      }
      return null;
    }

    // Save the valid location
    HiveUtils.setLastLocation(loc);

    if (kDebugMode) {
      print('Current location obtained: ${pos.latitude}, ${pos.longitude}');
      print('Location accuracy: ${pos.accuracy} meters');
    }

    return loc;
  } catch (e) {
    if (kDebugMode) {
      print('Error in getting current location: $e');
    }

    // Try to get last known position as fallback
    try {
      final lastPos = await Geolocator.getLastKnownPosition();
      if (lastPos != null) {
        final lastLoc = LatLng(lastPos.latitude, lastPos.longitude);
        if (kDebugMode) {
          print(
              'Using last known location: ${lastPos.latitude}, ${lastPos.longitude}');
        }
        return lastLoc;
      }
    } catch (lastPosError) {
      if (kDebugMode) {
        print('Error getting last known position: $lastPosError');
      }
    }

    return null;
  }
}

/// Get a reasonable default location based on user's region
/// This should be used only as a last resort when all location methods fail
LatLng getRegionalDefaultLocation() {
  // You should replace this with a more appropriate default for your user base
  // For now, I'm using coordinates for Iraq/Kurdistan region since your app seems to be for that region
  return const LatLng(36.1911, 44.0094); // Erbil, Kurdistan Region, Iraq
}
