import 'dart:math';

import 'package:ako_basma/providers/api/api_client.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../components/button/primary_button.dart';
import '../../components/form/chip_selector.dart';
import '../../components/form/simple_text_field.dart';
import '../../providers/api/api_service.dart';
import '../../providers/auth/auth.dart';
import '../../util/form/validators.dart';
import '../../util/ui/popups.dart';
import '../common/imageDialogPopup.dart';

class FeedbackPopup extends ConsumerStatefulWidget {
  const FeedbackPopup({
    super.key,
    this.subject,
  });

  final String? subject;

  @override
  ConsumerState<FeedbackPopup> createState() => _FeedbackPopupState();
}

class _FeedbackPopupState extends ConsumerState<FeedbackPopup> {
  final _fieldController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final _selectedItems = <String>[];
  @override
  Widget build(BuildContext context) {
    final profile = ref.watch(authStateProvider);
    return Container(
      padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom > 0
              ? MediaQuery.of(context).viewInsets.bottom + 15
              : 0),
      decoration: BoxDecoration(
          color: colors(context).surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(40))),
      child: Column(
        children: [
          // AppBar(
          //   title: Text(
          //     'Send us some feedback!',
          //     style: AppTextStyle.title.copyWith(fontSize: 18),
          //     textAlign: TextAlign.center,
          //   ),
          // ),
          Expanded(
            child: ListView(
              shrinkWrap: true,
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 30),
              // controller: widget.scrollController,
              children: [
                // const SizedBox(height: 16),
                Text(
                  'Do you have some suggestion or found some bug? Let us know.',
                  style: textStyles(context).bodySmall,
                ),
                const SizedBox(height: 12),
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SimpleTextField(
                        controller: _fieldController,
                        validator: (p0) {
                          return Validators.required(p0) != null
                              ? 'A brief message is required'
                              : null;
                        },
                        textInputAction: TextInputAction.done,
                        headingPadding: 8,
                        minLines: 5,
                        maxLines: 10,
                        decoration: const InputDecoration(
                            hintText: 'Describe your issue or idea...'),
                        // style: AppTextStyle.title.copyWith(fontSize: 14),
                        autocorrect: false,
                      ),

                      const SizedBox(height: 16),
                      Text(
                        'Tags',
                        style: textStyles(context).titleSmall,
                      ),
                      // Text(
                      //   'Clickable Filter',
                      //   style: AppTextStyle.sectionTitleMedium,
                      // ),
                      const SizedBox(height: 8),
                      ChipSelector.wrap(
                        validator: () {
                          return _selectedItems.isEmpty
                              ? 'Choose one or more tags'
                              : null;
                        },
                        items: const ['Bug', 'Feedback', 'Others'],
                        selectedItems: _selectedItems,
                        onItemTap: _toggleCategorySelection,
                        borderColor: colors(context).primaryContainer,
                        // selectedChipColor: AppColors.gray500,
                        // selectedTextColor: AppColors.primary200,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
          SafeArea(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                  24, 24, 24, max(MediaQuery.paddingOf(context).bottom, 24)),
              child: PrimaryButton.async(
                  label: 'Send Feedback', onPressed: _sendFeedback),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleCategorySelection(String item) {
    final index = _selectedItems.indexOf(item);
    setState(() {
      if (index != -1) {
        _selectedItems.removeAt(index);
      } else {
        _selectedItems.add(item);
      }
    });
  }

  Future<void> _sendFeedback() async {
    //validate
    if (_formKey.currentState?.validate() ?? false) {
      final profile = ref.read(authStateProvider);
      final name = profile?.name;
      final email = profile?.email;
      final phone = profile?.phone;
      final type = _selectedItems.contains('Feedback') ? 'feedback' : 'other';
      final subject = widget.subject ??
          (_selectedItems.contains('Bug')
              ? 'Found bug in mobile app!'
              : 'App Feedback');
      final content = _fieldController.text.trim();
      bool hasError = false;
      try {
        await ref.read(apiServiceProvider).createRequest(
              name: name,
              email: email,
              phone: phone,
              type: type,
              subject: subject,
              content: content,
            );
      } catch (e) {
        hasError = true;
        if (context.mounted) {
          showAppSnackbar(
            context,
            title: 'Problem while sending your request',
            type: 'error',
            text: e.errorMsg,
          );
        }
      }
      if (!hasError) {
        if (context.mounted) {
          Navigator.pop(context);
          showAdaptivePopup(
            context,
            (ctx, sc) => const ImageDialogPopup.success(
              description: 'Your feedback was submitted successfully.',
              title: 'Thank you !',
            ),
            useRootNavigator: true,
          );
        }
      }
    }

    //show some toast.
  }
}
