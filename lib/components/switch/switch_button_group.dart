import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

/// A reusable segmented switch button group that can display
/// any number of labels (minimum 2) and notifies the parent
/// when a segment is selected.
///
/// • Uses `Container` for spacing instead of `Si<PERSON><PERSON>ox` to conform
///   with project guidelines.
/// • Fully responsive – button group expands to full available width.
/// • Maintains a single `selectedIndex`, ensuring only one option
///   can be selected at a time (fixes previous bug where two buttons
///   could appear selected).
class SwitchButtonGroup extends StatefulWidget {
  const SwitchButtonGroup({
    super.key,
    required this.labels,
    this.initialIndex = 0,
    required this.onTap,
  })  : assert(labels.length >= 2, 'At least two labels are required.'),
        assert(initialIndex >= 0 && initialIndex < labels.length,
            'initialIndex must be within the range of labels.');

  /// Text labels to display for each segment.
  final List<String> labels;

  /// The initially-selected segment index.
  final int initialIndex;

  /// Callback fired with the selected segment index whenever the user taps.
  final ValueChanged<int> onTap;

  @override
  State<SwitchButtonGroup> createState() => _SwitchButtonGroupState();
}

class _SwitchButtonGroupState extends State<SwitchButtonGroup> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    // Styles shared across all segments.
    const double borderRadius = 6.0;
    const double borderWidth = 1.0;
    const double buttonHeight = 35.0;

    final Color selectedBgColor = colors.primaryVariant;
    final Color unselectedBgColor = colors.background;
    final Color selectedTextColor = colors.primary;
    final Color unselectedTextColor = colors.secondaryText;
    final Color unselectedBorderColor = colors.strokeColor;

    return Container(
      width: screenWidth - 32,
      height: 48,
      margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      decoration: BoxDecoration(
        border: Border.all(color: colors.primaryVariant, width: 1),
        borderRadius: BorderRadius.circular(8),
        color: colors.backgroundContainer,
      ),
      padding: const EdgeInsets.all(4),
      child: Row(
        children: List.generate(widget.labels.length * 2 - 1, (i) {
          // Even indices hold Expanded segments, odd indices hold spacers.
          if (i.isOdd) {
            return Container(width: 8); // Spacer using Container ‑ no SizedBox.
          }

          final int index = i ~/ 2;
          final bool isSelected = _selectedIndex == index;

          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() => _selectedIndex = index);
                widget.onTap(index);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: buttonHeight,
                decoration: BoxDecoration(
                  color: isSelected ? selectedBgColor : unselectedBgColor,
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: isSelected ? selectedBgColor : unselectedBorderColor,
                    width: borderWidth,
                  ),
                ),
                child: Center(
                  child: Text(
                    widget.labels[index],
                    textAlign: TextAlign.center,
                    style: textStyles.body.copyWith(
                      color:
                          isSelected ? selectedTextColor : unselectedTextColor,
                    ),
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
