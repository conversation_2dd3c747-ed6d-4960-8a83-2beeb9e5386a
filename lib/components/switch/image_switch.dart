import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';

class ImageSwitch extends StatelessWidget {
  const ImageSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    required this.activeTrackImage,
    required this.inactiveTrackImage,
    required this.activeThumbImage,
    required this.inactiveThumbImage,
    this.width = 50,
    this.height = 38,
    this.duration = const Duration(milliseconds: 300),
  });

  final bool value;
  final ValueChanged<bool> onChanged;
  final String activeTrackImage;
  final String inactiveTrackImage;
  final String activeThumbImage;
  final String inactiveThumbImage;
  final double width;
  final double height;
  final Duration duration;

  @override
  Widget build(BuildContext context) {
    // Padding inside the track so the thumb doesn't touch edges
    const double horizontalPadding = 4.0;
    final double thumbSize = height - horizontalPadding * 2;

    return GestureDetector(
      onTap: () => onChanged(!value),
      child: AnimatedContainer(
        duration: duration,
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(height / 2),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(height / 2),
          child: Stack(
            children: [
              // Background image blur effect
              Positioned.fill(
                child: ImageFiltered(
                  imageFilter: ImageFilter.blur(sigmaX: 0.6, sigmaY: 0.6),
                  child: SvgPicture.asset(
                    value ? activeTrackImage : inactiveTrackImage,
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              // Thumb container with padding
              Positioned.fill(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: horizontalPadding),
                  child: Stack(
                    children: [
                      // Animated thumb (sun/moon)
                      AnimatedAlign(
                        duration: duration,
                        curve: Curves.easeInOut,
                        alignment: value
                            ? AlignmentDirectional.centerEnd
                            : AlignmentDirectional.centerStart,
                        child: SizedBox(
                          width: thumbSize,
                          height: thumbSize,
                          child: SvgPicture.asset(
                            value ? activeThumbImage : inactiveThumbImage,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
