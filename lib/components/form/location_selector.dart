import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:remixicon/remixicon.dart';
import 'package:geolocator/geolocator.dart'; // Add geolocator import

import '../../util/form/validators.dart';
import '../../util/hive/hive_util.dart';
import '../../util/location/location.dart';
import '../../util/ui/popups.dart';
import '../button/primary_button.dart';

class LocationSelectorWizard extends ConsumerStatefulWidget {
  const LocationSelectorWizard({
    super.key,
    this.onSelected,
    this.onSelectedAsync,
    this.initialSelection,
  });
  final void Function(LocationId data)? onSelected;
  final Future<void> Function(LocationId data)? onSelectedAsync;

  final LocationId? initialSelection;

  @override
  ConsumerState<LocationSelectorWizard> createState() =>
      _LocationSelectorWizardState();
}

class _LocationSelectorWizardState
    extends ConsumerState<LocationSelectorWizard> {
  bool _searchMode = false;
  final _addressController = TextEditingController();
  final _labelController = TextEditingController();
  LatLng? _selectedLatLng;
  final _formKey = GlobalKey<FormState>();

  bool _searching = false;
  bool _hasError = false;
  bool _pickerMode = false;
  static const _searchTimer = Duration(seconds: 1);
  // final _searchController = TextEditingController();
  String? _searchQuery;
  Timer? _debounceTimer;
  List<LocationId>? _locations;

  GoogleMapController? _controller;

  // GoogleMapController? _controller;
  @override
  void initState() {
    if (widget.initialSelection != null) {
      _addressController.text = widget.initialSelection!.address;
      _labelController.text = widget.initialSelection!.label ?? "";
      _selectedLatLng = widget.initialSelection!.latlng;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final focusLatLng = widget.initialSelection != null
              ? widget.initialSelection!.latlng
              : await getCurrentLocation(
                    accuracy: LocationAccuracy.medium,
                    timeLimit: const Duration(seconds: 10),
                  ) ??
                  getRegionalDefaultLocation() // Use regional default as fallback
          //  ?? HiveUtils.getLastLocation()
          ;
      if (focusLatLng != null) {
        _mapSelection(focusLatLng);
      }
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final detailedAddressForm = Padding(
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // const SizedBox(height: 8),
            // Text(
            //   'Detailed Address',
            //   style: text(context).labelLarge,
            // ),
            // const SizedBox(height: 8),
            // TextFormField(
            //   controller: _addressController,
            //   validator: (value) => Validators.requiredField(value, context),
            //   decoration: InputDecoration(hintText: 'Place Address'),
            // ),
            // const SizedBox(height: 8),
            const SizedBox(height: 24),
            Text(
              'Alias',
              style: textStyles(context).labelLarge,
            ),
            // const SizedBox(height: 8),
            TextFormField(
              controller: _labelController,
              validator: (value) => Validators.required(value, context),
              // decoration: InputDecoration(hintText: 'Place Title'),
              onChanged: (val) {
                setState(() {});
              },
              decoration: InputDecoration(
                border: const UnderlineInputBorder(),
                helperText:
                    'This helps you easily identify this saved address.',
                hintText: 'Place Title',
                contentPadding:
                    const EdgeInsetsDirectional.fromSTEB(0, 0, 10, 0),
              ),
            ),
            const SizedBox(height: 16),
            ChipSelector(
                items: ['Home', 'Work', 'Others'],
                selectedItems: [_labelController.text.trim()],
                borderColor: colors(context).primary.withOpacity(0.1),
                onItemTap: (tag) {
                  setState(() {
                    _labelController.text = tag;
                  });
                }),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );

    final focusLatLng = _selectedLatLng ??
        (widget.initialSelection?.latlng ??
            // HiveUtils.getLastLocation() ??
            getRegionalDefaultLocation()); // Use regional default instead of hardcoded coordinates
    final searchResultsPage = _searching
        ? Center(child: CircularProgressIndicator())
        : (_hasError || (_locations?.isEmpty ?? true))
            ? Center(
                child: Padding(
                  padding:
                      const EdgeInsetsDirectional.symmetric(horizontal: 24),
                  child: Text(
                    _searching
                        ? 'Searching Location'
                        : _hasError
                            ? 'Oops. Some error occured'
                            : _locations == null
                                ? 'Search for the location using the search box or select from map.'
                                : 'No search results found for your query. Try again.',
                    textAlign: TextAlign.center,
                    // style: AppTextStyle.menuSubtitle,
                  ),
                ),
              )
            : Column(
                // padding: const EdgeInsets.fromLTRB(0, 15, 0, 30),
                children: [
                  ..._locations!.map(
                    (e) {
                      return _buildTile(context, data: e);
                    },
                  ),
                  // _buildTile(
                  //     title: 'Burger Town', address: '11th St., New Town'),
                  // _buildTile(
                  //     title: 'Burger Town', address: '11th St., New Town'),
                  // _buildTile(
                  //     title: 'Burger Town', address: '11th St., New Town'),
                ],
              );
    return Column(
      children: [
        Expanded(
          child: LayoutBuilder(builder: (context, constraints) {
            final mapContainerHeight =
                _searchMode || MediaQuery.viewInsetsOf(context).bottom > 50
                    ? 0.0
                    : constraints.maxHeight * 0.35;
            final showingMap = mapContainerHeight != 0.0;
            return Column(
              children: [
                AnimatedContainer(
                  duration: 500.milliseconds,
                  curve: Easing.standardDecelerate,
                  height: mapContainerHeight,
                  // color: Colors.blueAccent,
                  width: constraints.maxWidth,
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Listener(
                          onPointerDown: (event) {
                            setState(() {
                              _pickerMode = true;
                            });
                          },
                          onPointerUp: (event) async {
                            if (_controller == null) return;
                            setState(() {
                              _pickerMode = false;
                            });
                            LatLngBounds bounds =
                                await ((_controller)!.getVisibleRegion());
                            final lng = (bounds.northeast.longitude +
                                    bounds.southwest.longitude) /
                                2;
                            final lat = (bounds.northeast.latitude +
                                    bounds.southwest.latitude) /
                                2;
                            _mapSelection(LatLng(lat, lng));
                          },
                          child: GoogleMap(
                            // style:
                            //     Theme.of(context).brightness == Brightness.dark
                            //         ? mapNightStyleString
                            //         : null,
                            initialCameraPosition: CameraPosition(
                              target: focusLatLng,
                              zoom: 14.4746,
                            ),
                            onMapCreated:
                                (GoogleMapController controller) async {
                              setState(() {
                                _controller = controller;
                                // _init = false;
                              });
                            },
                            myLocationButtonEnabled: false,
                            myLocationEnabled: true,
                            rotateGesturesEnabled: false,
                            zoomControlsEnabled: false,
                          ),
                        ),
                      ),
                      if (showingMap)
                        Align(
                          alignment: Alignment.center,
                          child: FractionalTranslation(
                            translation: Offset(0, -0.57),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  decoration: ShapeDecoration(
                                    shape: StadiumBorder(),
                                    color: colors(context).tertiary,
                                  ),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  child: Text(
                                    'Place this pin to location',
                                    // Removed TextScaler.noScaling for better accessibility
                                    style: TextStyle(
                                        color: colors(context).onTertiary,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Icon(
                                  FontAwesomeIcons.mapPin,
                                  applyTextScaling: false,
                                  color: Colors.black,
                                )
                                    .animate(
                                        onPlay: (c) => c.repeat(reverse: true))
                                    .moveY(
                                        begin: 0,
                                        end: 4,
                                        duration: 500.milliseconds),
                              ],
                            ),
                          ),
                        ),
                      // Align(
                      //   alignment: Alignment.center,
                      //   child: Container(
                      //     height: 0.2,
                      //     width: 20,
                      //     color: Colors.black,
                      //   ),
                      // ),
                      if (showingMap)
                        Positioned(
                            right: 10,
                            top: 10,
                            child: IconButton.filled(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                style: IconButton.styleFrom(
                                    foregroundColor: colors(context).onSurface,
                                    backgroundColor: colors(context).surface),
                                icon: Icon(
                                  FontAwesomeIcons.xmark,
                                  size: 20,
                                ))),
                      if (showingMap)
                        Positioned(
                            right: 10,
                            bottom: 10,
                            child: IconButton.filled(
                                onPressed: () async {
                                  final latlng = await getCurrentLocation(
                                    accuracy: LocationAccuracy.high,
                                    timeLimit: const Duration(seconds: 15),
                                  );
                                  if (latlng == null) {
                                    showAppSnackbar(context,
                                        title: 'Error while getting location',
                                        type: 'error',
                                        text:
                                            'Please make sure you have granted location permissions and location services are enabled.');
                                    return;
                                  }
                                  if (latlng == _selectedLatLng) {
                                    return;
                                  }
                                  _controller?.animateCamera(
                                      CameraUpdate.newLatLng(latlng));
                                  _mapSelection(latlng);
                                },
                                style: IconButton.styleFrom(
                                    foregroundColor: colors(context).onSurface,
                                    backgroundColor: colors(context)
                                        .surfaceContainerHighest),
                                icon: Icon(
                                  FontAwesomeIcons.locationCrosshairs,
                                  size: 20,
                                )))
                    ],
                  ),
                ),
                ListTile(
                  onTap: _toggleSearchMode,
                  tileColor: colors(context).secondaryContainer,
                  title: _searchMode
                      ? Padding(
                          padding:
                              const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 8),
                          child: TextFormField(
                            controller: _addressController,
                            autofocus: true,
                            onChanged: (value) {
                              if (_searchMode) {
                                _setQuery(value);
                              }
                            },
                            decoration: InputDecoration(
                                hintText: 'Search for a location',
                                prefixIcon: Icon(
                                  Remix.search_line,
                                  color: colors(context).primary,
                                )),
                          ).animate().fadeIn(),
                        )
                      : _addressController.text.trim().isNotEmpty
                          ? Text(_addressController.text.trim())
                          : Text('Choose a location'),
                  subtitle: _searchMode ||
                          _addressController.text.trim().isNotEmpty
                      ? null
                      : const Text('Pick from Map or Tap to Search manually'),
                  trailing: Container(
                      padding: EdgeInsets.all(6),
                      decoration: ShapeDecoration(
                          shape: CircleBorder(side: BorderSide(width: 0.1))),
                      child: Icon(
                        _searchMode ? Remix.road_map_line : Remix.search_line,
                        color: colors(context).primary,
                      )),
                ),
                Expanded(
                    child: _searchMode
                        ? SingleChildScrollView(child: searchResultsPage)
                        : SingleChildScrollView(child: detailedAddressForm)),
                SafeArea(
                    top: false,
                    child: Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(16, 0, 16,
                          MediaQuery.viewInsetsOf(context).bottom + 20),
                      child: AnimatedSize(
                        alignment: Alignment.topCenter,
                        duration: 300.milliseconds,
                        child: Column(
                          children: [
                            if (widget.onSelected != null)
                              PrimaryButton(
                                  label: 'Confirm',
                                  onTap: (_selectedLatLng == null)
                                      ? null
                                      : _submitLocation)
                            else
                              PrimaryButton.async(
                                  label: 'Confirm',
                                  onPressed: (_selectedLatLng == null)
                                      ? null
                                      : _submitLocationAsync),
                            if (_searchMode)
                              TextButton(
                                  onPressed: () {
                                    context.pop();
                                  },
                                  child: const Text(
                                    'Go Back',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ))
                          ],
                        ),
                      ),
                    ))
              ],
            );
          }),
        )
      ],
    );
  }

  void _submitLocation() async {
    if (_selectedLatLng == null) {
      // return;
      showAppSnackbar(
        context,
        title: 'Nothing is selected',
        type: 'info',
        text: 'Please select your location before proceeding',
      );
      return;
    }
    if (_formKey.currentState?.validate() ?? false) {
      // widget.onSelected(
      //   AddressData(
      //     address1: _address1Controller.text.trim(),
      //     address2: _address2Controller.text.trim(),
      //     area: _selectedLocation!.area,
      //     city: _selectedLocation!.city,
      //     state: _selectedLocation!.state,
      //     country: _selectedLocation!.country,
      //     pin: _selectedLocation!.pin,
      //     lat: _selectedLocation!.latLng.latitude.toString(),
      //     lng: _selectedLocation!.latLng.longitude.toString(),
      //     tag: _selectedTag,
      //   ),
      // );
      context.pop();
    }
  }

  Future<void> _submitLocationAsync() async {
    if (_selectedLatLng == null) {
      // return;
      showAppSnackbar(
        context,
        title: 'Nothing is selected',
        type: 'info',
        text: 'Please select your location before proceeding',
      );
      return;
    }
    if (_formKey.currentState?.validate() ?? false) {
      if (widget.onSelectedAsync != null) {
        await widget.onSelectedAsync!(
          LocationId(
            label: _labelController.text.trim(),
            address: _addressController.text.trim(),
            hasAddressPlaceholder: _addressController.text.trim().isEmpty,
            latlng: _selectedLatLng!,
          ),
        );
      }
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  void _setQuery(String query) {
    if (_debounceTimer != null) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(_searchTimer, () {
      final title = query.trim().isEmpty ? null : query.trim();
      setState(() {
        _searchQuery = title;
        if (title != null) {
          _searchLocation(title);
        }
        // _partnerPagingController.refresh();
      });
    });
  }

  void _searchLocation(String query) async {
    // unfocus();
    setState(() {
      _hasError = false;
      // _searching = true;
    });

    try {
      final data = await searchLocation(query);

      setState(() {
        _searching = false;
        _hasError = false;

        _locations = data;
      });
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      setState(() {
        _hasError = true;
        // _searching = false;
      });
    }
  }

  void _toggleSearchMode() {
    setState(() {
      _searchMode = !_searchMode;
    });
  }

  Widget _buildTile(BuildContext context, {required LocationId data
      // required LatLng latLng,
      }) {
    return ListTile(
      title: Text(
        data.label ?? "",
        // style: AppTextStyle.menuTitle,
      ),
      subtitle: Text(
        data.address,
        // style: AppTextStyle.menuSubtitle,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      onTap: () {
        _toggleSearchMode();
        _mapSelection(data.latlng, data.address);
        _controller?.animateCamera(CameraUpdate.newLatLng(data.latlng));
      },
      leading: Icon(
        Remix.map_pin_range_line,
        color: colors(context).tertiary,
      ),
    );
  }

  Future<void> _mapSelection(LatLng latlng, [String? address]) async {
    final data = address ??
        await getAddress(
          latlng,
          // ref.read(currentLocaleProvider),
          null,
        );
    if (data != null) {
      setState(() {
        _selectedLatLng = latlng;
        _addressController.text = data;
      });
    }
  }
}

class LocationId {
  final int? id;
  final String? label;
  final String address;
  final bool hasAddressPlaceholder;
  final LatLng latlng;
  final String? tag;
  const LocationId({
    this.id,
    required this.label,
    required this.address,
    required this.latlng,
    this.hasAddressPlaceholder = false,
    this.tag,
  });

  // factory LocationId.fromJson(Map<String, dynamic> json, {String? customTag}) {
  //   final lat = json['latitude'] as String?;
  //   final long = json['longitude'] as String?;
  //   final latLng = LatLng(
  //       double.tryParse(lat ?? "") ?? 0, double.tryParse(long ?? "") ?? 0);
  //   final address = (json['address'] as String?) ?? "";

  //   return LocationId(
  //     id: json['id'] as int?,
  //     label: json['name'] ?? 'Untitled',
  //     address: address.isEmpty ? 'Address Unavailable' : address,
  //     hasAddressPlaceholder: address.trim().isEmpty,
  //     latlng: latLng,
  //     tag: customTag,
  //   );
  // }

  // factory LocationId.fromLocationJson(Map<String, dynamic> json) {
  //   final lat = json['latitude'] as String?;
  //   final long = json['longitude'] as String?;
  //   final latLng = LatLng(
  //       double.tryParse(lat ?? "") ?? 0, double.tryParse(long ?? "") ?? 0);
  //   //assuming address is in parts.
  //   final address = [
  //     json["street_name"],
  //     json["district"],
  //     json["postal_code"],
  //     json["city"],
  //     json["country"]
  //   ].nonNulls.join(', ');
  //   final tag = json['address_type'];
  //   return LocationId(
  //     id: json['id'] as int?,
  //     label: json['name'] ?? 'Untitled',
  //     address: address.trim().isEmpty ? 'Address unavailable' : address.trim(),
  //     hasAddressPlaceholder: address.trim().isEmpty,
  //     latlng: latLng,
  //     tag: tag,
  //   );
  // }
  LocationId copyWith({
    int? id,
    String? label,
    String? address,
    bool? hasAddressPlaceholder,
    LatLng? latlng,
  }) {
    return LocationId(
      id: id ?? this.id,
      label: label ?? this.label,
      address: address ?? this.address,
      hasAddressPlaceholder:
          hasAddressPlaceholder ?? this.hasAddressPlaceholder,
      latlng: latlng ?? this.latlng,
    );
  }

  // Override equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    if (other is! LocationId) return false;

    // Check if the addresses are the same or the locations are within 5 meters
    return (address.trim().isNotEmpty &&
            address.trim() != 'Address unavailable' &&
            (address == other.address)) ||
        _calculateDistance(latlng, other.latlng) <= 5.0;
  }

  @override
  int get hashCode => address.hashCode;

  // Helper method to calculate distance in meters between two LatLng points
  double _calculateDistance(LatLng point1, LatLng point2) {
    const earthRadius = 6371000.0; // meters
    final dLat = _degreesToRadians(point2.latitude - point1.latitude);
    final dLng = _degreesToRadians(point2.longitude - point1.longitude);

    final a = (sin(dLat / 2) * sin(dLat / 2)) +
        (cos(_degreesToRadians(point1.latitude)) *
            cos(_degreesToRadians(point2.latitude)) *
            sin(dLng / 2) *
            sin(dLng / 2));
    final c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) => degrees * (pi / 180);
}
