// import 'package:flutter/material.dart';
// import 'dart:math' as math;
// import 'package:ako_basma/styles/theme.dart';
// import 'package:ako_basma/labels.dart';
// import 'package:solar_icons/solar_icons.dart';

// class CustomFloatingActionButton extends StatefulWidget {
//   final VoidCallback? onPressed;
//   final Widget? child;
//   final String? text;
//   final Color? backgroundColor;
//   final Gradient? backgroundGradient;
//   final List<FABMenuItem>? menuItems;
//   final bool showBottomMenu;

//   const CustomFloatingActionButton({
//     super.key,
//     this.onPressed,
//     this.child,
//     this.text,
//     this.backgroundColor,
//     this.backgroundGradient,
//     this.menuItems,
//     this.showBottomMenu = false,
//   });

//   @override
//   State<CustomFloatingActionButton> createState() =>
//       _CustomFloatingActionButtonState();
// }

// class _CustomFloatingActionButtonState
//     extends State<CustomFloatingActionButton> {
//   bool _isMenuOpen = false;

//   void _toggleMenu() {
//     setState(() {
//       _isMenuOpen = !_isMenuOpen;
//     });
//   }

//   /// Handle menu item tap and close menu
//   void _handleMenuItemTap(VoidCallback? onTap) {
//     _toggleMenu(); // Close menu first
//     onTap?.call(); // Then execute the action
//   }

//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);
//     final colors = theme.extension<AppColors>()!;
//     final textStyles = theme.extension<TextStyles>()!;
//     final isRTL = Directionality.of(context) == TextDirection.rtl;

//     return Stack(
//       children: [
//         // Menu overlay with backdrop
//         if (_isMenuOpen && widget.menuItems != null)
//           Positioned.fill(
//             child: GestureDetector(
//               onTap: _toggleMenu,
//               child: Container(
//                 color: Colors.black.withOpacity(0.3),
//               ),
//             ),
//           ),

//         // Menu items positioned to the left of FAB
//         if (_isMenuOpen && widget.menuItems != null && widget.showBottomMenu)
//           PositionedDirectional(
//             start: isRTL ? 138 : null,
//             bottom: 40,
//             end: isRTL ? null : 138,
//             child: Container(
//               // Parent container for the menu
//               decoration: BoxDecoration(
//                 color: colors.backgroundContainer,
//                 border: Border.all(color: colors.primaryVariant, width: 1),
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               padding: const EdgeInsetsDirectional.symmetric(
//                   vertical: 8, horizontal: 14),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   for (int i = 0; i < widget.menuItems!.length; i++) ...[
//                     // specific menu item
//                     Container(
//                       width: 160,
//                       decoration: BoxDecoration(
//                         color: colors.background,
//                         borderRadius: BorderRadius.circular(8),
//                       ),
//                       child: Material(
//                         color: Colors.transparent,
//                         child: InkWell(
//                           onTap: () =>
//                               _handleMenuItemTap(widget.menuItems![i].onTap),
//                           borderRadius: BorderRadius.circular(8),
//                           child: Container(
//                             padding: const EdgeInsets.symmetric(
//                               horizontal: 14,
//                               vertical: 10,
//                             ),
//                             child: Row(
//                               mainAxisSize: MainAxisSize.max,
//                               children: [
//                                 if (widget.menuItems![i].icon != null)
//                                   Container(
//                                     margin: const EdgeInsetsDirectional.only(
//                                         end: 10),
//                                     child: Icon(
//                                       SolarIconsOutline.usersGroupTwoRounded,
//                                       size: 18,
//                                       color: colors.secondaryText,
//                                     ),
//                                   ),
//                                 Expanded(
//                                   // Expand text to fill remaining space
//                                   child: Text(
//                                     widget.menuItems![i].label,
//                                     style: textStyles.body2.copyWith(
//                                       color: colors.secondaryText,
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                     // Add spacing between menu items except after the last one
//                     if (i != widget.menuItems!.length - 1)
//                       const SizedBox(height: 8),
//                   ],
//                 ],
//               ),
//             ),
//           ),

//         // Main FAB positioned at bottom right
//         PositionedDirectional(
//           bottom: 16,
//           end: 24,
//           child: Transform(
//             alignment: Alignment.center,
//             transform:
//                 Matrix4.rotationZ(-math.pi / 4), // Rotate container 45 degrees
//             child: Container(
//               width: 50,
//               height: 50,
//               decoration: BoxDecoration(
//                 color: widget.backgroundGradient == null
//                     ? (widget.backgroundColor ?? colors.primary)
//                     : null,
//                 gradient: widget.backgroundGradient,
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               child: Material(
//                 color: Colors.transparent,
//                 child: InkWell(
//                   borderRadius: BorderRadius.circular(8),
//                   onTap: widget.menuItems != null && widget.showBottomMenu
//                       ? _toggleMenu
//                       : widget.onPressed,
//                   child: Transform(
//                     alignment: Alignment.center,
//                     transform: Matrix4.rotationZ(
//                         math.pi / 4), // Rotate content back to normal
//                     child: _buildFABContent(colors, textStyles),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   /// Build the content inside the FAB based on provided parameters
//   Widget _buildFABContent(AppColors colors, TextStyles textStyles) {
//     // If custom child is provided, use it
//     if (widget.child != null) {
//       return widget.child!;
//     }

//     // If text is provided, show text
//     if (widget.text != null) {
//       return Center(
//         child: Text(
//           widget.text!,
//           style: textStyles.headline2.copyWith(
//             color: Colors.white,
//             height: 1.0, // Ensure proper text centering
//           ),
//           textAlign: TextAlign.center,
//         ),
//       );
//     }

//     // Default: show plus icon
//     return const Center(
//       child: Icon(
//         Icons.add,
//         size: 28,
//         color: Colors.white,
//       ),
//     );
//   }
// }

// /// Model class for FAB menu items
// class FABMenuItem {
//   final String label;
//   final IconData? icon;
//   final VoidCallback? onTap;

//   const FABMenuItem({
//     required this.label,
//     this.icon,
//     this.onTap,
//   });
// }

// /// Predefined FAB configurations for different screens
// class FABConfigurations {
//   /// Chat screen FAB with new group and new channel options

//   static CustomFloatingActionButton chatScreenFAB({
//     required VoidCallback onNewGroup,
//     required VoidCallback onNewChannel,
//   }) {
//     return CustomFloatingActionButton(
//       showBottomMenu: true,
//       menuItems: [
//         FABMenuItem(
//           label: Labels.newGroup,
//           icon: Icons.group_add,
//           onTap: onNewGroup,
//         ),
//         FABMenuItem(
//           label: Labels.newChannel,
//           icon: Icons.tag,
//           onTap: onNewChannel,
//         ),
//       ],
//     );
//   }

//   /// Home screen FAB with AI functionality
//   /// Uses gradient background and direct action
//   static CustomFloatingActionButton homeScreenFAB({
//     required VoidCallback onAITap,
//     required AppColors colors,
//   }) {
//     return CustomFloatingActionButton(
//       text: Labels.ai,
//       backgroundGradient: colors.primaryGradient,
//       onPressed: onAITap,
//     );
//   }
// }
