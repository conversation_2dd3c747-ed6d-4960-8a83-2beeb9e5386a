import 'dart:math' as math;
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/app_theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// AI Floating Action Button for Home Screen
class AIFloatingActionButton extends StatelessWidget {
  const AIFloatingActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final localization = AppLocalizations.of(context)!;
    final bottomInset =
        MediaQuery.paddingOf(context).bottom; // Dynamic system inset

    return SafeArea(
      bottom: true,
      child: Container(
        margin: EdgeInsetsDirectional.only(
            end: 24,
            bottom: bottomInset + 16 // Dynamic margin instead of fixed 16
            ),
        child: Transform(
          alignment: Alignment.center,
          transform: Matrix4.rotationZ(-math.pi /
              4), // Rotate container 45 degrees following existing pattern
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              // Use gradient background
              gradient: theme.colors.primaryGradient,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                onTap: () {
                  // Navigate to AI chat screen using GoRouter
                  context.push('/ai-chat');
                },
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationZ(
                      math.pi / 4), // Rotate content back to normal
                  child: Center(
                    child: FittedBox(
                      fit: BoxFit
                          .scaleDown, // so that the text doesn't go out of the FAB
                      child: Text(
                        localization.ai,
                        style: theme.textStyles.headline2.copyWith(
                          color: Colors.white,
                          height: 1.0, // Ensure proper text centering
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
