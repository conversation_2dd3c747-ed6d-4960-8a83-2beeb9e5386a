import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

Future<TimeOfDay?> showRoundedTimePicker(
  BuildContext context, {
  TimeOfDay? initialTime,
  String? helpText,
}) {
  // Fall back to current time when no initial value is supplied.
  final TimeOfDay initial = initialTime ?? TimeOfDay.now();

  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;

  return showTimePicker(
    context: context,
    initialTime: initial,
    helpText: helpText,
    builder: (context, child) {
      return Theme(
        data: Theme.of(context).copyWith(
          timePickerTheme: TimePickerThemeData(
            backgroundColor: colors.background,
            hourMinuteShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color: colors.primaryVariant,
                width: 1,
              ),
            ),
            dayPeriodShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            dayPeriodColor: WidgetStateColor.resolveWith((states) =>
                states.contains(WidgetState.selected)
                    ? colors.primary
                    : colors.primaryVariant),
            dayPeriodTextColor: WidgetStateColor.resolveWith((states) =>
                states.contains(WidgetState.selected)
                    ? Theme.of(context).colorScheme.onPrimary
                    : colors.secondaryText),
            hourMinuteColor: WidgetStateColor.resolveWith((states) =>
                states.contains(WidgetState.selected)
                    ? colors.primaryVariant
                    : colors.backgroundContainer),
            hourMinuteTextColor: WidgetStateColor.resolveWith((states) =>
                states.contains(WidgetState.selected)
                    ? colors.primaryText
                    : colors.secondaryText),
            // Increased font size for hour and minute text
            hourMinuteTextStyle: const TextStyle(fontSize: 54),
            dialBackgroundColor: colors.backgroundContainer,
            dialHandColor: colors.primary,
            dialTextColor: WidgetStateColor.resolveWith((states) =>
                states.contains(WidgetState.selected)
                    ? Theme.of(context).colorScheme.onPrimary
                    : colors.secondaryText),
            entryModeIconColor: colors.primary,
          ),
          colorScheme: ColorScheme(
            brightness: Brightness.light,
            primary: colors.primary,
            onPrimary: colors.primaryText,
            secondary: colors.secondary,
            onSecondary: colors.secondaryText,
            error: colors.error,
            onError: colors.error,
            background: colors.background,
            onBackground: colors.primaryText,
            surface: colors.primaryVariant,
            onSurface: colors.primaryText,
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: colors.primary,
            ),
          ),
        ),
        child: child!,
      );
    },
  );
}
