import 'dart:math';

import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:jumping_dot/jumping_dot.dart';

import '../../styles/ui.dart';

class PrimaryButton extends StatefulWidget {
  const PrimaryButton({
    super.key,
    required this.label,
    required this.onTap,
    this.style,
    this.textStyle,
    this.wrapBottomPadding = false,
    this.expand = true,
    this.padding =
        const EdgeInsetsDirectional.symmetric(vertical: 8, horizontal: 16),
  })  : isAsync = false,
        onPressed = null;

  const PrimaryButton.async({
    super.key,
    required this.label,
    required this.onPressed,
    this.style,
    this.textStyle,
    this.wrapBottomPadding = false,
    this.expand = true,
    this.padding =
        const EdgeInsetsDirectional.symmetric(vertical: 8, horizontal: 16),
  })  : isAsync = true,
        onTap = null;

  final String label;
  final void Function()? onTap;
  final Future<void> Function()? onPressed;
  final ButtonStyle? style;
  final TextStyle? textStyle;
  final bool expand;
  final EdgeInsetsGeometry padding;
  final bool isAsync;
  final bool wrapBottomPadding;

  @override
  State<PrimaryButton> createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<PrimaryButton> {
  bool _loading = false;

  @override
  Widget build(BuildContext context) {
    if (widget.wrapBottomPadding) {
      return Padding(
        padding: EdgeInsetsDirectional.fromSTEB(
            24, 0, 24, max(MediaQuery.paddingOf(context).bottom, 24)),
        child: _buildButton(),
      );
    }
    return _buildButton();
  }

  Widget _buildButton() {
    final colors = AppColors.of(context);
    final handleBehavior = _loading
        ? null
        : widget.isAsync
            ? widget.onPressed != null
                ? _handleAsyncPress
                : null
            : widget.onTap;

    final buttonContent = Stack(
      alignment: Alignment.center,
      children: [
        // Text content (visible when not loading)
        Visibility(
          visible: !_loading,
          maintainSize: true, // Keep the size of the text even when invisible
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
            padding: widget.padding,
            child: Text(
              widget.label,
              style: widget.textStyle,
            ),
          ),
        ),
        // Loading indicator (visible when loading)
        Visibility(
          visible: _loading,
          maintainSize: true,
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(
                (widget.padding.horizontal ?? 0) + 2,
                (widget.padding.vertical ?? 0) + 2,
                (widget.padding.horizontal ?? 0) + 2,
                (widget.padding.vertical ?? 0) +
                    2), // Adjust padding for visual alignment of dots
            child: const DotsLoadingIndicator(),
          ),
        ),
      ],
    );

    final button = ElevatedButton(
      onPressed: handleBehavior,
      style:
          // can work for outlined buttons(if there are any) too.
          // take an extra argument of buttonType.

          widget.style ??
              AppButtonStyle.primaryButtonStyle(context).copyWith(
                  backgroundColor: _loading
                      ? WidgetStateColor.resolveWith((_) => colors
                          .primaryVariant) // Assuming primaryVariant is your desired loading background color
                      : null),
      child: Center(child: buttonContent),
    );

    return widget.expand ? Row(children: [Expanded(child: button)]) : button;
  }

  Widget _buildLoadingIndicator() {
    // This method is no longer needed as the loader is part of the Stack above
    return const SizedBox.shrink();
  }

  Future<void> _handleAsyncPress() async {
    setState(() => _loading = true);
    try {
      await widget.onPressed?.call();
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }
}
