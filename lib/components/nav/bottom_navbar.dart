import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

import 'package:ako_basma/l10n/generated/app_localizations.dart';

import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/constants/assets.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.sizeOf(context).width;
    final navBarWidth = screenWidth > 360 ? 328.0 : screenWidth - 32;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    return Container(
      margin: const EdgeInsetsDirectional.only(bottom: 28, start: 16, end: 16),
      width: navBarWidth,
      height: 72,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(
        //     color: colors(context).shadow.withOpacity(0.10), width: 1),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: Offset(0, 0),
          ),
        ],
      ),
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildNavItem(
            context: context,
            path: 'home',
            icon: SolarIconsOutline.homeAngle,
            selectedIcon: SolarIconsBold.homeAngle,
            label: AppLocalizations.of(context)!.home,
            index: 0,
          ),
          _buildNavItem(
            context: context,
            path: 'Chat',
            icon: SolarIconsOutline.dialog,
            selectedIcon: SolarIconsBold.dialog,
            label: AppLocalizations.of(context)!.chat,
            index: 1,
          ),
          _buildNavItem(
            context: context,
            path: 'tasks',
            icon: Iconsax.menu_board_copy,
            selectedIcon: Iconsax.menu_board,
            label: AppLocalizations.of(context)!.tasks,
            index: 2,
          ),
          _buildNavItem(
            context: context,
            path: 'profile',
            label: AppLocalizations.of(context)!.profile,
            index: 3,
            icon: Iconsax.profile_circle_copy,
            selectedIcon: Iconsax.profile_circle,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    IconData? icon,
    IconData? selectedIcon,
    required String path,
    required String label,
    required int index,
    Widget? customIcon,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final isSelected = currentIndex == index;
    final iconColor = isSelected ? colors.primary : colors.secondaryText;
    final textColor = isSelected ? colors.primary : colors.tertiaryText;

    return Expanded(
      child: InkWell(
        onTap: () => onTap(index),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              customIcon ??
                  Icon(
                    (isSelected ? selectedIcon ?? icon : icon) ??
                        FontAwesomeIcons.circleQuestion,
                    color: iconColor,
                    size: 24,
                  ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 4),
                child: Text(
                  label,
                  style: textStyles.buttonSmall.copyWith(
                    color: textColor,
                  ),
                  textScaler: TextScaler.noScaling,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
