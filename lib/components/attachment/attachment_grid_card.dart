import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/styles/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:pdfrx/pdfrx.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class AttachmentGridCard extends StatefulWidget {
  final String? filePath;
  final String? url;
  final double width;
  final double height;

  const AttachmentGridCard({
    super.key,
    this.filePath,
    this.url,
    this.width = 150,
    this.height = 150,
  }) : assert(filePath != null || url != null,
            'Either filePath or url must be provided');

  @override
  State<AttachmentGridCard> createState() => _AttachmentGridCardState();
}

class _AttachmentGridCardState extends State<AttachmentGridCard> {
  Widget? _thumbnail;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadThumbnail();
  }

  @override
  void didUpdateWidget(AttachmentGridCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filePath != widget.filePath || oldWidget.url != widget.url) {
      _loadThumbnail();
    }
  }

  Future<void> _loadThumbnail() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _thumbnail = null;
    });

    try {
      Widget thumbnail;
      if (widget.filePath != null) {
        thumbnail = await _buildLocalThumbnail(widget.filePath!);
      } else if (widget.url != null) {
        thumbnail = await _buildRemoteThumbnail(widget.url!);
      } else {
        thumbnail = _buildPlaceholder();
      }

      if (!mounted) return;
      setState(() {
        _thumbnail = thumbnail;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error building thumbnail: $e');
      if (!mounted) return;
      setState(() {
        _thumbnail = _buildPlaceholder();
        _isLoading = false;
      });
    }
  }

  Future<Widget> _buildLocalThumbnail(String filePath) async {
    final extension = path.extension(filePath).toLowerCase();

    if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
      return Image.file(
        File(filePath),
        fit: BoxFit.cover,
        width: widget.width,
        height: widget.height,
        cacheWidth: (widget.width * 3).toInt(),
        cacheHeight: (widget.height * 3).toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } else if (extension == '.pdf') {
      return _buildPdfThumbnail(filePath);
    } else {
      return _buildDocumentThumbnail(extension);
    }
  }

  Future<Widget> _buildRemoteThumbnail(String url) async {
    final extension = path.extension(url).toLowerCase();

    if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
      return Image.network(
        url,
        fit: BoxFit.cover,
        width: widget.width,
        height: widget.height,
        cacheWidth: (widget.width * 3).toInt(),
        cacheHeight: (widget.height * 3).toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } else if (extension == '.pdf') {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/temp_${DateTime.now().millisecondsSinceEpoch}.pdf');

      try {
        final response = await http.get(Uri.parse(url));
        await tempFile.writeAsBytes(response.bodyBytes);
        return _buildPdfThumbnail(tempFile.path);
      } catch (e) {
        debugPrint('Error downloading PDF: $e');
        return _buildPlaceholder();
      }
    } else {
      return _buildDocumentThumbnail(extension);
    }
  }

  Future<Widget> _buildPdfThumbnail(String filePath) async {
    try {
      // Using pdfrx API - Create a simple PDF viewer widget for thumbnail
      // Since pdfrx doesn't provide direct image rendering like pdfx,
      // we'll create a minimal PdfPageView widget
      final document = await PdfDocument.openFile(filePath);

      return SizedBox(
        width: widget.width,
        height: widget.height,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: PdfPageView(
            document: document,
            pageNumber: 1,
            alignment: Alignment.center,
          ),
        ),
      );
    } catch (e) {
      debugPrint('Error rendering PDF thumbnail: $e');
      return _buildPlaceholder();
    }
  }

  Widget _buildDocumentThumbnail(String extension) {
    IconData iconData;
    switch (extension) {
      case '.doc':
      case '.docx':
        iconData = Icons.description_rounded;
        break;
      case '.xls':
      case '.xlsx':
        iconData = Icons.table_chart_rounded;
        break;
      case '.ppt':
      case '.pptx':
        iconData = Icons.slideshow_rounded;
        break;
      case '.txt':
        iconData = Icons.text_snippet_rounded;
        break;
      default:
        iconData = Icons.insert_drive_file_rounded;
    }

    final theme = AppTheme.of(context);
    return Container(
      color: theme.colors.background,
      child: Icon(
        iconData,
        size: 48,
        color: theme.colors.secondaryText,
      ),
    );
  }

  Widget _buildPlaceholder() {
    final theme = AppTheme.of(context);
    return Container(
      color: theme.colors.background,
      child: Icon(
        Icons.broken_image,
        size: 48,
        color: theme.colors.disabled,
      ),
    );
  }

  Future<void> _openFile() async {
    try {
      if (widget.url != null) {
        final uri = Uri.parse(widget.url!);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri,
              mode: LaunchMode
                  .inAppWebView); // LaunchMode.inAppWebView: Opens URLs in an in-app web view (perfect for PDFs)
        }
      } else if (widget.filePath != null) {
        final uri = Uri.file(widget.filePath!);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri,
              mode: LaunchMode
                  .platformDefault); // // LaunchMode.platformDefault: Uses the platform's default behavior for local files
        }
      }
    } catch (e) {
      debugPrint('Error opening file: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: _openFile,
      child: Container(
        // clipBehavior: Clip.hardEdge,
        // padding: const EdgeInsets.all(8),
        // width: 150,
        // height: 170,

        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Align(
              alignment: Alignment.center,
              child: AnimatedContainer(
                clipBehavior: Clip.hardEdge,
                height: widget.height,
                width: widget.width,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: _isLoading ? Colors.transparent : Colors.white,
                  border:
                      Border.all(width: 2, color: theme.colors.primaryVariant),
                  borderRadius: BorderRadius.circular(8),
                ),
                duration: 300.milliseconds,
                child: _isLoading
                    ? const Center(child: DotsLoadingIndicator())
                    : (_thumbnail ?? _buildPlaceholder())
                        .animate()
                        .fadeIn(duration: 300.milliseconds),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Iconsax.document_text_1_copy,
                  color: theme.colors.tertiaryText,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    path.basename(widget.url ?? widget.filePath ?? ''),
                    style: theme.textStyles.body2.copyWith(
                      color: theme.colors.tertiaryText,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
