import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

enum AttachmentState {
  uploading,
  completed,
  error,
  uploaded,
  viewOnly,
}

class AttachmentTile extends StatelessWidget {
  final String fileName;
  final String fileSize;
  final AttachmentState state;
  final double? progress;
  final String? errorMessage;
  final VoidCallback? onDelete;
  final VoidCallback? onViewLocal;
  final VoidCallback? onTryAgain;
  final String? remoteUrl;

  const AttachmentTile({
    super.key,
    required this.fileName,
    required this.fileSize,
    required this.state,
    this.progress,
    this.errorMessage,
    this.onDelete,
    this.onViewLocal,
    this.onTryAgain,
    this.remoteUrl,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final tintColor = switch (state) {
      AttachmentState.error => colors.error,
      AttachmentState.uploaded => colors.secondaryText,
      AttachmentState() => colors.primaryText,
    };
    Widget buildProgressIndicator() {
      final color = switch (state) {
        AttachmentState.completed => colors.success,
        AttachmentState.error => colors.error,
        _ => colors.primary,
      };
      return Row(
        children: [
          Expanded(
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: colors.disabled,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ),
          SizedBox(
            width: 25,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '${((progress ?? 0) * 100).toInt()}%',
                style: textStyles.body3.copyWith(
                  fontSize: 10,
                  color: colors.secondaryText,
                ),
                textAlign: TextAlign.end,
              ),
            ),
          )
        ],
      );
    }

    Widget buildActionWidget() {
      switch (state) {
        case AttachmentState.completed:

          // const SizedBox(width: 8),
          return Icon(
            Iconsax.tick_circle,
            color: colors.success,
            size: 24,
          );
        case AttachmentState.viewOnly:
          return Icon(
            SolarIconsOutline.download,
            color: colors.primaryText,
            size: 20,
          );

        case AttachmentState():
          return GestureDetector(
            onTap: onDelete,
            child: Icon(
              SolarIconsOutline.trashBinMinimalistic_2,
              color: state == AttachmentState.error
                  ? colors.error
                  : colors.secondaryText,
              size: 24,
            ),
          );
      }
    }

    Widget buildTitle() {
      switch (state) {
        case AttachmentState.error:
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                errorMessage ?? 'Upload Failed, Please Try Again',
                style: textStyles.buttonSmall.copyWith(
                  fontSize: 14,
                  color: colors.error,
                ),
              ),
              Text(
                fileName,
                style: textStyles.body3.copyWith(
                  color: colors.error,
                ),
              ),
              Text(
                'Try Again',
                style: textStyles.body3.copyWith(
                  color: colors.error,
                ),
              ),
            ],
          );
        case AttachmentState():
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                fileName,
                style: textStyles.buttonSmall.copyWith(
                  fontSize: 14,
                  color: tintColor,
                ),
              ),
              Text(
                fileSize,
                style: textStyles.body3.copyWith(
                  color: state == AttachmentState.uploaded
                      ? colors.secondaryText
                      : colors.tertiaryText,
                ),
              ),
              if (state == AttachmentState.uploaded)
                Text(
                  'Click to View',
                  style: textStyles.buttonSmall.copyWith(
                    color: colors.primary,
                  ),
                ),
            ],
          );
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.strokeColor,
        ),
      ),
      child: AnimatedSize(
        duration: 300.milliseconds,
        alignment: Alignment.bottomCenter,
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Iconsax.document_text_1_copy,
                  color: tintColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: buildTitle(),
                ),
                const SizedBox(width: 8),
                buildActionWidget(),
              ],
            ),
            if (state == AttachmentState.uploading ||
                state == AttachmentState.completed)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: buildProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}
