import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:solar_icons/solar_icons.dart';

class ScrollableAttachmentList extends StatelessWidget {
  final List<File> attachments;
  final Function(int) onRemoveAttachment;
  final EdgeInsetsGeometry? margin;
  final bool showCount;

  const ScrollableAttachmentList({
    super.key,
    required this.attachments,
    required this.onRemoveAttachment,
    this.margin,
    this.showCount = true,
  });

  @override
  Widget build(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      margin: margin ?? const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // proper scrolling
        children: [
          // Section header with attachment count
          if (showCount)
            Text(
              '${localization.attachment} (${attachments.length})',
              style: textStyles.body2.copyWith(
                color: colors.primaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          if (showCount) const SizedBox(height: 8),

          // Attachment tiles
          ...List.generate(attachments.length, (index) {
            final file = attachments[index];
            final fileName = file.path.split('/').last;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colors.background,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colors.strokeColor),
              ),
              child: Row(
                children: [
                  // File icon
                  Icon(
                    SolarIconsOutline.paperclip,
                    size: 20,
                    color: colors.primary,
                  ),
                  const SizedBox(width: 8),

                  // File name
                  Expanded(
                    child: Text(
                      fileName,
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Remove button
                  InkWell(
                    onTap: () => onRemoveAttachment(index),
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.close,
                        size: 20,
                        color: colors.error,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
