import 'dart:math';

import 'package:ako_basma/components/attachment/attachment_grid_card.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/app_theme.dart';
// import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:url_launcher/url_launcher.dart';

class AttachmentPlaceholderCard extends StatelessWidget {
  const AttachmentPlaceholderCard(
      {super.key,
      this.preset,
      this.label,
      this.icon,
      this.filePath,
      this.url,
      this.previewWidth,
      this.previewHeight,
      this.onTap,
      this.onDelete});

  /// national_id, contract, other,
  final String? preset;
  final String? label;
  final dynamic icon;
  final String? filePath;
  final String? url;
  final double? previewWidth;
  final double? previewHeight;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  // final Attac

  @override
  Widget build(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final theme = AppTheme.of(context);
    final resIcon = icon ??
        switch (preset) {
          'national_id' => SolarIconsOutline.userId,
          'contract' => SolarIconsOutline.document,
          'other' => SolarIconsOutline.upload,
          String() => SolarIconsOutline.upload,
          null => SolarIconsOutline.upload,
        };
    final resLabel = label ??
        switch (preset) {
          'national_id' => 'National ID',
          'contract' => 'Contract',
          'other' => 'Other documents',
          String() => 'Attachment',
          null => 'Attachment',
        };
    final data = filePath ?? url;
    return InkWell(
      borderRadius: BorderRadius.circular(6),
      onTap: () async {
        unfocus();
        if (data != null) {
          final uri = filePath != null ? Uri.file(filePath!) : Uri.parse(url!);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri);
          }
        } else {
          if (onTap != null) {
            onTap!();
          }
        }
      },
      child: preset == 'upload'
          ? Stack(
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: theme.colors.strokeColor,
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 44,
                        height: 44,
                        child: Center(
                          child: Icon(
                            SolarIconsOutline.upload,
                            size: 40,
                            color: theme.colors.secondaryText,
                          ),
                        ),
                      ),
                      // const SizedBox(height: 12),
                      // Text(
                      //   localization.clickToUpload,
                      //   style: theme.textStyles.body2.copyWith(
                      //     color: theme.colors.primary,
                      //   ),
                      // ),
                      const SizedBox(height: 8),
                      Text(
                        localization.otherDocuments,
                        style: theme.textStyles.body3.copyWith(
                          color: theme.colors.secondaryText,
                        ),
                      )
                    ],
                  ),
                ),
              ],
            )
          : Stack(
              children: [
                AnimatedContainer(
                  duration: 300.milliseconds,
                  clipBehavior: Clip.hardEdge,
                  padding: data != null
                      ? const EdgeInsets.symmetric(horizontal: 8, vertical: 8)
                      : const EdgeInsets.symmetric(
                          horizontal: 40, vertical: 20),
                  constraints: const BoxConstraints(minHeight: 114),
                  // margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                  decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                          color: data != null
                              ? theme.colors.backgroundContainer
                              : theme.colors.strokeColor)),

                  child: Center(
                    child: data != null
                        ? LayoutBuilder(builder: (context, constraints) {
                            return AttachmentGridCard(
                              filePath: filePath,
                              url: url,
                              width: min(constraints.maxWidth, 150),
                            );
                          })
                        : Column(
                            children: [
                              if (resIcon is IconData)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Icon(
                                    resIcon,
                                    size: 40,
                                    color: theme.colors.secondaryText,
                                  ),
                                )
                              else if (resIcon is Widget)
                                resIcon,
                              Text(
                                resLabel,
                                style: theme.textStyles.body.copyWith(
                                  color: theme.colors.secondaryText,
                                ),
                              )
                            ],
                          ),
                  ),
                ),
                if (onDelete != null && data != null)
                  PositionedDirectional(
                    top: 4,
                    end: 10,
                    child: InkWell(
                      onTap: onDelete!,
                      child: Container(
                        height: 18,
                        width: 18,
                        decoration: ShapeDecoration(
                          shape: const CircleBorder(),
                          color: theme.colors.error.withOpacity(0.9),
                          shadows: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 14,
                            applyTextScaling: false,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
    );
  }
}
