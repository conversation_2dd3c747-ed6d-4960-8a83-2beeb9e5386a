import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ako_basma/util/location/location.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:geolocator/geolocator.dart'; // Add geolocator import for LocationAccuracy

/// blurred Google Maps
class BlurredMapBackground extends StatefulWidget {
  final Widget child;
  final double blurIntensity;
  final BorderRadius? borderRadius;
  final double height;
  final bool enableBlur; // control blur
  final double overlayOpacity; // control overlay opacity

  const BlurredMapBackground({
    super.key,
    required this.child,
    this.blurIntensity = 2.0,
    this.borderRadius,
    this.height = 200.0,
    this.enableBlur = true,
    this.overlayOpacity = 0.85,
  });

  @override
  State<BlurredMapBackground> createState() => _BlurredMapBackgroundState();
}

class _BlurredMapBackgroundState extends State<BlurredMapBackground> {
  GoogleMapController? _mapController;
  LatLng? _currentLocation;
  bool _isLoadingLocation = true;

  @override
  void initState() {
    super.initState();
    // Use regional default location instead of London
    _currentLocation = getRegionalDefaultLocation();
    _getCurrentLocation();
  }

  /// Get current device location
  Future<void> _getCurrentLocation() async {
    try {
      setState(() {
        _isLoadingLocation = true;
      });

      final location = await getCurrentLocation(
        accuracy:
            LocationAccuracy.medium, // Use medium accuracy for background maps
        timeLimit: const Duration(seconds: 15),
      );

      if (location != null && mounted) {
        setState(() {
          _currentLocation = location;
          _isLoadingLocation = false;
        });

        // Move camera to current location if map controller is available
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: location,
              zoom: 16.0,
            ),
          ),
        );
      } else {
        // No location available, use regional default and stop loading
        if (mounted) {
          setState(() {
            _isLoadingLocation = false;
            _currentLocation = getRegionalDefaultLocation();
          });
        }
      }
    } catch (e) {
      // Error getting location - use regional default location
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
          _currentLocation = getRegionalDefaultLocation();
        });
      }
    }
  }

  /// Configure map style based on theme (dark/light)
  String? _getMapStyle(BuildContext context) {
    if (Theme.of(context).brightness == Brightness.dark) {
      return '''
        [
          {
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#212121"
              }
            ]
          },
          {
            "elementType": "labels.icon",
            "stylers": [
              {
                "visibility": "off"
              }
            ]
          },
          {
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#757575"
              }
            ]
          },
          {
            "elementType": "labels.text.stroke",
            "stylers": [
              {
                "color": "#212121"
              }
            ]
          },
          {
            "featureType": "administrative",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#757575"
              }
            ]
          },
          {
            "featureType": "road",
            "elementType": "geometry.fill",
            "stylers": [
              {
                "color": "#2c2c2c"
              }
            ]
          },
          {
            "featureType": "water",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#000000"
              }
            ]
          }
        ]
      ''';
    } else {
      return null; // Use default light style
    }
  }

  /// Handle map creation and apply styling
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    // Apply map style for theme
    final style = _getMapStyle(context);
    if (style != null) {
      controller.setMapStyle(style);
    }

    // Move camera to current location if available
    if (_currentLocation != null) {
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentLocation!,
            zoom: 16.0,
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius,
        color: colors.surface,
      ),
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: Stack(
          children: [
            // Google Map Layer
            Positioned.fill(
              child: GoogleMap(
                onMapCreated: _onMapCreated,
                initialCameraPosition: CameraPosition(
                  target: _currentLocation!,
                  zoom: 16.0,
                ),
                markers: const {}, // No markers as requested
                style: _getMapStyle(context),
                myLocationEnabled: false, // Disable location marker
                myLocationButtonEnabled: false,
                zoomControlsEnabled: false,
                mapToolbarEnabled: false,
                compassEnabled: false,
                rotateGesturesEnabled: false,
                scrollGesturesEnabled:
                    false, // Disable interaction for background
                zoomGesturesEnabled: false,
                tiltGesturesEnabled: false,
                mapType: MapType.normal,
                liteModeEnabled: false,
                buildingsEnabled: true,
                trafficEnabled: false,
              ),
            ),

            // Blur Effect Layer
            if (widget.enableBlur)
              Positioned.fill(
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: widget.blurIntensity,
                    sigmaY: widget.blurIntensity,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xFF151515)
                          .withOpacity(widget.overlayOpacity), // Subtle overlay
                      borderRadius: widget.borderRadius,
                    ),
                  ),
                ),
              )
            else
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: colors.surface
                        .withOpacity(widget.overlayOpacity), // Subtle overlay
                    borderRadius: widget.borderRadius,
                  ),
                ),
              ),

            // Content Layer (child widget)
            Positioned.fill(
              child: widget.child,
            ),
          ],
        ),
      ),
    );
  }
}
