import 'dart:async';

import 'package:ako_basma/providers/auth/login_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gif_view/gif_view.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({
    super.key,
    required this.onComplete,
    required this.onFadeStart,
    required this.contentReady,
  });

  /// called when the anim is completed.
  final VoidCallback onComplete;

  final VoidCallback contentReady;

  /// called when the anim is done halfway and logo starts fading out
  final VoidCallback onFadeStart;
  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  late GifController _controller;
  late final ProviderSubscription<LoginStates>? sub;
  final halfAnimCompleter = Completer<void>();
  bool _isInitialized = false;

  static const _frameCount = 61;
  static const _halfFrame = 31;
  static const _fadeStartFrame = 40;

  @override
  void initState() {
    super.initState();
    _controller = GifController();

    print('playing anim');

    // Initialize asynchronously to prevent blocking
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });
  }

  void _initializeApp() async {
    if (_isInitialized) return;
    _isInitialized = true;

    try {
      await Future.wait<void>([
        halfAnimCompleter.future,
        waitForLoginStateResolved().then((_) {
          print('initializer completed with $_');
        })
      ]);

      if (mounted) {
        _resumeAnim();
      }
    } catch (e) {
      print('Error in splash initialization: $e');
      if (mounted) {
        _resumeAnim();
      }
    }
  }

  void _resumeAnim() {
    if (mounted) {
      _controller.play(initialFrame: _halfFrame);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      // Match background color with theme to prevent flicker
      backgroundColor: isDark ? Colors.black : Colors.white,
      body: Center(
        child: GifView.asset(
          'assets/images/splash/splash-${isDark ? 'dark' : 'light'}.gif',
          controller: _controller,
          autoPlay: true,
          onFrame: (frame) {
            if (frame == _fadeStartFrame - 5) {
              widget.contentReady();
            }
            if (frame == _halfFrame) {
              _controller.pause();
              if (!halfAnimCompleter.isCompleted) {
                halfAnimCompleter.complete();
              }
            }
            if (frame == _fadeStartFrame) {
              widget.onFadeStart();
            }
          },
          onFinish: () {
            widget.onComplete();
          },
          height: size.height,
          width: size.width,
          frameRate: 60,
          fit: BoxFit.cover,
          loop: false,
        ),
      ),
    );
  }

  Future<LoginStates> waitForLoginStateResolved() {
    final completer = Completer<LoginStates>();
    sub = ref.listenManual<LoginStates>(
      loginStateProvider,
      (previous, next) {
        if (next != LoginStates.waiting && !completer.isCompleted) {
          completer.complete(next);
          sub?.close();
        }
      },
    );
    final current = ref.read(loginStateProvider);
    if (current != LoginStates.waiting && !completer.isCompleted) {
      completer.complete(current);
      sub?.close();
    }
    return completer.future;
  }

  @override
  void dispose() {
    sub?.close();
    _controller.dispose();
    super.dispose();
  }
}
