import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/create_project.dart';
import 'package:ako_basma/screens/tasks/components/create_task.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/screens/tasks/components/task_search.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/screens/tasks/components/project_selection_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/screens/tasks/components/task_details/task_details_popup.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:ako_basma/components/FAB/ai_floating_action_button.dart';

import 'package:solar_icons/solar_icons.dart';

class Tasks extends StatefulWidget {
  const Tasks({super.key});

  @override
  State<Tasks> createState() => _TasksState();
}

class _TasksState extends State<Tasks> {
  bool _hasProject = false;
  bool _hasTasks = false;
  bool _showCreateTaskScreen =
      false; // New state to control the create task screen
  int _selectedProjectIndex = 0;
  final List<String> _projectList = [
    'Ako Basma',
    'Ako Basma',
    'Ako Basma',
    'Ako Basma',
  ];

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main content
        Column(
          children: [
            _buildAppBar(context),
            Expanded(
              child: _buildBody(context),
            ),
          ],
        ),

        // AI Floating Action Button
        const PositionedDirectional(
          bottom: 0,
          end: 0,
          child: AIFloatingActionButton(),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      color: colors.background,
      padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
      child: SafeArea(
        bottom: false,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left side – Project selector
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Project selector
                  InkWell(
                    onTap: () async {
                      showModalBottomSheet<int>(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        useRootNavigator: true,
                        builder: (context) => ProjectSelectionPopup(
                          selectedIndex: _selectedProjectIndex,
                          projects: _projectList,
                          onSelected: (index) {
                            setState(() {
                              _selectedProjectIndex = index;
                            });
                          },
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            _hasProject
                                ? _projectList[_selectedProjectIndex]
                                : localization.allProjects,
                            style: textStyles.headline4.copyWith(
                              color: colors.primaryText,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                            margin: const EdgeInsetsDirectional.only(start: 4)),
                        Icon(
                          HugeIcons.strokeRoundedArrowDown01,
                          color: colors.primary,
                          size: 24,
                        ),
                      ],
                    ),
                  ),

                  // Small subtitle – "Manage Projects / Manage Tasks"
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 10),
                    child: Text(
                      _hasProject && !_showCreateTaskScreen
                          ? 'Manage Tasks'
                          : localization.manageProject,
                      style: textStyles.body3.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Right side – Add button
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(6.67),
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
              ),
              child: IconButton(
                iconSize: 18,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () {
                  if (!_hasProject) {
                    _showCreateProjectPopup(context);
                  } else {
                    _showAddTaskPopup(context);
                  }
                },
                icon: Icon(
                  Iconsax.add_copy,
                  size: 24,
                  color: colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showCreateProjectPopup(BuildContext context) async {
    final bool? projectCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => CreateProject(
        onBack: () => Navigator.pop(ctx),
        onProjectCreated: () {
          // legacy callback (not used now)
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    if (projectCreated == true && mounted) {
      setState(() {
        _hasProject = true;
        _showCreateTaskScreen = true;
      });
    }
  }

  Future<void> _showAddTaskPopup(BuildContext context) async {
    final bool? taskCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => AddTaskPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    if (taskCreated == true && mounted) {
      setState(() {
        _hasTasks = true;
        _showCreateTaskScreen = false;
      });
    }
  }

  // Method to show task search popup
  Future<void> _showTaskSearchPopup(BuildContext context) async {
    // Dismiss keyboard before showing popup to prevent keyboard issues
    FocusScope.of(context).unfocus();

    await showAdaptivePopup(
      context,
      (ctx, sc) => TaskSearchPopup(sc: sc),
      isDismissible: true,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 20,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    if (!_hasProject) {
      return Container(
        color: colors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                SolarIconsBold.serverMinimalistic,
                size: 68,
                color: colors.tertiaryText,
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 16),
              ),
              Text(
                localization.noProjectFound,
                style: textStyles.headline3.copyWith(
                  color: colors.primaryText,
                ),
              ),
              const SizedBox(
                height: 14,
              ),
              Text(
                localization.pleaseCreateAProject,
                style: textStyles.body3.copyWith(
                  color: colors.secondaryText,
                ),
              ),
              const SizedBox(
                height: 26,
              ),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(110, 0, 110, 0),
                height: 40,
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateProjectPopup(context),
                  icon: Icon(
                    Iconsax.add_copy,
                    size: 24,
                    color: theme.colorScheme.onPrimary,
                  ),
                  label: Text(
                    localization.createProject,
                    style: textStyles.buttonMedium.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontSize: 16,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                    minimumSize: const Size.fromHeight(40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (_showCreateTaskScreen) {
      return CreateTask(
        showAppBar: false,
        onTaskCreated: () {
          setState(() {
            _hasTasks = true;
            _showCreateTaskScreen = false;
          });
        },
      );
    } else if (!_hasTasks) {
      return Container(
        color: colors.background,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/images/server.svg',
                width: 64,
                height: 64,
                colorFilter: ColorFilter.mode(
                  colors.tertiaryText,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                localization.noTasksFound,
                style: textStyles.body.copyWith(
                  color: colors.primaryText,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                localization.pleaseCreateATask,
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(80, 0, 80, 0),
                child: ElevatedButton.icon(
                  onPressed: () => _showAddTaskPopup(context),
                  icon: Icon(Iconsax.add_copy,
                      size: 20, color: theme.colorScheme.onPrimary),
                  label: Text(
                    localization.createTask,
                    style: textStyles.buttonMedium.copyWith(
                      color: theme.colorScheme.onPrimary,
                      fontSize: 16,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                    minimumSize: const Size(double.infinity, 48),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      return Container(
        color: colors.background,
        child: Column(
          children: [
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 4),
              child: CustomSearchBar(
                hintText: localization.searchForATask,
                bgColor: colors.backgroundContainer,
                margin: EdgeInsets.zero,
                readOnly:
                    true, // Make search bar read-only since we handle taps
                onTap: () => _showTaskSearchPopup(context),
                onChanged: (value) {
                  // Search functionality handled by popup
                },
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 8),
                itemCount: 5,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      // Dismiss keyboard before showing popup to prevent keyboard issues
                      FocusScope.of(context).unfocus();

                      showAdaptivePopup(
                        context,
                        (ctx, sc) => TaskDetailsPopup(
                          onBack: () => Navigator.pop(ctx),
                        ),
                        isDismissible: true,
                        scrollable: true,
                        contentPadding: EdgeInsets.zero,
                        topRadius: 0,
                        fullScreen: true,
                        useRootNavigator: true,
                      );
                    },
                    child: TaskCard(
                      margin: EdgeInsetsDirectional.only(
                        top: index == 0 ? 14 : 8,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    }
  }
}
