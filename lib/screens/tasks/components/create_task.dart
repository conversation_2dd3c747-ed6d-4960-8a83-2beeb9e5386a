import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/tasks/components/add_task_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class CreateTask extends StatelessWidget {
  final VoidCallback? onBack;
  final VoidCallback? onTaskCreated;
  final bool showAppBar;

  const CreateTask({
    super.key,
    this.onBack,
    this.onTaskCreated,
    this.showAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showAppBar) _buildAppBar(context),
        Expanded(
          child: _buildBody(context),
        ),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      color: colors.background,
      padding: const EdgeInsetsDirectional.fromSTEB(20, 16, 20, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left side - Project title and manage text
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    localization.allProjects,
                    style: TextStyle(
                      color: colors.primaryText,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 4),
                  ),
                  Icon(
                    SolarIconsOutline.altArrowDown,
                    color: colors.primary,
                    size: 24,
                  ),
                ],
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 4),
              ),
              Text(
                localization.manageProject,
                style: TextStyle(
                  color: colors.secondaryText,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          // Right side - Add button
          Container(
            decoration: BoxDecoration(
              color: colors.backgroundContainer,
              borderRadius: BorderRadius.circular(6.67),
              border: Border.all(
                color: colors.strokeColor,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => _showAddTaskPopup(context),
              icon: Icon(
                Iconsax.add_copy,
                size: 24,
                color: colors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddTaskPopup(BuildContext context) async {
    final bool? taskCreated = await showAdaptivePopup<bool>(
      context,
      (ctx, sc) => AddTaskPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );

    // If the user saved the task, call the callback
    if (taskCreated == true) {
      onTaskCreated?.call();
    }
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      color: colors.background,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty state icon
            Image.asset(
              'assets/images/create_task.png',
              width: 64,
              height: 64,
              colorBlendMode: BlendMode.srcIn,
              color: colors.primaryText,
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 16),
            ),
            Text(
              localization.noTasksFound,
              style: textStyles.headline3.copyWith(
                color: colors.primaryText,
              ),
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 14),
            ),
            Text(
              localization.pleaseCreateATask,
              style: textStyles.body3.copyWith(
                color: colors.secondaryText,
              ),
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 24),
            ),
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(120, 4, 120, 0),
              child: ElevatedButton.icon(
                onPressed: () => _showAddTaskPopup(context),
                icon: Icon(
                  Iconsax.add_copy,
                  size: 24,
                  color: theme.colorScheme.onPrimary,
                ),
                label: Text(
                  localization.createTask,
                  style: textStyles.buttonMedium.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontSize: 16,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 4, 16, 4),
                  minimumSize: const Size(double.infinity, 48),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
