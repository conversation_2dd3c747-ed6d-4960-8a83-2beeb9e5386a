import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class ProjectSelectionPopup extends StatefulWidget {
  final int selectedIndex;
  final List<String> projects;
  final Function(int) onSelected;

  const ProjectSelectionPopup({
    super.key,
    required this.selectedIndex,
    required this.projects,
    required this.onSelected,
  });

  @override
  State<ProjectSelectionPopup> createState() => _ProjectSelectionPopupState();
}

class _ProjectSelectionPopupState extends State<ProjectSelectionPopup> {
  late int _selectedIndex;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedIndex;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            margin: const EdgeInsetsDirectional.only(bottom: 16),
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                localization.projects,
                style: textStyles.headline4.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),

          // Projects list
          ...List.generate(widget.projects.length, (index) {
            return Container(
              margin: const EdgeInsetsDirectional.only(bottom: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: colors.backgroundContainer,
                border: Border.all(color: colors.strokeColor, width: 1),
              ),
              child: RadioListTile<int>(
                value: index,
                groupValue: _selectedIndex,
                onChanged: (val) {
                  setState(() => _selectedIndex = val!);
                },
                title: Text(
                  widget.projects[index],
                  style: textStyles.body.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
                activeColor: colors.primary,
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding:
                    const EdgeInsetsDirectional.symmetric(horizontal: 4),
                // Decrease gap between radio button and text
                dense: true,
                visualDensity: VisualDensity.compact,
              ),
            );
          }),

          // Buttons
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 12),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 40,
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colors.primaryText,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        side: BorderSide(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        localization.cancel,
                        style: textStyles.body.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 16),
                ),
                Expanded(
                  flex: 3,
                  child: Container(
                    height: 40,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        foregroundColor: colors.primaryText,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {
                        widget.onSelected(_selectedIndex);
                        Navigator.pop(context);
                      },
                      child: Text(
                        localization.save,
                        style: textStyles.buttonMedium.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
