import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'dropdown_field.dart';
import 'assignee_field.dart';
import 'deadline_field.dart';

class TaskFields extends StatefulWidget {
  final bool isEditing;

  const TaskFields({super.key, this.isEditing = false});

  @override
  State<TaskFields> createState() => _TaskFieldsState();
}

class _TaskFieldsState extends State<TaskFields> {
  String _statusValue = 'Pending';
  String _departmentValue = 'IT';

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      children: [
        Container(
          margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
          height: 1,
          color: colors.strokeColor,
        ),
        Container(height: 20),
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
          padding: const EdgeInsetsDirectional.all(16),
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: widget.isEditing
                  ? colors.primary.withValues(alpha: 0.5)
                  : colors.strokeColor,
              width: widget.isEditing ? 1 : 1,
            ),
          ),
          child: Column(
            children: [
              DropdownField(
                label: localization.status,
                value: _statusValue,
                isStatus: true,
                isEditing: widget.isEditing,
                onChanged: (value) {
                  setState(() {
                    _statusValue = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              DeadlineField(
                label: localization.deadline,
                deadline: DateTime.now(),
                isEditing: widget.isEditing,
              ),
              const SizedBox(height: 16),
              DropdownField(
                label: localization.department,
                value: _departmentValue,
                isDepartment: true,
                isEditing: widget.isEditing,
                onChanged: (value) {
                  setState(() {
                    _departmentValue = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              AssigneeField(
                label: localization.assignee,
                showLabel: true,
                isEditing: widget.isEditing,
              ),
              const SizedBox(height: 12),
              AssigneeField(
                showLabel: false,
                isEditing: widget.isEditing,
                isLastAssignee: true,
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
