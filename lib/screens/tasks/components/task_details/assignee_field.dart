import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class AssigneeField extends StatelessWidget {
  final String label;
  final bool showLabel;
  final bool isEditing;
  final bool isLastAssignee;

  const AssigneeField({
    super.key,
    this.label = '',
    required this.showLabel,
    this.isEditing = false,
    this.isLastAssignee = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: showLabel
              ? Text(
                  label.isNotEmpty ? label : localization.assignee,
                  style: textStyles.body.copyWith(
                    color: isEditing ? colors.primary : colors.tertiaryText,
                  ),
                )
              : Container(),
        ),
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                height: 40,
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: colors.background,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isEditing ? colors.primary : colors.strokeColor,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    AnimatedScale(
                      duration: const Duration(milliseconds: 200),
                      scale: isEditing ? 1.1 : 1.0,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: const BoxDecoration(shape: BoxShape.circle),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.asset(
                            'assets/images/person.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    Container(
                        margin: const EdgeInsetsDirectional.only(start: 8)),
                    Expanded(
                      child: Text(
                        'Moh Reed',
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (isEditing && isLastAssignee) ...[
                Container(margin: const EdgeInsets.only(top: 8)),
                Center(
                  child: GestureDetector(
                    onTap: () {
                      // TODO: Implement add assignee functionality
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: 28,
                      height: 28,
                      child: DottedBorder(
                        borderType: BorderType.Circle,
                        dashPattern: const [6, 2],
                        color: colors.primary,
                        strokeWidth: 1,
                        child: Container(
                          width: 28,
                          height: 28,
                          decoration: BoxDecoration(
                            color: colors.background,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.add,
                            color: colors.primary,
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
