import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:hugeicons/hugeicons.dart';

class DropdownField extends StatefulWidget {
  final String label;
  final String value;
  final bool isStatus;
  final bool isDepartment;
  final bool isEditing;
  final Function(String)? onChanged;

  const DropdownField({
    super.key,
    required this.label,
    required this.value,
    this.isStatus = false,
    this.isDepartment = false,
    this.isEditing = false,
    this.onChanged,
  });

  @override
  State<DropdownField> createState() => _DropdownFieldState();
}

class _DropdownFieldState extends State<DropdownField>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  List<String> get _dropdownItems {
    if (widget.isStatus) {
      return ['Backlog', 'Pending', 'In-Progress', 'Completed'];
    } else if (widget.isDepartment) {
      return ['Design', 'IT', 'HR'];
    }
    return [];
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    if (!widget.isEditing) return;

    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _selectItem(String item) {
    widget.onChanged?.call(item);
    _toggleDropdown();
  }

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;
    final textStyles = Theme.of(context).extension<TextStyles>()!;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                widget.label,
                style: textStyles.body.copyWith(
                  color:
                      widget.isEditing ? colors.primary : colors.tertiaryText,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: GestureDetector(
                onTap: _toggleDropdown,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  height: 40,
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 12, 0),
                  decoration: BoxDecoration(
                    color: widget.isEditing
                        ? colors.background
                        : colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: widget.isEditing
                          ? colors.primary
                          : colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.value,
                          style: textStyles.body2.copyWith(
                            color: widget.isStatus
                                ? _getStatusColor(colors)
                                : widget.isDepartment
                                    ? colors.secondaryText
                                    : colors.secondaryText,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Icon(
                        HugeIcons.strokeRoundedArrowDown01,
                        color: widget.isEditing
                            ? colors.primary
                            : colors.tertiaryText,
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        if (widget.isEditing && (widget.isStatus || widget.isDepartment))
          AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: _expandAnimation.value,
                  child: Container(
                    margin: const EdgeInsetsDirectional.only(start: 120),
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.primary,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: _dropdownItems.map((item) {
                          final isSelected = item == widget.value;
                          return GestureDetector(
                            onTap: () => _selectItem(item),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsetsDirectional.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                item,
                                style: textStyles.body2.copyWith(
                                  color: widget.isStatus
                                      ? _getStatusColorForItem(colors, item)
                                      : colors.secondaryText,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Color _getStatusColor(AppColors colors) {
    switch (widget.value) {
      case 'Pending':
        return colors.warning;
      case 'In-Progress':
        return colors.primary;
      case 'Completed':
        return colors.success;
      default:
        return colors.warning;
    }
  }

  Color _getStatusColorForItem(AppColors colors, String item) {
    switch (item) {
      case 'Pending':
        return colors.warning;
      case 'In-Progress':
        return colors.primary;
      case 'Completed':
        return colors.success;
      default:
        return colors.secondaryText;
    }
  }
}
