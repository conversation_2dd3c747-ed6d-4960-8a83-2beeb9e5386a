import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:intl/intl.dart';

class DeadlineField extends StatelessWidget {
  final String label;
  final DateTime? deadline;
  final bool isEditing;

  const DeadlineField({
    super.key,
    required this.label,
    this.deadline,
    this.isEditing = false,
  });

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;
    final textStyles = Theme.of(context).extension<TextStyles>()!;

    final displayDate = deadline ?? DateTime.now();
    final formattedDate =
        DateFormat(formatDateDmy(displayDate, context)).format(displayDate);

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: textStyles.body.copyWith(
              color: isEditing ? colors.primary : colors.tertiaryText,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: GestureDetector(
            onTap: isEditing ? () => _selectDate(context) : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: 40,
              padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 12, 0),
              decoration: BoxDecoration(
                color:
                    isEditing ? colors.background : colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isEditing ? colors.primary : colors.strokeColor,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Text(
                    formattedDate,
                    style: textStyles.body2.copyWith(
                      color: colors.secondaryText,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (isEditing) ...[
                    const Spacer(),
                    Icon(
                      Icons.calendar_today,
                      color: colors.primary,
                      size: 16,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: deadline ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    // In a real implementation,update the deadline here
    if (picked != null) {
      // Handle date selection
    }
  }
}
