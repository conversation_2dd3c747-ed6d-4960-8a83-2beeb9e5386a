import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

class TaskTitle extends StatelessWidget {
  final String title;
  final bool isEditing;
  final TextEditingController? controller;

  const TaskTitle({
    super.key,
    required this.title,
    this.isEditing = false,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 20),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: isEditing
            ? TextFormField(
                key: const ValueKey('title_edit'),
                controller: controller,
                style: textStyles.headline2.copyWith(
                  color: colors.primaryText,
                ),
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: colors.strokeColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: colors.primary, width: 1),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: colors.background,
                ),
                maxLines: null,
              )
            : Text(
                key: const ValueKey('title_view'),
                title,
                style: textStyles.headline2.copyWith(
                  color: colors.primaryText,
                ),
              ),
      ),
    );
  }
}
