import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_svg/svg.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class CardHeader extends StatelessWidget {
  final String taskId;
  final bool isEditing;
  final VoidCallback? onEditToggle;
  final VoidCallback? onSave;
  final VoidCallback? onCancel;

  const CardHeader({
    super.key,
    this.taskId = '#UI007',
    this.isEditing = false,
    this.onEditToggle,
    this.onSave,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 10),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              taskId,
              style: textStyles.headline3.copyWith(
                color: colors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: isEditing
                ? _buildEditingActions(context, colors)
                : _buildViewActions(context, colors),
          ),
        ],
      ),
    );
  }

  Widget _buildViewActions(BuildContext context, AppColors colors) {
    return Row(
      key: const ValueKey('view_actions'),
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        SvgPicture.asset(
          'assets/icons/tasks_screen/share.svg',
          width: 24,
          height: 24,
          colorFilter: ColorFilter.mode(
            colors.primaryText,
            BlendMode.srcIn,
          ),
        ),
        Container(margin: const EdgeInsetsDirectional.fromSTEB(6, 0, 12, 0)),
        GestureDetector(
          onTap: onEditToggle,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Colors.transparent,
            ),
            child: Icon(
              Iconsax.edit_2_copy,
              size: 24,
              color: colors.secondaryText,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEditingActions(BuildContext context, AppColors colors) {
    final textStyles = Theme.of(context).extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Row(
      key: const ValueKey('edit_actions'),
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        // Cancel button
        GestureDetector(
          onTap: onCancel,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: colors.strokeColor),
              color: colors.background,
            ),
            child: Text(
              localization.cancel,
              style: textStyles.body2.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ),
        ),
        Container(margin: const EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0)),
        // Save button
        GestureDetector(
          onTap: onSave,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: colors.primary,
            ),
            child: Text(
              localization.save,
              style: textStyles.body2.copyWith(
                color: colors.backgroundContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
