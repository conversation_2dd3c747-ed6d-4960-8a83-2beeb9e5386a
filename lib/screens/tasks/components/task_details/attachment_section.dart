import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/components/attachment/attachment_grid_card.dart';
import 'package:ako_basma/components/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class AttachmentSection extends StatefulWidget {
  const AttachmentSection({super.key});

  @override
  State<AttachmentSection> createState() => _AttachmentSectionState();
}

class _AttachmentSectionState extends State<AttachmentSection> {
  List<File> selectedFiles = [];
  final _attachmentButtonKey = GlobalKey(debugLabel: 'taskAttachmentKey');

  Future<void> _pickAttachment() async {
    final res = await showLocalPickerMenu(
      buttonKey: _attachmentButtonKey,
      context: context,
      allowedTypes: ['image', 'pdf'], // Allow images and PDF files
      allowMultiple: true,
      maxSizeInMB: 10,
    );

    if (res != null) {
      setState(() {
        if (res is File) {
          selectedFiles.add(res);
        }
        if (res is List<File>) {
          selectedFiles.addAll(res);
        }
      });
      // Files successfully selected and added to attachments list
    }
  }

  void _removeFile(int index) {
    setState(() {
      selectedFiles.removeAt(index);
    });
  }

  Widget _buildAttachmentGrid() {
    if (selectedFiles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
      child: Column(
        children: [
          // attachment tiles in grid
          ...List.generate((selectedFiles.length / 2).ceil(), (rowIndex) {
            final startIndex = rowIndex * 2;
            final endIndex = (startIndex + 2).clamp(0, selectedFiles.length);
            final filesInRow = selectedFiles.sublist(startIndex, endIndex);

            return Container(
              margin: EdgeInsets.only(
                bottom:
                    rowIndex < (selectedFiles.length / 2).ceil() - 1 ? 16 : 0,
              ),
              child: Row(
                children: [
                  // First file in row
                  Expanded(
                    child: AttachmentPlaceholderCard(
                      filePath: filesInRow[0].path,
                      previewWidth: 114,
                      previewHeight: 100,
                      onDelete: () => _removeFile(startIndex),
                    ),
                  ),

                  // Second file in row (if exists)
                  if (filesInRow.length > 1) ...[
                    const SizedBox(width: 16),
                    Expanded(
                      child: AttachmentPlaceholderCard(
                        filePath: filesInRow[1].path,
                        previewWidth: 114,
                        previewHeight: 100,
                        onDelete: () => _removeFile(startIndex + 1),
                      ),
                    ),
                  ] else ...[
                    const SizedBox(width: 16),
                    const Expanded(child: SizedBox()),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // Allow shrinking for proper scrolling
      children: [
        // Attachment section header
        Container(
          margin: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
          child: Text(
            '${localization.attachment} (${selectedFiles.length})',
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ),

        // Attachment grid
        if (selectedFiles.isNotEmpty) ...[
          _buildAttachmentGrid(),
          const SizedBox(height: 16),
        ],

        // Upload button
        Container(
          key: _attachmentButtonKey,
          margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 20),
          child: AttachmentPlaceholderCard(
            preset: 'upload',
            onTap: _pickAttachment,
          ),
        ),
      ],
    );
  }
}
