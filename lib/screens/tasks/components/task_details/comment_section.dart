import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:solar_icons/solar_icons.dart';

class CommentSection extends StatefulWidget {
  const CommentSection({super.key});

  @override
  State<CommentSection> createState() => _CommentSectionState();
}

class _CommentSectionState extends State<CommentSection> {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  int _currentIndex = 0;

  @override
  void dispose() {
    _commentController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Quick comment options
    final List<String> commentOptions = [
      localization.canIGetMoreInfo,
      localization.statusUpdate,
      'Task Completed',
      'Need Help',
      'Approve Task',
    ];

    return Container(
      width: double.infinity,
      margin: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colors.strokeColor, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Add Comment TextField
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localization.addComment,
                  style: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _commentController,
                  focusNode: _focusNode,
                  maxLines: 3,
                  minLines: 1,
                  decoration: InputDecoration(
                    hintText: 'Write your comment here...',
                    hintStyle: textStyles.body2.copyWith(
                      color: colors.tertiaryText.withOpacity(0.6),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.primary,
                        width: 1,
                      ),
                    ),
                    contentPadding:
                        const EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
                    filled: true,
                    fillColor: colors.background,
                    suffixIcon: GestureDetector(
                      onTap: () {
                        if (_commentController.text.trim().isNotEmpty) {
                          // Handle comment submission here
                          FocusScope.of(context).unfocus();
                          // You can add comment submission logic here
                          _commentController.clear();
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        child: Icon(
                          SolarIconsOutline.plain,
                          color: _commentController.text.trim().isNotEmpty
                              ? colors.primary
                              : colors.tertiaryText.withOpacity(0.5),
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                  style: textStyles.body2.copyWith(
                    color: colors.secondaryText,
                  ),
                  onChanged: (value) {
                    setState(() {}); // Rebuild to update send icon color
                  },
                ),
              ],
            ),
          ),
          // Quick comment options carousel
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 16),
            child: CarouselSlider(
              options: CarouselOptions(
                height: 40,
                viewportFraction: 0.48,
                enableInfiniteScroll: false,
                padEnds: false,
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
              ),
              items: commentOptions.asMap().entries.map((entry) {
                int index = entry.key;
                String comment = entry.value;

                return Container(
                  margin: EdgeInsetsDirectional.only(
                    start: index == 0 ? 16 : 6,
                    end: index == commentOptions.length - 1 ? 16 : 6,
                  ),
                  child: GestureDetector(
                    onTap: () {
                      // Set the selected quick comment in the text field
                      _commentController.text = comment;
                      setState(() {});
                    },
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                        12,
                        8,
                        12,
                        8,
                      ),
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          comment,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                            fontSize: 13,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
