import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'card_header.dart';
import 'task_title.dart';
import 'task_fields.dart';
import 'description_section.dart';
import 'attachment_section.dart';

class MainTaskCard extends StatefulWidget {
  const MainTaskCard({super.key});

  @override
  State<MainTaskCard> createState() => _MainTaskCardState();
}

class _MainTaskCardState extends State<MainTaskCard>
    with TickerProviderStateMixin {
  bool _isEditing = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  // Controllers for editable fields
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Initialize controllers with current values
    _titleController =
        TextEditingController(text: 'Change email option process');
    _descriptionController = TextEditingController(
      text:
          'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.',
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
      if (_isEditing) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _saveChanges() {
    // Here it'll save the changes to your data source
    // For now, we'll just toggle back to view mode
    setState(() {
      _isEditing = false;
      _animationController.reverse();
    });

    // Show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Task updated successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _cancelChanges() {
    // Reset controllers to original values
    _titleController.text = 'Change email option process';
    _descriptionController.text =
        'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.';

    setState(() {
      _isEditing = false;
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: double.infinity,
            decoration: BoxDecoration(
              color: colors.backgroundContainer,
              borderRadius: BorderRadius.circular(12),
              border: _isEditing
                  ? Border.all(color: colors.primary.withOpacity(0.3), width: 1)
                  : null,
              boxShadow: _isEditing
                  ? [
                      BoxShadow(
                        color: colors.primary.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 8),
                      ),
                    ]
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CardHeader(
                  isEditing: _isEditing,
                  onEditToggle: _toggleEdit,
                  onSave: _saveChanges,
                  onCancel: _cancelChanges,
                ),
                TaskTitle(
                  title: _titleController.text,
                  isEditing: _isEditing,
                  controller: _titleController,
                ),
                TaskFields(isEditing: _isEditing),
                DescriptionSection(
                  description: _descriptionController.text,
                  isEditing: _isEditing,
                  controller: _descriptionController,
                ),
                const AttachmentSection(),
              ],
            ),
          ),
        );
      },
    );
  }
}
