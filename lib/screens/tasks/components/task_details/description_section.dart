import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class DescriptionSection extends StatelessWidget {
  final String description;
  final bool isEditing;
  final TextEditingController? controller;

  const DescriptionSection({
    super.key,
    required this.description,
    this.isEditing = false,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isEditing ? colors.primary.withOpacity(0.5) : colors.strokeColor,
          width: 1,
        ),
      ),
      child: <PERSON><PERSON>(
        clipBehavior: Clip.none,
        children: [
          // The actual description content
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 20, 16, 16),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: isEditing
                  ? TextFormField(
                      key: const ValueKey('description_edit'),
                      controller: controller,
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                        height: 1.4,
                      ),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Enter task description...',
                        hintStyle: textStyles.body2.copyWith(
                          color: colors.secondaryText.withOpacity(0.6),
                        ),
                      ),
                      maxLines: null,
                      minLines: 2,
                    )
                  : Text(
                      key: const ValueKey('description_view'),
                      description,
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                        height: 1.4,
                      ),
                    ),
            ),
          ),

          // The over-laid label
          PositionedDirectional(
            top: -10,
            start: 12,
            child: Container(
              padding: const EdgeInsetsDirectional.fromSTEB(4, 0, 4, 0),
              color: colors.backgroundContainer,
              child: Text(
                localization.description,
                style: textStyles.body2.copyWith(
                  color: isEditing ? colors.primary : colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
