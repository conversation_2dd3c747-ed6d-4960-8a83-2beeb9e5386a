import 'dart:io';

import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:ako_basma/components/attachment/attachment_placeholder_card.dart';

class CreateProject extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? onProjectCreated;

  const CreateProject({
    super.key,
    this.onBack,
    this.onProjectCreated,
  });

  @override
  State<CreateProject> createState() => _CreateProjectState();
}

class _CreateProjectState extends State<CreateProject> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final List<File> _attachments = [];
  final _attachmentButtonKey = GlobalKey(debugLabel: 'projectAttachmentKey');

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickAttachment() async {
    final res = await showLocalPickerMenu(
      buttonKey: _attachmentButtonKey,
      context: context,
      allowedTypes: ['image', 'pdf'], // Allow images and PDF files
      allowMultiple: true,
      maxSizeInMB: 10,
    );

    if (res != null) {
      setState(() {
        if (res is File) {
          _attachments.add(res);
        }
        if (res is List<File>) {
          _attachments.addAll(res);
        }
      });
      // Files successfully selected and added to attachments list
    }
  }

  void _handleAddProject() {
    // Close and inform caller that a project has been created
    Navigator.pop(context, true);
    // Additional callbacks (if any) handled by caller via the returned value.
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 12,
                  ),
                  Text(
                    localization.createProject,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Form content
            Expanded(
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsetsDirectional.only(bottom: 80),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Title field
                      Container(
                        margin: const EdgeInsetsDirectional.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: TextField(
                          controller: titleController,
                          decoration: InputDecoration(
                            contentPadding:
                                const EdgeInsetsDirectional.fromSTEB(
                                    16, 12, 16, 12),
                            border: InputBorder.none,
                            hintText: localization.title,
                            hintStyle: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                          ),
                          style: textStyles.body.copyWith(
                            color: colors.primaryText,
                          ),
                        ),
                      ),

                      // Description field
                      Container(
                        margin: const EdgeInsetsDirectional.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: TextField(
                          controller: descriptionController,
                          decoration: InputDecoration(
                            contentPadding:
                                const EdgeInsetsDirectional.fromSTEB(
                                    16, 12, 16, 12),
                            border: InputBorder.none,
                            hintText: localization.description,
                            hintStyle: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                          ),
                          style: textStyles.body.copyWith(
                            color: colors.primaryText,
                          ),
                        ),
                      ),

                      // File upload area
                      InkWell(
                        key: _attachmentButtonKey,
                        onTap: _pickAttachment,
                        child: DottedBorder(
                          borderType: BorderType.RRect,
                          radius: const Radius.circular(8),
                          color: colors.strokeColor,
                          dashPattern: const [8, 6],
                          strokeWidth: 1.5,
                          child: Container(
                            decoration: BoxDecoration(
                              color: colors.backgroundContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                24, 24, 24, 24),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: colors.background,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: SvgPicture.asset(
                                      'assets/icons/workspace_screen/upload.svg',
                                      colorFilter: ColorFilter.mode(
                                        colors.primary,
                                        BlendMode.srcIn,
                                      ),
                                      width: 24,
                                      height: 24,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 14),
                                Text(
                                  localization.clickToUpload,
                                  style: textStyles.body2.copyWith(
                                    color: colors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Container(height: 14),
                                Text(
                                  localization.maxFileSizeInMB(25),
                                  style: textStyles.body3.copyWith(
                                    color: colors.secondaryText,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Display selected attachments - Grid layout with previews
                      if (_attachments.isNotEmpty)
                        Container(
                          margin: const EdgeInsetsDirectional.fromSTEB(
                              10, 16, 10, 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${localization.attachment} (${_attachments.length})',
                                style: textStyles.body2.copyWith(
                                  color: colors.primaryText,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Attachment grid with proper previews
                              ...List.generate((_attachments.length / 2).ceil(),
                                  (rowIndex) {
                                final startIndex = rowIndex * 2;
                                final endIndex = (startIndex + 2)
                                    .clamp(0, _attachments.length);
                                final filesInRow =
                                    _attachments.sublist(startIndex, endIndex);

                                return Container(
                                  margin: EdgeInsets.only(
                                    bottom: rowIndex <
                                            (_attachments.length / 2).ceil() - 1
                                        ? 16
                                        : 0,
                                  ),
                                  child: Row(
                                    children: [
                                      // First file in row
                                      Expanded(
                                        child: AttachmentPlaceholderCard(
                                          filePath: filesInRow[0].path,
                                          previewWidth: 114,
                                          previewHeight: 100,
                                          onDelete: () => setState(() {
                                            _attachments.removeAt(startIndex);
                                          }),
                                        ),
                                      ),

                                      // Second file in row (if exists)
                                      if (filesInRow.length > 1) ...[
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: AttachmentPlaceholderCard(
                                            filePath: filesInRow[1].path,
                                            previewWidth: 114,
                                            previewHeight: 100,
                                            onDelete: () => setState(() {
                                              _attachments
                                                  .removeAt(startIndex + 1);
                                            }),
                                          ),
                                        ),
                                      ] else ...[
                                        const SizedBox(width: 16),
                                        const Expanded(child: SizedBox()),
                                      ],
                                    ],
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            // Add button pinned at bottom
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 16),
              width: double.infinity,
              height: 60,
              child: ElevatedButton(
                onPressed: _handleAddProject,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  localization.add,
                  style: textStyles.buttonMedium.copyWith(
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
