import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/router/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hugeicons/hugeicons.dart';

import '../../components/button/primary_button.dart';
import '../../components/form/simple_text_field.dart';
import '../../constants/assets.dart';
import '../../util/form/validators.dart';
import '../../l10n/generated/app_localizations.dart';
import '../../providers/language/language_provider.dart';
import 'package:ako_basma/util/ui/formatting.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key, this.initialMode, this.inviteCode});

  final String? initialMode;
  final String? inviteCode;

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  bool _initialized = false;

  final _formKey = GlobalKey<FormState>();
  final _phoneFormKey = GlobalKey<FormState>();

  final _contactController = TextEditingController();
  String _countryCode = '+964';

  bool _phoneVerified = false;
  final _phoneFocus = FocusNode();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _initApp();
      _phoneFocus.addListener(() {
        setState(() {});
      });
    });

    super.initState();
  }

  Future<void> _initApp() async {
    setState(() {
      _initialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final bool isRtl = Directionality.of(context) == TextDirection.rtl;
    // final languageNotifier = ref.watch(languageProvider.notifier);
    final form = Column(
      children: [
        Container(
          margin: const EdgeInsetsDirectional.only(top: 20),
        ),
        Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: colors.strokeColor,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: colors.backgroundContainer,
                ),
                child: InkWell(
                  onTap: () {
                    // Country picker
                  },
                  child: Padding(
                    padding: const EdgeInsetsDirectional.symmetric(
                        horizontal: 15, vertical: 18),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          embedLtr(_countryCode),
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                        ),
                        Container(
                          width: 5,
                        ),
                        Icon(
                          HugeIcons.strokeRoundedArrowDown01,
                          size: 18,
                          color: colors.primary,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(start: 10),
              ),

              // Phone number
              Expanded(
                child: SimpleTextField(
                  controller: _contactController,
                  validator: (value) {
                    return Validators.phone('$_countryCode$value');
                  },
                  onChanged: (_) {
                    setState(() {});
                  },
                  focusNode: _phoneFocus,
                  autocorrect: false,
                  decoration: InputDecoration(
                    label: Align(
                      alignment:
                          isRtl ? Alignment.centerRight : Alignment.centerLeft,
                      child: Text(
                        localization.phoneNumber,
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                    floatingLabelBehavior: FloatingLabelBehavior.auto,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colors.strokeColor,
                      ),
                    ),
                    fillColor: colors.backgroundContainer,
                    filled: true,
                  ),
                  keyboardType: TextInputType.phone,
                ),
              ),
            ],
          ),
        ),
        Container(
          margin: const EdgeInsetsDirectional.only(top: 20),
        ),
      ],
    );

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.only(top: 80),
                child: Column(
                  children: [
                    Image.asset(
                      Assets.logoImage,
                      height: 100,
                      width: 94.74,
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 16),
                    ),
                    Container(
                      width: 200.57,
                      height: 50,
                      child: Text(
                        localization.akoBasma, // Using localized app name
                        style: textStyles.headline.copyWith(
                          color: const Color(0xFF066A96), // App primary color
                          fontWeight: FontWeight.bold,
                          fontSize: 36,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 60,
              ),
              Form(
                key: _formKey,
                child: form,
              ),
              const Spacer(),
              Container(
                // width: MediaQuery.of(context).size.width,
                padding: const EdgeInsetsDirectional.fromSTEB(4, 10, 4, 10),
                child: PrimaryButton.async(
                  label: localization.next,
                  textStyle: textStyles.buttonMedium.copyWith(
                    fontSize: 20,
                    color: _contactController.text.isEmpty
                        ? colors.tertiaryText
                        : Theme.of(context).colorScheme.onPrimary,
                  ),
                  expand: true,
                  padding: const EdgeInsetsDirectional.fromSTEB(24, 12, 24, 12),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _contactController.text.isEmpty
                        ? colors.disabled
                        : Theme.of(context).colorScheme.primary,
                    foregroundColor: _contactController.text.isEmpty
                        ? colors.tertiaryText
                        : Theme.of(context).colorScheme.onPrimary,
                    minimumSize: const Size.fromHeight(56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                    shadowColor: Colors.transparent,
                  ),
                  onPressed: () async {
                    // await _onVerifyPhonePress();
                    ref.read(routerProvider).push('/otp');
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
