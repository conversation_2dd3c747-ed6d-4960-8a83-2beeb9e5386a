import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

/// Message bubble component for individual chat messages
/// Different styling for sent vs received messages
class MessageBubble extends StatelessWidget {
  final String message;
  final String time;
  final bool isSentByMe;
  final bool isRead;
  final String profileImage;
  final bool showAvatar;

  const MessageBubble({
    super.key,
    required this.message,
    required this.time,
    required this.isSentByMe,
    required this.isRead,
    required this.profileImage,
    this.showAvatar = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      margin: const EdgeInsetsDirectional.fromSTEB(0, 2, 0, 2),
      child: Row(
        mainAxisAlignment:
            isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Message bubble
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth:
                    screenWidth * 0.75, // Limit bubble width to 75% of screen
              ),
              decoration: BoxDecoration(
                // Use the predefined primary gradient for sent messages
                gradient: isSentByMe ? colors.primaryGradient : null,
                color: isSentByMe ? null : colors.backgroundContainer,
                borderRadius: BorderRadiusDirectional.only(
                  topStart: const Radius.circular(15),
                  topEnd: const Radius.circular(15),
                  bottomStart: Radius.circular(isSentByMe ? 15 : 1),
                  bottomEnd: Radius.circular(isSentByMe ? 1 : 15),
                ),
              ),
              padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
              child: Column(
                crossAxisAlignment: isSentByMe
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Message text
                  Text(
                    message,
                    style: textStyles.body.copyWith(
                      color: isSentByMe ? Colors.white : colors.primaryText,
                      height: 1.4,
                    ),
                  ),

                  // Time and read status
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Time
                        Text(
                          time,
                          style: textStyles.body3.copyWith(
                            color: isSentByMe
                                ? theme.colorScheme.onPrimary
                                : colors.tertiaryText,
                            fontSize: 11,
                          ),
                        ),

                        // Read status (double checkmarks for sent messages)
                        if (isSentByMe)
                          Container(
                            margin: const EdgeInsetsDirectional.only(start: 4),
                            child: Icon(Icons.done_all,
                                size: 14,
                                color: isRead
                                    ? theme.colorScheme.onPrimary
                                        .withOpacity(0.9)
                                    : theme.colorScheme.onPrimary),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
