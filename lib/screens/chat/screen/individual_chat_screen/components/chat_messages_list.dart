import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/message_bubble.dart';

class ChatMessagesList extends StatelessWidget {
  const ChatMessagesList({super.key});

  // Mock data - replace with actual chat history
  List<Map<String, dynamic>> _getSampleMessages() {
    return [
      {
        'id': '1',
        'message':
            'Awesome, thanks for letting me know! Can\'t wait for my delivery. 🎉',
        'time': '10:11',
        'isSentByMe': true,
        'isRead': true,
        'profileImage': '',
      },
      {
        'id': '2',
        'message': 'No problem at all!\nI\'ll be there in about 15 minutes.',
        'time': '10:11',
        'isSentByMe': false,
        'isRead': false,
        'profileImage': 'assets/images/person.png',
      },
      {
        'id': '3',
        'message': 'I\'ll text you when I arrive.',
        'time': '10:11',
        'isSentByMe': false,
        'isRead': false,
        'profileImage': 'assets/images/person.png',
      },
      {
        'id': '4',
        'message': 'Great! 😊',
        'time': '10:12',
        'isSentByMe': true,
        'isRead': true,
        'profileImage': '',
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final messages = _getSampleMessages();

    return Container(
      color: colors.background,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final message = messages[index];

          bool showTimeHeader = false;
          if (index == 0) {
            showTimeHeader = true;
          }

          return Column(
            children: [
              MessageBubble(
                message: message['message'],
                time: message['time'],
                isSentByMe: message['isSentByMe'],
                isRead: message['isRead'],
                profileImage: message['profileImage'],
                showAvatar: false,
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(bottom: 8),
              ),
            ],
          );
        },
      ),
    );
  }
}
