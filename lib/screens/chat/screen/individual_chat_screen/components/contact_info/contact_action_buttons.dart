import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:solar_icons/solar_icons.dart';

/// Contact action buttons component

class ContactActionButtons extends StatelessWidget {
  final VoidCallback? onCall;
  final VoidCallback? onChat;
  final VoidCallback? onEmail;

  const ContactActionButtons({
    super.key,
    this.onCall,
    this.onChat,
    this.onEmail,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Main container
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Call button
          _buildActionButton(
            context,
            icon: SolarIconsOutline.phone,
            label: localization.call,
            onTap: onCall,
            color: colors.primary,
          ),

          // Chat button
          _buildActionButton(
            context,
            icon: SolarIconsOutline.chatRoundLine,
            label: localization.chat,
            onTap: onChat,
            color: colors.primary,
          ),

          // Email button
          _buildActionButton(
            context,
            icon: SolarIconsOutline.letter,
            label: localization.email,
            onTap: onEmail,
            color: colors.info,
          ),
        ],
      ),
    );
  }

  /// Helper method to build individual action buttons
  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsetsDirectional.fromSTEB(20, 20, 20, 20),
          child: Column(
            children: [
              // Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: colors.primaryVariant,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),

              // Label
              Container(
                margin: const EdgeInsetsDirectional.only(top: 8),
                child: Text(
                  label,
                  style: textStyles.body2.copyWith(
                    color: colors.secondaryText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
