import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_header.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

class IndividualChatScreen extends StatefulWidget {
  final String userName;
  final String userImage;
  final String lastSeen;
  final bool isOnline;

  const IndividualChatScreen({
    super.key,
    required this.userName,
    required this.userImage,
    required this.lastSeen,
    this.isOnline = false,
  });

  @override
  State<IndividualChatScreen> createState() => _IndividualChatScreenState();
}

class _IndividualChatScreenState extends State<IndividualChatScreen> {
  bool _showAttachmentMenu = false;

  static const double inputFieldHeight = 60.0;
  static const double attachmentMenuPadding = 50.0;

  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu();

      final File? photo = await ImageService.captureFromCamera();

      if (photo != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Photo captured successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu();

      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu();

      final File? document = await DocumentService.selectDocument();

      if (document != null) {
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');

        if (mounted) {
          DocumentService.showSuccessMessage(
              context, 'Document "$fileName" selected successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  void _handleContactPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactSelectionModal(
        onContactsSelected: (selectedContacts) {
          if (selectedContacts.isNotEmpty) {
            print(
                'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
          }
        },
      ),
    );
  }

  void _handleLocationPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => LocationSharingModal(
        onLocationSelected: (locationType, locationData) {
          print('Location type: $locationType');
          print('Location data: $locationData');
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return GestureDetector(
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            Column(
              children: [
                ChatHeader(
                  userName: widget.userName,
                  userImage: widget.userImage,
                  lastSeen: widget.lastSeen,
                  isOnline: widget.isOnline,
                  onBackPressed: () => context.pop(),
                ),
                const Expanded(
                  child: ChatMessagesList(),
                ),
                ChatInputField(
                  onSendMessage: (message) {
                    print('Message sent: $message');
                  },
                  onAttachmentPressed: _toggleAttachmentMenu,
                  onVoicePressed: () {
                    print('Voice message pressed');
                  },
                ),
              ],
            ),
            if (_showAttachmentMenu)
              PositionedDirectional(
                bottom: MediaQuery.of(context).viewInsets.bottom +
                    inputFieldHeight +
                    attachmentMenuPadding,
                start: MediaQuery.of(context).size.width * 0.02,
                end: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed: _handleCameraPress,
                  onRecordPressed: () {
                    _toggleAttachmentMenu();
                    print('Record pressed');
                  },
                  onContactPressed: () {
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed: _handleGalleryPress,
                  onLocationPressed: () {
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed: _handleDocumentPress,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
