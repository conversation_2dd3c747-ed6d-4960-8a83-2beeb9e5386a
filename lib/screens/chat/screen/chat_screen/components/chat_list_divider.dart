import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

class ChatListDivider extends StatelessWidget {
  const ChatListDivider({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      child: Divider(
        color: colors.strokeColor,
        thickness: 1,
        height: 1,
      ),
    );
  }
}
