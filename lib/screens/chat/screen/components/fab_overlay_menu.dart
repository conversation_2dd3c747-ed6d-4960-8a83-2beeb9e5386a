import 'dart:math';

import 'package:ako_basma/styles/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/components/overlay/overlay_builder.dart';
import 'package:ako_basma/components/overlay/animated_section.dart';

import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/add_employee.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

/// Floating Action Button with Overlay Menu, following the exact overlay pattern from chat input field/attachment menu
class FabOverlayMenu extends StatefulWidget {
  const FabOverlayMenu({super.key});

  @override
  State<FabOverlayMenu> createState() => _FabOverlayMenuState();
}

class _FabOverlayMenuState extends State<FabOverlayMenu> {
  final LayerLink _fabLayerLink = LayerLink();
  bool _displaying = true;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final isRtl = Directionality.of(context) == TextDirection.rtl;
    return OverlayBuilder(
      overlay: (size, hideCallback) {
        // The overlay menu, positioned to the right of the FAB
        final overlayOffset =
            Offset(isRtl ? 16 + 47.5 : -18, -35); // 8px to the right
        return Stack(
          children: [
            GestureDetector(
              onTap: () => setState(() => _displaying = false),
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                color: Colors.transparent,
              ),
            ),
            PositionedDirectional(
              start: 0,
              top: 0,
              child: CompositedTransformFollower(
                link: _fabLayerLink,
                followerAnchor:
                    isRtl ? Alignment.centerLeft : Alignment.centerRight,
                showWhenUnlinked: false,
                offset: overlayOffset,
                child: AnimatedSection(
                  animationDismissed: hideCallback,
                  expand: _displaying,
                  axis: Axis.horizontal,
                  axisAlignment: -1.0,
                  child: Material(
                    elevation: 8,
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      width: 196,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: theme.colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildMenu(SolarIconsOutline.usersGroupTwoRounded,
                              AppLocalizations.of(context)!.newGroup, () {
                            // Navigate to Add Employee screen for group creation as full screen modal
                            // Use root navigator to bypass the shell route and hide bottom navigation
                            Navigator.of(context, rootNavigator: true).push(
                              MaterialPageRoute(
                                builder: (context) => const AddEmployee(),
                                fullscreenDialog:
                                    true, // Ensures it's treated as a modal that covers everything
                              ),
                            );
                          }, hideCallback),
                          const SizedBox(height: 8),
                          _buildMenu(SolarIconsOutline.usersGroupTwoRounded,
                              AppLocalizations.of(context)!.newChannel, () {
                            // Show placeholder message for new channel functionality
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(AppLocalizations.of(context)!
                                    .newChannelFunctionality),
                              ),
                            );
                          }, hideCallback),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
      child: (showCallback) => CompositedTransformTarget(
        link: _fabLayerLink,
        child: Transform.translate(
          offset: Offset(
            Directionality.of(context) == TextDirection.rtl ? 7.5 : -7.5,
            0,
          ),
          child: Transform.rotate(
            angle: pi / 4,
            child: InkWell(
              splashFactory: NoSplash.splashFactory,
              onTap: () {
                setState(() => _displaying = true);
                showCallback();
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                  width: 40,
                  height: 40,
                  padding: const EdgeInsets.all(4),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: theme.colors.primary /* Brand-Primary */,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                  ),
                  child: Transform.rotate(
                    angle: pi / 4,
                    child: const Icon(
                      Iconsax.add_copy,
                      color: Colors.white,
                      size: 32,
                    ),
                  )),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenu(IconData icon, String label, void Function() onPressed,
      void Function() hideCallback) {
    final theme = AppTheme.of(context);
    return InkWell(
      onTap: () {
        setState(() => _displaying = false);
        hideCallback();
        onPressed();
      },
      splashFactory: NoSplash.splashFactory,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: theme.colors.background),
        child: Row(
          children: [
            Icon(icon, size: 20, color: theme.colors.secondaryText),
            const SizedBox(width: 8),
            Text(
              label,
              style: theme.textStyles.body
                  .copyWith(color: theme.colors.secondaryText),
            ),
          ],
        ),
      ),
    );
  }
}
