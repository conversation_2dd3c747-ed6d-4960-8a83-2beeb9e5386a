import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/create_group.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class AddEmployee extends StatefulWidget {
  const AddEmployee({super.key});

  @override
  State<AddEmployee> createState() => _AddEmployeeState();
}

class _AddEmployeeState extends State<AddEmployee> {
  // List to track selected employees
  final Set<int> _selectedEmployees = {};

  // employee data will come from API eventually
  late final List<Map<String, dynamic>> _employees;

  @override
  void initState() {
    super.initState();
    // Generate 12 employees with different names for demo
    _employees = List.generate(12, (index) {
      final names = [
        '<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>'
      ];
      return {
        'name': names[index],
        'profileImage': 'assets/images/person.png',
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        // border: Border.all(
                        //   color: colors.strokeColor,
                        //   width: 1,
                        // ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),

                  // Title and step indicator
                  Column(
                    children: [
                      Text(
                        localization.addEmployee,
                        style: textStyles.body2.copyWith(
                          color: colors.primaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Container(
                        margin: const EdgeInsetsDirectional.only(top: 5),
                        child: Text(
                          '${_selectedEmployees.length.toString().padLeft(2, '0')}/${_employees.length.toString().padLeft(2, '0')}',
                          style: textStyles.body3.copyWith(
                            color: colors.tertiaryText,
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Next button
                  Container(
                    padding: const EdgeInsetsDirectional.only(end: 16),
                    child: GestureDetector(
                      onTap: () {
                        // Navigate to create group screen only if employees are selected
                        if (_selectedEmployees.isNotEmpty) {
                          // Get selected employees data
                          final selectedEmployeesData = _selectedEmployees
                              .map((index) => _employees[index])
                              .toList();

                          // Navigate to CreateGroup screen
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => CreateGroup(
                                selectedEmployees: selectedEmployeesData,
                                totalAvailableEmployees: _employees.length,
                              ),
                            ),
                          );
                        }
                      },
                      child: Text(
                        localization.next,
                        style: textStyles.body2.copyWith(
                          color: _selectedEmployees.isNotEmpty
                              ? colors.primary
                              : colors.secondaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Search bar
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 0),
              child: CustomSearchBar(
                hintText: localization.search,
                height: 44,
              ),
            ),

            // Selected employees at the top (if any)
            if (_selectedEmployees.isNotEmpty)
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 88,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _selectedEmployees.length,
                        itemBuilder: (context, index) {
                          final employeeIndex =
                              _selectedEmployees.elementAt(index);
                          final employee = _employees[employeeIndex];
                          return Container(
                            margin: const EdgeInsetsDirectional.only(end: 16),
                            child: _buildSelectedEmployeeAvatar(
                                employee, colors, textStyles),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),

            // Employee list
            Expanded(
              child: Container(
                margin: EdgeInsetsDirectional.fromSTEB(
                  16,
                  _selectedEmployees.isEmpty
                      ? 12
                      : 12, // Add top margin only when no employees selected
                  16,
                  0,
                ),
                child: ListView.builder(
                  itemCount: _employees.length,
                  itemBuilder: (context, index) {
                    final employee = _employees[index];
                    final isSelected = _selectedEmployees.contains(index);

                    return Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 10),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            if (isSelected) {
                              _selectedEmployees.remove(index);
                            } else {
                              _selectedEmployees.add(index);
                            }
                          });
                        },
                        child: Row(
                          children: [
                            // Profile image
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: colors.primaryVariant,
                              ),
                              child: ClipOval(
                                child: Image.asset(
                                  employee['profileImage'],
                                  width: 32,
                                  height: 32,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    // Fallback to initials if image fails to load
                                    return Container(
                                      color: colors.primary.withOpacity(0.1),
                                      child: Center(
                                        child: Text(
                                          employee['name'][0].toUpperCase(),
                                          style: textStyles.headline4.copyWith(
                                            color: colors.primary,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),

                            // Employee name
                            Expanded(
                              child: Container(
                                margin:
                                    const EdgeInsetsDirectional.only(start: 12),
                                child: Text(
                                  employee['name'],
                                  style: textStyles.headline4.copyWith(
                                    color: colors.primaryText,
                                  ),
                                ),
                              ),
                            ),

                            // Checkbox
                            Checkbox(
                              value: isSelected,
                              onChanged: (bool? value) {
                                setState(() {
                                  if (value == true) {
                                    _selectedEmployees.add(index);
                                  } else {
                                    _selectedEmployees.remove(index);
                                  }
                                });
                              },
                              activeColor: colors.primary,
                              side: BorderSide(
                                color: colors.primary,
                                width: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build selected employee avatar widget for top section
  Widget _buildSelectedEmployeeAvatar(
      Map<String, dynamic> employee, AppColors colors, dynamic textStyles) {
    return Column(
      children: [
        // Avatar with small profile image
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: colors.primaryVariant,
          ),
          child: ClipOval(
            child: Image.asset(
              employee['profileImage'],
              width: 64,
              height: 64,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // Fallback to initials if image fails to load
                return Container(
                  color: colors.primary.withOpacity(0.1),
                  child: Center(
                    child: Text(
                      employee['name'][0].toUpperCase(),
                      style: textStyles.headline4.copyWith(
                        color: colors.primary,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),

        // Employee name below avatar
        Container(
          margin: const EdgeInsetsDirectional.only(top: 4),
          child: Text(
            employee['name'], // Show full name
            style: textStyles.headline4.copyWith(
              color: colors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
