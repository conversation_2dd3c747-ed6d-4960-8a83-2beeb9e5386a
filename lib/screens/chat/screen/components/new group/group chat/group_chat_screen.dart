import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screen/components/new group/group chat/group_chat_header.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

class GroupChatScreen extends StatefulWidget {
  final String groupName;
  final File? groupImage;
  final List<Map<String, dynamic>> members;

  const GroupChatScreen({
    super.key,
    required this.groupName,
    this.groupImage,
    required this.members,
  });

  @override
  State<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends State<GroupChatScreen> {
  bool _showAttachmentMenu = false;

  static const double inputFieldHeight = 60.0;
  static const double attachmentMenuPadding = 50.0;

  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu();

      final File? photo = await ImageService.captureFromCamera();

      if (photo != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Photo captured successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu();

      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu();

      final File? document = await DocumentService.selectDocument();

      if (document != null) {
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');

        if (mounted) {
          DocumentService.showSuccessMessage(
              context, 'Document "$fileName" selected successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  void _handleContactPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactSelectionModal(
        onContactsSelected: (selectedContacts) {
          if (selectedContacts.isNotEmpty) {
            print(
                'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
          }
        },
      ),
    );
  }

  void _handleLocationPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => LocationSharingModal(
        onLocationSelected: (locationType, locationData) {
          // Location sharing implementation
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return GestureDetector(
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            Column(
              children: [
                GroupChatHeader(
                  groupName: widget.groupName,
                  groupImage: widget.groupImage,
                  memberCount: widget.members.length,
                  onBackPressed: () {
                    Navigator.of(context, rootNavigator: true)
                        .popUntil((route) => route.isFirst);
                  },
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                    child: Column(
                      children: [
                        Container(
                          margin: const EdgeInsetsDirectional.fromSTEB(
                              0, 20, 0, 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                margin: const EdgeInsetsDirectional.fromSTEB(
                                    16, 0, 16, 0),
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                  20,
                                  8,
                                  20,
                                  8,
                                ),
                                decoration: BoxDecoration(
                                  color: colors.background,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: colors.strokeColor,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  localization.today,
                                  style: textStyles.body3.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: const EdgeInsetsDirectional.only(top: 8),
                          child: Text(
                            localization.nameCreatedGroup('You'),
                            style: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const Expanded(child: SizedBox()),
                      ],
                    ),
                  ),
                ),
                ChatInputField(
                  onSendMessage: (message) {
                    // Handle sending message in group
                    print('Group message sent: $message');
                  },
                  onAttachmentPressed: _toggleAttachmentMenu,
                  onVoicePressed: () {
                    // TODO: Handle voice message in group
                    // implement voice message sending logic here
                  },
                ),
              ],
            ),
            if (_showAttachmentMenu)
              PositionedDirectional(
                bottom: MediaQuery.of(context).viewInsets.bottom +
                    inputFieldHeight +
                    attachmentMenuPadding,
                start: MediaQuery.of(context).size.width * 0.02,
                end: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed: _handleCameraPress,
                  onRecordPressed: () {
                    // Handle record action
                    _toggleAttachmentMenu();
                    print('Record pressed in group');
                  },
                  onContactPressed: () {
                    // Handle contact action
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed: _handleGalleryPress,
                  onLocationPressed: () {
                    // Handle location action
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed: _handleDocumentPress,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
