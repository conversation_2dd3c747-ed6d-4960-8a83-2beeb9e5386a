import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/search%20bar/search_bar.dart';

/// Contact selection modal component for chat attachments
/// Displays a full-screen bottom modal with searchable contact list
/// organized alphabetically with checkboxes for selection
class ContactSelectionModal extends StatefulWidget {
  final Function(List<Contact>) onContactsSelected;

  const ContactSelectionModal({
    super.key,
    required this.onContactsSelected,
  });

  @override
  State<ContactSelectionModal> createState() => _ContactSelectionModalState();
}

class _ContactSelectionModalState extends State<ContactSelectionModal> {
  final TextEditingController _searchController = TextEditingController();
  List<Contact> _allContacts = [];
  List<Contact> _filteredContacts = [];
  Set<String> _selectedContactIds = <String>{};

  @override
  void initState() {
    super.initState();
    _initializeContacts();
    _filteredContacts = _allContacts;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Initialize sample contacts data
  /// In real app, this would fetch from contacts API or database
  void _initializeContacts() {
    _allContacts = [
      Contact(
          id: '1',
          name: 'Aillan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '2',
          name: 'Billan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '3',
          name: 'Cillan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '4',
          name: 'Cillan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '5',
          name: 'Cillan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '6',
          name: 'Eillan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '7',
          name: 'Eillan James',
          profileImage: 'assets/images/person.png'),
      Contact(
          id: '8',
          name: 'Eillan James',
          profileImage: 'assets/images/person.png'),
    ];
  }

  /// Filter contacts based on search query
  void _filterContacts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredContacts = _allContacts;
      } else {
        _filteredContacts = _allContacts
            .where((contact) =>
                contact.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  /// Toggle contact selection
  void _toggleContactSelection(Contact contact) {
    setState(() {
      if (_selectedContactIds.contains(contact.id)) {
        _selectedContactIds.remove(contact.id);
      } else {
        _selectedContactIds.add(contact.id);
      }
    });
  }

  /// Group contacts alphabetically by first letter
  Map<String, List<Contact>> _groupContactsAlphabetically() {
    final Map<String, List<Contact>> groupedContacts = {};

    for (final contact in _filteredContacts) {
      final firstLetter = contact.name[0].toUpperCase();
      if (!groupedContacts.containsKey(firstLetter)) {
        groupedContacts[firstLetter] = [];
      }
      groupedContacts[firstLetter]!.add(contact);
    }

    // Sort the keys alphabetically
    final sortedKeys = groupedContacts.keys.toList()..sort();
    final sortedMap = <String, List<Contact>>{};
    for (final key in sortedKeys) {
      sortedMap[key] = groupedContacts[key]!;
    }

    return sortedMap;
  }

  /// Handle close button press
  void _handleClose() {
    final selectedContacts = _allContacts
        .where((contact) => _selectedContactIds.contains(contact.id))
        .toList();
    widget.onContactsSelected(selectedContacts);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final mediaQuery = MediaQuery.of(context);
    final groupedContacts = _groupContactsAlphabetically();

    return Container(
      height: mediaQuery.size.height -
          mediaQuery.padding.top -
          50, // Account for status bar + extra spacing
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Top handle indicator
          Container(
            margin: const EdgeInsets.only(top: 8, bottom: 8),
            width: 150,
            height: 4,
            decoration: BoxDecoration(
              color: colors.strokeColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Search bar
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomSearchBar(
              controller: _searchController,
              hintText: localization.searching,
              height: 44,
              margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
              onChanged: _filterContacts,
              bgColor: colors.background,
            ),
          ),

          // Contacts list
          Expanded(
            child: _filteredContacts.isEmpty
                ? _buildEmptyState(colors, textStyles, localization)
                : _buildContactsList(groupedContacts, colors, textStyles),
          ),

          // Close button
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 32),
            width: double.infinity,
            height: 50,
            child: OutlinedButton(
              onPressed: _handleClose,
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: colors.tertiaryText,
                  width: 1,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                backgroundColor: Colors.transparent,
              ),
              child: Text(
                localization.close,
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state when no contacts found
  Widget _buildEmptyState(
      AppColors colors, TextStyles textStyles, AppLocalizations localization) {
    return Center(
      child: Text(
        localization.noContactsFound,
        style: textStyles.body.copyWith(
          color: colors.tertiaryText,
        ),
      ),
    );
  }

  /// Build the contacts list grouped alphabetically
  Widget _buildContactsList(
    Map<String, List<Contact>> groupedContacts,
    AppColors colors,
    TextStyles textStyles,
  ) {
    return ListView.builder(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      itemCount: groupedContacts.length,
      itemBuilder: (context, index) {
        final letter = groupedContacts.keys.elementAt(index);
        final contacts = groupedContacts[letter]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Alphabetical section header
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(0, 18, 0, 12),
              child: Text(
                letter,
                style: textStyles.headline4.copyWith(
                  color: colors.primaryText,
                ),
              ),
            ),

            // Contacts in this section
            ...contacts.map((contact) => _buildContactItem(
                  contact,
                  colors,
                  textStyles,
                )),
          ],
        );
      },
    );
  }

  /// Build individual contact item
  Widget _buildContactItem(
    Contact contact,
    AppColors colors,
    TextStyles textStyles,
  ) {
    final isSelected = _selectedContactIds.contains(contact.id);

    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _toggleContactSelection(contact),
          child: Container(
            padding: const EdgeInsetsDirectional.fromSTEB(
                12, 8, 16, 8), // Reduced padding for less height
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // Profile picture
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      contact.profileImage,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        // Fallback if image fails to load
                        return Container(
                          color: colors.primaryVariant,
                          child: Icon(
                            Icons.person,
                            color: colors.primary,
                            size: 24,
                          ),
                        );
                      },
                    ),
                  ),
                ),

                // Name
                Expanded(
                  child: Container(
                    margin: const EdgeInsetsDirectional.fromSTEB(6, 0, 0, 0),
                    child: Text(
                      contact.name,
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

                // Checkbox
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color:
                          colors.primary, // Always use primary color for border
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    color: isSelected ? colors.primary : Colors.transparent,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          color: colors.background,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Contact model class
class Contact {
  final String id;
  final String name;
  final String profileImage;

  Contact({
    required this.id,
    required this.name,
    required this.profileImage,
  });
}
