import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:solar_icons/solar_icons.dart';

/// Attachment menu component
class AttachmentMenu extends StatelessWidget {
  final VoidCallback? onCameraPressed;
  final VoidCallback? onRecordPressed;
  final VoidCallback? onContactPressed;
  final VoidCallback? onGalleryPressed;
  final VoidCallback? onLocationPressed;
  final VoidCallback? onDocumentPressed;

  const AttachmentMenu({
    super.key,
    this.onCameraPressed,
    this.onRecordPressed,
    this.onContactPressed,
    this.onGalleryPressed,
    this.onLocationPressed,
    this.onDocumentPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width:
          MediaQuery.of(context).size.width * 0.96, // Match input field width
      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      child: Container(
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        padding: const EdgeInsetsDirectional.fromSTEB(20, 20, 20, 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // First row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  context,
                  SolarIconsBold.cameraMinimalistic,
                  localization.camera,
                  colors.primary,
                  onCameraPressed,
                ),
                _buildAttachmentOption(
                  context,
                  SolarIconsBold.microphone2,
                  localization.record,
                  colors.primary,
                  onRecordPressed,
                ),
                _buildAttachmentOption(
                  context,
                  SolarIconsBold.user,
                  localization.contact,
                  colors.primary,
                  onContactPressed,
                ),
              ],
            ),

            Container(
              margin: const EdgeInsetsDirectional.only(top: 24),
            ),

            // Second row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  context,
                  SolarIconsBold.gallery,
                  localization.gallery,
                  colors.primary,
                  onGalleryPressed,
                ),
                _buildAttachmentOption(
                  context,
                  SolarIconsBold.mapPoint,
                  localization.myLocation,
                  colors.primary,
                  onLocationPressed,
                ),
                _buildAttachmentOption(
                  context,
                  SolarIconsBold.fileText,
                  localization.document,
                  colors.primary,
                  onDocumentPressed,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds individual attachment option with icon and label
  Widget _buildAttachmentOption(
    BuildContext context,
    IconData icon,
    String label,
    Color iconColor,
    VoidCallback? onPressed,
  ) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 80,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon container with background
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: colors.primaryVariant,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 26,
                color: iconColor,
              ),
            ),

            // Label text
            Container(
              margin: const EdgeInsetsDirectional.only(top: 8),
              child: Text(
                label,
                style: textStyles.body3.copyWith(
                  color: colors.primaryText,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
