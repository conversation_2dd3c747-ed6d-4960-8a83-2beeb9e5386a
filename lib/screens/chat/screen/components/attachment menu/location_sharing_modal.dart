import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ako_basma/util/location/location.dart';
import 'package:geolocator/geolocator.dart'; // Add geolocator import

/// Location sharing modal component for chat attachments
class LocationSharingModal extends StatefulWidget {
  final Function(String locationType, Map<String, dynamic> locationData)
      onLocationSelected;

  const LocationSharingModal({
    super.key,
    required this.onLocationSelected,
  });

  @override
  State<LocationSharingModal> createState() => _LocationSharingModalState();
}

class _LocationSharingModalState extends State<LocationSharingModal> {
  // Added for Google Map
  GoogleMapController? _mapController;
  LatLng? _currentLocation;
  bool _isLoadingLocation = true;

  @override
  void initState() {
    super.initState();
    // Use regional default location instead of Baghdad coordinates
    _currentLocation = getRegionalDefaultLocation();
    _getCurrentLocation();
  }

  String? _getMapStyle(BuildContext context) {
    if (Theme.of(context).brightness == Brightness.dark) {
      return '[{"elementType":"geometry","stylers":[{"color":"#212121"}]},{"elementType":"labels.icon","stylers":[{"visibility":"off"}]},{"elementType":"labels.text.fill","stylers":[{"color":"#757575"}]},{"elementType":"labels.text.stroke","stylers":[{"color":"#212121"}]},{"featureType":"administrative","elementType":"geometry","stylers":[{"color":"#757575"}]},{"featureType":"road","elementType":"geometry.fill","stylers":[{"color":"#2c2c2c"}]},{"featureType":"water","elementType":"geometry","stylers":[{"color":"#000000"}]}å]';
    }
    return null;
  }

  Future<void> _getCurrentLocation() async {
    try {
      setState(() {
        _isLoadingLocation = true;
      });

      final loc = await getCurrentLocation(
        accuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      if (!mounted) return;
      if (loc != null) {
        setState(() {
          _currentLocation = loc;
          _isLoadingLocation = false;
        });
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(target: loc, zoom: 16.0),
          ),
        );
      } else {
        setState(() {
          _isLoadingLocation = false;
          _currentLocation = getRegionalDefaultLocation();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
          _currentLocation = getRegionalDefaultLocation();
        });
      }
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    final style = _getMapStyle(context);
    if (style != null) {
      controller.setMapStyle(style);
    }
    if (_currentLocation != null) {
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: _currentLocation!, zoom: 16.0),
        ),
      );
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  /// Handle live location sharing
  void _handleLiveLocationShare() {
    widget.onLocationSelected('live_location', {
      'type': 'live',
      'duration': '8 hours', // Default live location duration
    });
    Navigator.pop(context);
  }

  /// Handle current location sharing
  void _handleCurrentLocationShare() {
    final loc = _currentLocation ?? const LatLng(33.8938, 44.3661);
    widget.onLocationSelected('current_location', {
      'type': 'current',
      'accuracy': '14 metres',
      'latitude': loc.latitude,
      'longitude': loc.longitude,
    });
    Navigator.pop(context);
  }

  /// Handle close button press
  void _handleClose() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final mediaQuery = MediaQuery.of(context);
    final localization = AppLocalizations.of(context)!;

    return Container(
      constraints: BoxConstraints(
        maxHeight: mediaQuery.size.height * 0.75, // Increased max height
        minHeight: mediaQuery.size.height * 0.5, // Minimum height
      ),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top handle indicator
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 8),
            width: 150,
            height: 4,
            decoration: BoxDecoration(
              color: colors.strokeColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Scrollable content
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Map/Location Image Container
                  Container(
                    margin: const EdgeInsetsDirectional.fromSTEB(16, 6, 16, 0),
                    height: 250,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        children: [
                          // location on google map
                          GoogleMap(
                            onMapCreated: _onMapCreated,
                            initialCameraPosition: CameraPosition(
                              target: _currentLocation ??
                                  const LatLng(33.3152, 44.3661),
                              zoom: 16.0,
                            ),
                            myLocationEnabled: false,
                            myLocationButtonEnabled: false,
                            zoomControlsEnabled: false,
                            scrollGesturesEnabled: true,
                            rotateGesturesEnabled: false,
                            tiltGesturesEnabled: false,
                            mapType: MapType.normal,
                          ),

                          if (_isLoadingLocation)
                            Container(
                              color: Colors.black.withOpacity(0.3),
                              child: const Center(
                                  child: CircularProgressIndicator()),
                            ),

                          if (!_isLoadingLocation && _currentLocation != null)
                            Center(
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: colors.primary.withOpacity(0.2),
                                ),
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: colors.primary.withOpacity(0.7),
                                  ),
                                  child: CircleAvatar(
                                    radius: 12,
                                    backgroundColor: colors.primary,
                                    child: const Icon(
                                      Icons.person,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  // Send Your Live Location Container
                  Container(
                    margin: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 0),
                    width: double.infinity,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: _handleLiveLocationShare,
                        child: Container(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16, 14, 16, 14),
                          decoration: BoxDecoration(
                            color: colors.background,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: colors.primaryVariant,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            localization.sendYourLiveLocation,
                            style: textStyles.body2.copyWith(
                              color: colors.secondaryText,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Nearby Places Section
                  Container(
                    margin:
                        const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                    width: double.infinity,
                    child: Text(
                      localization.nearbyPlaces,
                      style: textStyles.headline4.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ),

                  // Send Your Current Location Container
                  Container(
                    margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                    width: double.infinity,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: _handleCurrentLocationShare,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 14),
                          decoration: BoxDecoration(
                            color: colors.background,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: colors.primaryVariant,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Main text
                              Text(
                                localization.sendYourCurrentLocation,
                                style: textStyles.body2.copyWith(
                                  color: colors.secondaryText,
                                ),
                              ),

                              // Subtitle text
                              Container(
                                margin: const EdgeInsets.only(top: 10),
                                child: Text(
                                  'Approximate to 14 meters',
                                  style: textStyles.body3.copyWith(
                                    color: colors.tertiaryText,
                                    fontSize: 10,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Close button
          Container(
            margin: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 32),
            width: double.infinity,
            height: 56,
            child: OutlinedButton(
              onPressed: _handleClose,
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: colors.tertiaryText,
                  width: 1,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                backgroundColor: Colors.transparent,
              ),
              child: Text(
                localization.close,
                style: textStyles.body.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
