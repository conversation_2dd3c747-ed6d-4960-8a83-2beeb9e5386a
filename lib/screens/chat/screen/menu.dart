// import 'package:flutter/material.dart';
// import 'package:flutter/cupertino.dart';

// class AppMenu extends StatefulWidget {
//   const AppMenu({super.key});

//   @override
//   State<AppMenu> createState() => _AppMenuState();
// }

// class _AppMenuState extends State<AppMenu> {
//   @override
//   void initState() {
//     super.initState();
//     // Move MediaQuery access to post-frame callback
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       _expand();
//     });
//   }

//   double _height = 0;
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         ...ListTile.divideTiles(
//           context: context,
//           tiles: [
//             ListTile(
//               title: Text('Generate response '),
//               trailing: Transform.scale(
//                 scale: 0.8,
//                 child: CupertinoSwitch(
//                   onChanged: (value) {},
//                   value: true,
//                 ),
//               ),
//             )
//           ],
//         )
//       ],
//     );
//   }

//   void _expand() {
//     if (mounted) {
//       setState(() {
//         _height = MediaQuery.of(context).size.height * 0.6;
//       });
//     }
//   }
// }
