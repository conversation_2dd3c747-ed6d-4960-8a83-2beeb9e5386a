import 'dart:io';

import 'package:flutter/material.dart';
import 'package:remixicon/remixicon.dart';
import 'package:image_picker/image_picker.dart';

import '../../../styles/colors.dart';

/// declare necessary permissions for photo lib and camera access.
/// see docs of [image_picker]
class ImagePickerPrompt extends StatelessWidget {
  const ImagePickerPrompt({
    super.key,
    required this.onImagePicked,
  });

  final void Function(File) onImagePicked;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Choose where to select image from',
            style: textStyles(context).titleMedium!.copyWith(fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 18),
          LayoutBuilder(builder: (ctx, constraints) {
            final wide = constraints.maxWidth > 500;
            // final btns =
            final cameraBtn = _buildButton(
                'Camera', 'Take a photo from camera now', Remix.camera_3_line,
                () {
              Navigator.pop(context);
              _pickImage(true);
              // context.go('/home/<USER>/referMember');
              // Navigator.push(
              //   context,
              //   MaterialPageRoute(
              //     builder: (context) {
              //       return ReferMemberScreen();
              //     },
              //   ),
              // );
            }, context);
            final libraryBtn = _buildButton(
                'Library',
                'Choose a photo from photo library',
                // Love a place? Spread the word & get some sweet rewards!
                Remix.gallery_line, () {
              Navigator.pop(context);
              _pickImage(false);

              // Navigator.push(
              //   context,
              //   MaterialPageRoute(
              //     builder: (context) {
              //       return ReferPartnerScreen();
              //     },
              //   ),
              // );
            }, context);
            return wide
                ? IntrinsicHeight(
                    child: Row(
                      children: [
                        Expanded(
                          child: cameraBtn,
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Expanded(
                          child: libraryBtn,
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      cameraBtn,
                      const SizedBox(
                        height: 10,
                      ),
                      libraryBtn,
                      const SizedBox(
                        height: 50,
                      ),
                    ],
                  );
          })
        ],
      ),
    );
  }

  Widget _buildButton(
    String name,
    String desc,
    IconData icon,
    void Function() onTap,
    BuildContext context,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            width: 0.3,
            color: colors(context).outline,
          ),
        ),
        child: Stack(
          children: [
            // Positioned(
            //     top: -25,
            //     right: -30,
            //     bottom: -30,
            //     // top: 0,
            //     // bottom: 0,
            //     child: Opacity(
            //       opacity: 0.5,
            //       child: Image.asset(
            //         image,
            //         fit: BoxFit.cover,
            //         color: Colors.black.withOpacity(0.6),
            //         colorBlendMode: BlendMode.srcATop,

            //         // opacity: AnimationMin(first, next),
            //       ),
            //     )),
            Positioned(
              // top: 0,
              // left: 0,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 25, horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                name,
                                style: textStyles(context)
                                    .titleMedium!
                                    .copyWith(color: colors(context).primary),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          icon,
                          color: colors(context).primary,
                          size: 50,
                        ),
                      ],
                    ),
                    const Divider(),
                    Text(
                      desc,
                      style:
                          textStyles(context).bodySmall!.copyWith(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _pickImage(bool fromCamera) async {
    final ImagePicker picker = ImagePicker();
// Pick an image.

    final XFile? photo = await picker.pickImage(
      source: fromCamera ? ImageSource.camera : ImageSource.gallery,
      maxHeight: 1024,
      maxWidth: 1024,
      imageQuality: 75,
    );
    if (photo != null) {
      onImagePicked(File(photo.path));
      // photo.a;
    }

// Capture a photo.
  }
}
