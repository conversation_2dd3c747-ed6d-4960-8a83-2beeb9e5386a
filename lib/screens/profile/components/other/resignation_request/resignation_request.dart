import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/formatting.dart'; // Add this import
import 'package:ako_basma/util/ui/popups.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:io';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:ako_basma/components/attachment/attachment_placeholder_card.dart';

class ResignationRequest extends StatefulWidget {
  const ResignationRequest({super.key, this.onBack});
  final VoidCallback? onBack;

  @override
  State<ResignationRequest> createState() => _ResignationRequestState();
}

class _ResignationRequestState extends State<ResignationRequest> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final List<File> _attachments = [];
  final _attachmentButtonKey =
      GlobalKey(debugLabel: 'resignationAttachmentKey');

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    amountController.dispose();
    super.dispose();
  }

  Future<void> _pickAttachment() async {
    final res = await showLocalPickerMenu(
      buttonKey: _attachmentButtonKey,
      context: context,
      allowedTypes: ['image', 'pdf'], // Allow images and PDF files
      allowMultiple: true,
      maxSizeInMB: 10,
    );

    if (res != null) {
      setState(() {
        if (res is File) {
          _attachments.add(res);
        }
        if (res is List<File>) {
          _attachments.addAll(res);
        }
      });
      // Files successfully selected and added to attachments list
    }
  }

  void _showTopSnackbar(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: localization.requestSubmittedSuccessfully,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      // Ensures the layout adjusts when keyboard opens
      resizeToAvoidBottomInset: true,
      backgroundColor: colors.background,
      body: Container(
        decoration: BoxDecoration(
          color: colors.background,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            // Header with back button and title
            Container(
              padding: EdgeInsetsDirectional.fromSTEB(
                  16, MediaQuery.of(context).padding.top + 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      if (widget.onBack != null) {
                        widget.onBack!();
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 12),
                  ),
                  Text(
                    localization.resignationRequest,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            // Main content - scrollable with keyboard padding
            Expanded(
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                // Add padding when keyboard is open to ensure content is scrollable
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Container(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
                        child: Column(
                          children: [
                            // Reason for Resignation field
                            TextField(
                              controller: titleController,
                              maxLines: 6,
                              decoration: InputDecoration(
                                contentPadding:
                                    const EdgeInsetsDirectional.fromSTEB(
                                        16, 12, 16, 12),
                                labelText: localization.reasonForResignation,
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.auto,
                                alignLabelWithHint: true,
                                labelStyle: textStyles.body2.copyWith(
                                  color: colors.tertiaryText,
                                ),
                                filled: true,
                                fillColor: colors.backgroundContainer,
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: colors.strokeColor,
                                    width: 1,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: colors.primary,
                                    width: 1,
                                  ),
                                ),
                              ),
                              style: textStyles.body2.copyWith(
                                color: colors.primaryText,
                              ),
                              textAlignVertical: TextAlignVertical.top,
                            ),

                            const SizedBox(
                              height: 16,
                            ),

                            // Additional Notes field
                            TextField(
                              controller: descriptionController,
                              maxLines: 6,
                              decoration: InputDecoration(
                                contentPadding:
                                    const EdgeInsetsDirectional.fromSTEB(
                                        16, 12, 16, 12),
                                labelText: localization.additionalNotes,
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.auto,
                                alignLabelWithHint: true,
                                labelStyle: textStyles.body2.copyWith(
                                  color: colors.tertiaryText,
                                ),
                                filled: true,
                                fillColor: colors.backgroundContainer,
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: colors.strokeColor,
                                    width: 1,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: colors.primary,
                                    width: 1,
                                  ),
                                ),
                              ),
                              style: textStyles.body2.copyWith(
                                color: colors.primaryText,
                              ),
                              textAlignVertical: TextAlignVertical.top,
                            ),

                            const SizedBox(
                              height: 16,
                            ),

                            // Last Working Day field
                            GestureDetector(
                              onTap: () async {
                                final DateTime? picked =
                                    await showDatePickerDialog(
                                        context: context,
                                        initialDate: DateTime.now(),
                                        minDate: DateTime.now(),
                                        maxDate: DateTime(2030)
                                        // firstDate: DateTime.now(),
                                        // lastDate: DateTime(2030),
                                        );
                                if (picked != null) {
                                  amountController.text =
                                      formatDateDmy(picked, context);
                                }
                              },
                              child: AbsorbPointer(
                                child: TextField(
                                  controller: amountController,
                                  decoration: InputDecoration(
                                    contentPadding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            16, 20, 16, 20),
                                    labelText: localization.lastWorkingDay,
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.auto,
                                    labelStyle: textStyles.body2.copyWith(
                                      color: colors.tertiaryText,
                                    ),
                                    filled: true,
                                    fillColor: colors.backgroundContainer,
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color: colors.strokeColor,
                                        width: 1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: BorderSide(
                                        color: colors.primary,
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  style: textStyles.body2.copyWith(
                                    color: colors.primaryText,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // file picker
                      Container(
                        margin: const EdgeInsetsDirectional.fromSTEB(
                            10, 10, 10, 16),
                        child: InkWell(
                          key: _attachmentButtonKey,
                          onTap: _pickAttachment,
                          child: DottedBorder(
                            color: colors.strokeColor,
                            strokeWidth: 1,
                            dashPattern: const [8, 6],
                            borderType: BorderType.RRect,
                            radius: const Radius.circular(8),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  24, 24, 24, 24),
                              decoration: BoxDecoration(
                                color: colors.backgroundContainer,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: colors.background,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/icons/workspace_screen/upload.svg',
                                          width: 24,
                                          height: 24,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    height: 12,
                                  ),
                                  Text(
                                    localization.clickToUpload,
                                    style: textStyles.body2.copyWith(
                                      color: colors.primary,
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Text(
                                    localization.maxFileSizeInMB(25),
                                    style: textStyles.body2.copyWith(
                                      color: colors.tertiaryText,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Display selected attachments - Grid layout with previews
                      if (_attachments.isNotEmpty)
                        Container(
                          margin: const EdgeInsetsDirectional.fromSTEB(
                              10, 0, 10, 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '${localization.attachment} (${_attachments.length})',
                                style: textStyles.body2.copyWith(
                                  color: colors.primaryText,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Attachment grid with proper previews
                              ...List.generate((_attachments.length / 2).ceil(),
                                  (rowIndex) {
                                final startIndex = rowIndex * 2;
                                final endIndex = (startIndex + 2)
                                    .clamp(0, _attachments.length);
                                final filesInRow =
                                    _attachments.sublist(startIndex, endIndex);

                                return Container(
                                  margin: EdgeInsets.only(
                                    bottom: rowIndex <
                                            (_attachments.length / 2).ceil() - 1
                                        ? 16
                                        : 0,
                                  ),
                                  child: Row(
                                    children: [
                                      // First file in row
                                      Expanded(
                                        child: AttachmentPlaceholderCard(
                                          filePath: filesInRow[0].path,
                                          previewWidth: 114,
                                          previewHeight: 100,
                                          onDelete: () => setState(() {
                                            _attachments.removeAt(startIndex);
                                          }),
                                        ),
                                      ),

                                      // Second file in row (if exists)
                                      if (filesInRow.length > 1) ...[
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: AttachmentPlaceholderCard(
                                            filePath: filesInRow[1].path,
                                            previewWidth: 114,
                                            previewHeight: 100,
                                            onDelete: () => setState(() {
                                              _attachments
                                                  .removeAt(startIndex + 1);
                                            }),
                                          ),
                                        ),
                                      ] else ...[
                                        const SizedBox(width: 16),
                                        const Expanded(child: SizedBox()),
                                      ],
                                    ],
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // cancel and submit resignation buttons with safe area padding
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(19, 16, 19, 32),
              child: Row(
                children: [
                  // Cancel Button
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            if (widget.onBack != null) {
                              widget.onBack!();
                            } else {
                              Navigator.of(context).pop();
                            }
                          },
                          child: Center(
                            child: Text(
                              localization.cancel,
                              style: textStyles.body.copyWith(
                                color: colors.secondaryText,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 10),
                  ),
                  // Submit Resignation Button
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        color: colors.error,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () {
                            _showTopSnackbar(context);

                            //  submit resignation
                            Future.delayed(const Duration(milliseconds: 1), () {
                              if (widget.onBack != null) {
                                widget.onBack!();
                              } else {
                                Navigator.of(context, rootNavigator: true)
                                    .pop();
                              }
                            });
                          },
                          child: Center(
                            child: Text(
                              localization.submitResignation,
                              style: textStyles.body2.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
