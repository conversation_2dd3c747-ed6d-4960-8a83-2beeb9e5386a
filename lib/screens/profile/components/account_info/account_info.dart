import 'package:ako_basma/screens/profile/components/account_info/details.dart';
import 'package:ako_basma/screens/profile/components/account_info/edit_info_button.dart';
import 'package:ako_basma/screens/profile/components/account_info/name.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

Widget accountInfo(BuildContext context, {VoidCallback? onEditSuccess}) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  return Container(
    margin: const EdgeInsetsDirectional.only(top: 8),
    height: 255,
    width: double.infinity,
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Padding(
          padding: EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
          child: Name(),
        ),
        const Details(),
        EditInfoButton(onEditSuccess: onEditSuccess),
      ],
    ),
  );
}
