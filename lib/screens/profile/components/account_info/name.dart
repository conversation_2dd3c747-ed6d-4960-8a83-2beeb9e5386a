import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'dart:io';

class Name extends StatefulWidget {
  const Name({super.key});

  @override
  State<Name> createState() => _NameState();
}

class _NameState extends State<Name> {
  final GlobalKey _profilePictureKey = GlobalKey();
  String? _selectedImagePath;

  Future<void> _pickProfilePicture() async {
    final res = await showLocalPickerMenu(
      buttonKey: _profilePictureKey,
      context: context,
      allowedTypes: ['image'],
      allowMultiple: false,
      maxSizeInMB: 5.0,
      maxHeight: 1024,
      maxWidth: 1024,
      imageQuality: 80,
    );

    if (res != null && res is File) {
      setState(() {
        _selectedImagePath = res.path;
      });

      // Here the image will upload to backend
      // For now, it's in local state
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>();
    final textStyles = theme.extension<TextStyles>();

    return Container(
      height: 48,
      width: double.infinity,
      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              GestureDetector(
                key: _profilePictureKey,
                onTap: _pickProfilePicture,
                child: Stack(
                  children: [
                    Container(
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: colors!.tertiaryText,
                          width: 1,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: _selectedImagePath != null &&
                                _selectedImagePath!.isNotEmpty
                            ? Image.file(
                                File(_selectedImagePath!),
                                height: 40,
                                width: 40,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return const ImageContainer(
                                    url: 'https://i.pravatar.cc/150?img=59',
                                    placeholderAsset:
                                        'assets/images/person.png',
                                    height: 40,
                                    width: 40,
                                    fit: BoxFit.cover,
                                  );
                                },
                              )
                            : const ImageContainer(
                                url: 'https://i.pravatar.cc/150?img=59',
                                placeholderAsset: 'assets/images/person.png',
                                height: 40,
                                width: 40,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    PositionedDirectional(
                      end: 0,
                      bottom: 2,
                      child: GestureDetector(
                        onTap: _pickProfilePicture,
                        child: Container(
                          height: 12,
                          width: 12,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: const Color(0xFF006685),
                            border: Border.all(
                              color: Colors.white,
                              width: 1,
                            ),
                          ),
                          child: const Icon(
                            Icons.edit,
                            size: 8,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Nada Jaafar Uday',
                    style: textStyles!.body3.copyWith(
                      color: colors.secondaryText,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'UI/UX Designer',
                    style: textStyles.body3.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            height: 26,
            width: 75,
            padding: const EdgeInsetsDirectional.fromSTEB(2, 2, 2, 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: colors.strokeColor,
                width: 1,
              ),
              color: colors.background,
            ),
            child: Center(
              child: Text(
                'Design Team',
                style: textStyles.body3.copyWith(
                  color: colors.primaryText,
                  fontSize: 10,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
