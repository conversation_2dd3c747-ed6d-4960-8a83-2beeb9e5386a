import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

import 'package:solar_icons/solar_icons.dart';

class AccInfo extends StatefulWidget {
  const AccInfo({super.key});

  @override
  State<AccInfo> createState() => _AccInfoState();
}

class _AccInfoState extends State<AccInfo> {
  // Controllers for the three name fields
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController middleNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  // Focus nodes to track field focus states
  final FocusNode firstNameFocus = FocusNode();
  final FocusNode middleNameFocus = FocusNode();
  final FocusNode lastNameFocus = FocusNode();
  final FocusNode phoneFocus = FocusNode();
  final FocusNode emailFocus = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // Clean up controllers and focus nodes
    firstNameController.dispose();
    middleNameController.dispose();
    lastNameController.dispose();
    phoneNumberController.dispose();
    emailController.dispose();
    firstNameFocus.dispose();
    middleNameFocus.dispose();
    lastNameFocus.dispose();
    phoneFocus.dispose();
    emailFocus.dispose();
    super.dispose();
  }

  // Custom text field widget with floating label behavior like auth screen
  Widget _buildCustomTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String labelText,
    required AppColors colors,
    required TextStyles textStyles,
    TextInputType? keyboardType,
    Widget? prefixIcon,
    int? flex = 1,
    EdgeInsetsDirectional? margin,
  }) {
    return Expanded(
      flex: flex!,
      child: Container(
        // Custom margin for proper spacing alignment
        margin: margin ?? EdgeInsetsDirectional.zero,
        child: TextField(
          controller: controller,
          focusNode: focusNode,
          keyboardType: keyboardType,
          style: textStyles.body2.copyWith(
            color: colors.primaryText,
          ),
          decoration: InputDecoration(
            contentPadding: const EdgeInsetsDirectional.symmetric(
              horizontal: 16,
              vertical: 18,
            ),
            labelText: labelText,
            floatingLabelBehavior: FloatingLabelBehavior.auto,
            labelStyle: textStyles.body2.copyWith(
              color: colors.tertiaryText,
            ),
            filled: true,
            fillColor: colors.backgroundContainer,
            prefixIcon: prefixIcon,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: colors.strokeColor,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: colors.primary,
                width: 1,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Account Info section title
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.only(bottom: 16),
            padding: const EdgeInsetsDirectional.symmetric(vertical: 4),
            child: Text(
              localization.accountInfo,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              maxLines: 2,
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),

          // Three name fields in a row
          Row(
            children: [
              _buildCustomTextField(
                controller: firstNameController,
                focusNode: firstNameFocus,
                labelText: localization.firstName,
                colors: colors,
                textStyles: textStyles,
                flex: 1,
                // No left margin, 4px right margin for spacing
                margin: const EdgeInsetsDirectional.only(end: 8),
              ),
              _buildCustomTextField(
                controller: middleNameController,
                focusNode: middleNameFocus,
                labelText: localization.middleName,
                colors: colors,
                textStyles: textStyles,
                flex: 1,
                // 4px margins on both sides for center spacing
                margin: const EdgeInsetsDirectional.symmetric(horizontal: 4),
              ),
              _buildCustomTextField(
                controller: lastNameController,
                focusNode: lastNameFocus,
                labelText: localization.lastName,
                colors: colors,
                textStyles: textStyles,
                flex: 1,
                // 4px left margin, no right margin for edge alignment
                margin: const EdgeInsetsDirectional.only(start: 8),
              ),
            ],
          ),

          Container(
            margin: const EdgeInsetsDirectional.only(top: 12),
          ),

          // Phone number field
          TextField(
            controller: phoneNumberController,
            focusNode: phoneFocus,
            keyboardType: TextInputType.phone,
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsetsDirectional.symmetric(
                horizontal: 16,
                vertical: 18,
              ),
              labelText: localization.phoneNumber,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              floatingLabelAlignment: FloatingLabelAlignment.start,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              prefixIcon: Icon(
                SolarIconsOutline.phone,
                color: colors.primaryText,
                size: 24,
              ),
              // suffixIcon: Icon(
              //   SolarIconsOutline.phone,
              //   color: colors.primaryText,
              //   size: 24,
              // ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                  width: 1,
                ),
              ),
            ),
          ),

          Container(
            margin: const EdgeInsetsDirectional.only(top: 12),
          ),

          // Email field
          TextField(
            controller: emailController,
            focusNode: emailFocus,
            keyboardType: TextInputType.emailAddress,
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsetsDirectional.symmetric(
                horizontal: 16,
                vertical: 18,
              ),
              labelText: localization.emailAddress,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              floatingLabelAlignment: FloatingLabelAlignment.start,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              prefixIcon: Icon(
                SolarIconsOutline.letter,
                color: colors.primaryText,
                size: 24,
              ),
              // suffixIcon: isRtl
              //     ? Icon(
              //         SolarIconsOutline.letter,
              //         color: colors.primaryText,
              //         size: 24,
              //       )
              //     : null,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                  width: 1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
