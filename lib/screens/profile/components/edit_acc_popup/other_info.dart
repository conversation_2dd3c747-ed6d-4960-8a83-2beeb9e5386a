import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/components/dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart'
    as AnimatedDropdown;
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:intl/intl.dart';
import 'package:ako_basma/util/ui/formatting.dart';

class OtherInfo extends StatefulWidget {
  const OtherInfo({super.key});

  @override
  State<OtherInfo> createState() => _OtherInfoState();
}

class _OtherInfoState extends State<OtherInfo> {
  final TextEditingController dateOfBirthController = TextEditingController();
  final TextEditingController startDateController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController genderController = TextEditingController();

  // Date values
  DateTime? selectedDateOfBirth;
  DateTime? selectedStartDate;

  // Country dropdown value
  String? selectedCountry;

  // Gender dropdown value
  String? selectedGender;

  @override
  void initState() {
    super.initState();

    //sample country
    // selectedCountry = 'Iraq';
  }

  @override
  void dispose() {
    dateOfBirthController.dispose();
    startDateController.dispose();
    countryController.dispose();
    genderController.dispose();
    super.dispose();
  }

  // date picker
  Future<void> _selectDate(BuildContext context, bool isDateOfBirth) async {
    final DateTime? picked = await showDatePickerDialog(
      context: context,
      initialDate: isDateOfBirth
          ? (selectedDateOfBirth ?? DateTime.now())
          : (selectedStartDate ?? DateTime.now()),
      minDate: DateTime(1950),
      maxDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isDateOfBirth) {
          selectedDateOfBirth = picked;
          // format date to dd/mm/yyyy and yyyy/mm/dd based on language
          dateOfBirthController.text = formatDateDmy(picked, context);
        } else {
          selectedStartDate = picked;
          startDateController.text = formatDateDmy(picked, context);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Other Information section title
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.only(bottom: 16),
            padding: const EdgeInsetsDirectional.symmetric(vertical: 4),
            child: Text(
              localization.otherInfo,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
              ),
              maxLines: 2,
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),

          // Gender dropdown field
          AnimatedDropdown.CustomDropdown<String>(
            hintText: localization.gender,
            items: [
              localization.male,
              localization.female,
            ],
            onChanged: (value) {
              setState(() {
                selectedGender = value;
                genderController.text = value ?? '';
              });
            },
            decoration: AnimatedDropdown.CustomDropdownDecoration(
              closedFillColor: colors.backgroundContainer,
              expandedFillColor: colors.backgroundContainer,
              closedBorder: Border.all(
                color: colors.strokeColor,
              ),
              expandedBorder: Border.all(
                color: colors.strokeColor,
              ),
              closedBorderRadius: BorderRadius.circular(8),
              expandedBorderRadius: BorderRadius.circular(8),
              hintStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              headerStyle: textStyles.body2.copyWith(
                color: colors.primaryText,
              ),
              listItemStyle: textStyles.body2.copyWith(
                color: colors.primaryText,
              ),
              closedSuffixIcon: Icon(
                HugeIcons.strokeRoundedArrowDown01,
                color: colors.primaryText,
                size: 24,
              ),
            ),
          ),

          const SizedBox(
            height: 14,
          ),

          // Date of Birth field
          GestureDetector(
            onTap: () => _selectDate(context, true),
            child: AbsorbPointer(
              child: TextField(
                controller: dateOfBirthController,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsetsDirectional.fromSTEB(
                    16,
                    18,
                    16,
                    18,
                  ),
                  labelText: localization.dateOfBirth,
                  floatingLabelBehavior: FloatingLabelBehavior.auto,
                  labelStyle: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                  filled: true,
                  fillColor: colors.backgroundContainer,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: colors.strokeColor,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: colors.primary,
                    ),
                  ),
                ),
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          const SizedBox(
            height: 14,
          ),

          // Country dropdown field (read-only)
          CustomDropdown(
            items: [localization.iraq, 'India'], //TODO - localize india
            controller: countryController,
            hintText: localization.country,
            enabled: false,
            hintStyle: textStyles.body2.copyWith(
              color: colors.tertiaryText,
            ),
            selectedStyle: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
            fillColor: colors.backgroundContainer,
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: colors.strokeColor,
            ),
            fieldSuffixIcon: Icon(
              HugeIcons.strokeRoundedArrowDown01,
              color: colors.primaryText,
              size: 24,
            ),
            onChanged: (value) {
              // Disabled, so this won't be called
            },
          ),

          const SizedBox(
            height: 14,
          ),

          // Start Date field with date picker (now read-only)
          AbsorbPointer(
            child: TextField(
              controller: startDateController,
              enabled: false,
              readOnly: true,
              decoration: InputDecoration(
                // Increased padding for larger field height
                contentPadding: const EdgeInsetsDirectional.fromSTEB(
                  16,
                  18,
                  16,
                  18,
                ),
                labelText: localization.startDate,
                floatingLabelBehavior: FloatingLabelBehavior.auto,
                labelStyle: textStyles.body2.copyWith(
                  color: colors.tertiaryText,
                ),
                filled: true,
                fillColor: colors.backgroundContainer,

                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: colors.strokeColor,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: colors.primary,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: colors.strokeColor,
                  ),
                ),
              ),
              style: textStyles.body2.copyWith(
                color: colors.primaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
