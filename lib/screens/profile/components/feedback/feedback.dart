import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';

class Feedback extends StatefulWidget {
  const Feedback({super.key});

  @override
  State<StatefulWidget> createState() {
    return _FeedbackState();
  }
}

class _FeedbackState extends State<Feedback> {
  int _activeIndex = 0;
  // double _carouselHeight = 120; // Default height

  // Sample feedback data
  final List<Map<String, dynamic>> feedbackData = [
    {
      'date': DateTime(2025, 3, 15),
      'message':
          'Excellent work on the recent project. Keep up the great effort '
    },
    {
      'date': DateTime(2025, 3, 10),
      'message':
          'Great collaboration with the team and timely delivery of tasks'
    },
    {
      'date': DateTime(2025, 3, 5),
      'message':
          'Outstanding problem-solving skills demonstrated during the sprint'
    },
    {
      'date': DateTime(2025, 2, 28),
      'message': 'Impressive attention to detail and quality of work delivered'
    },
  ];

  Widget _buildFeedbackCard(
      BuildContext context, Map<String, dynamic> feedback) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 12),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  localization.managersFeedback,
                  style: textStyles.body3.copyWith(
                    color: colors.primaryText,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                formatDateDmyDisplay(feedback['date'], context),
                style: textStyles.body3.copyWith(
                  color: colors.tertiaryText,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Feedback message
          Flexible(
            child: Text(
              feedback['message'],
              style: textStyles.body2.copyWith(
                color: colors.secondaryText,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _calculateCardHeight(
      BuildContext context, Map<String, dynamic> feedback) {
    final theme = Theme.of(context);
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // text height
    final textPainter = TextPainter(
      text: TextSpan(
        text: feedback['message'],
        style: textStyles.body2.copyWith(height: 1.4),
      ),
      maxLines: null,
      textDirection: TextDirection.ltr,
    );

    final screenWidth = MediaQuery.sizeOf(context).width;
    final availableWidth = screenWidth - 32 - 8;

    textPainter.layout(maxWidth: availableWidth);

    // Base height: padding + header + margin + text height
    // 16 (top padding) + 20 (header height) + 12 (margin) + text height + 12 (bottom padding)
    return 16 + 20 + 12 + textPainter.height + 12;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Calculate the maximum height needed for all feedback cards
    double maxHeight = 100;
    for (final feedback in feedbackData) {
      final cardHeight = _calculateCardHeight(context, feedback);
      if (cardHeight > maxHeight) {
        maxHeight = cardHeight;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Container(
          width: double.infinity,
          padding: const EdgeInsetsDirectional.fromSTEB(0, 14, 0, 0),
          child: Text(
            localization.feedback,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Feedback card carousel
        SizedBox(
          height: maxHeight,
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              PositionedDirectional(
                start: -16,
                end: -16,
                top: 0,
                bottom: 0,
                child: CarouselSlider(
                  options: CarouselOptions(
                    height: maxHeight,
                    enlargeCenterPage: false,
                    viewportFraction:
                        1 - (24 / MediaQuery.sizeOf(context).width),
                    enableInfiniteScroll: false,
                    disableCenter: true,
                    padEnds: false,
                    autoPlay: false,
                    onPageChanged: (index, reason) {
                      setState(() {
                        _activeIndex = index;
                      });
                    },
                  ),
                  items: feedbackData.map((feedback) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: _buildFeedbackCard(context, feedback),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),
      ],
    );
  }
}
