import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:ako_basma/components/switch/image_switch.dart';

class CustomSettingsTile extends StatelessWidget {
  final String title;
  final bool hasToggle;
  final bool? toggleValue;
  final Function(bool)? onToggleChanged;
  final VoidCallback? onTap;
  final Color? titleColor;
  final bool useCustomSwitch;
  final String? activeThumbImage;
  final String? inactiveThumbImage;
  final String? activeTrackImage;
  final String? inactiveTrackImage;
  final double? tileHeight;
  final EdgeInsetsGeometry? contentPadding;

  const CustomSettingsTile({
    super.key,
    required this.title,
    this.hasToggle = false,
    this.toggleValue,
    this.onToggleChanged,
    this.onTap,
    this.titleColor,
    this.useCustomSwitch = false,
    this.activeThumbImage,
    this.inactiveThumbImage,
    this.activeTrackImage,
    this.inactiveTrackImage,
    this.tileHeight,
    this.contentPadding,
  }) : assert(
            useCustomSwitch == false ||
                (activeThumbImage != null &&
                    inactiveThumbImage != null &&
                    activeTrackImage != null &&
                    inactiveTrackImage != null),
            'Provide all images when useCustomSwitch is true');

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return GestureDetector(
      onTap: hasToggle ? null : onTap,
      child: Container(
        height: tileHeight ?? 56,
        padding: contentPadding ??
            const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: textStyles.body2.copyWith(
                  color: titleColor ?? colors.secondaryText,
                ),
              ),
            ),
            if (hasToggle)
              Transform.scale(
                scale: useCustomSwitch ? 0.89 : 0.8,
                child: useCustomSwitch
                    ? ImageSwitch(
                        value: toggleValue ?? false,
                        onChanged: (val) => onToggleChanged?.call(val),
                        activeTrackImage: activeTrackImage!,
                        inactiveTrackImage: inactiveTrackImage!,
                        activeThumbImage: activeThumbImage!,
                        inactiveThumbImage: inactiveThumbImage!,
                        width: 47,
                        height: 28,
                      )
                    : CupertinoSwitch(
                        value: toggleValue ?? false,
                        onChanged: onToggleChanged,
                        activeTrackColor: colors.primary,
                        thumbColor: theme.colorScheme.onPrimary,
                        inactiveTrackColor: colors.strokeColor,
                      ),
              ),
          ],
        ),
      ),
    );
  }
}
