import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:solar_icons/solar_icons.dart';

/// HR Chat input field component
class HrChatInputField extends StatefulWidget {
  final Function(String) onSendMessage;
  final VoidCallback onAttachmentPressed;

  const HrChatInputField({
    super.key,
    required this.onSendMessage,
    required this.onAttachmentPressed,
  });

  @override
  State<HrChatInputField> createState() => _HrChatInputFieldState();
}

class _HrChatInputFieldState extends State<HrChatInputField> {
  final TextEditingController _messageController = TextEditingController();
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    // Listen to text changes to enable/disable send button
    _messageController.addListener(() {
      setState(() {
        _hasText = _messageController.text.trim().isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  /// Handle send message action
  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty) {
      widget.onSendMessage(message);
      _messageController.clear(); // Clear input after sending
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width: MediaQuery.of(context).size.width *
          0.96, // Responsive width as per user rules
      decoration: BoxDecoration(
        color: colors.background,
      ),
      padding: const EdgeInsetsDirectional.fromSTEB(16, 18, 16, 18),
      child: SafeArea(
        top: false,
        child: Container(
          constraints: const BoxConstraints(
            minHeight: 50,
          ),
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colors.strokeColor,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Attachment button
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: widget.onAttachmentPressed,
                  child: Container(
                    padding: const EdgeInsetsDirectional.all(16),
                    child: Icon(
                      SolarIconsOutline.paperclip,
                      size: 24,
                      color: colors.tertiaryText,
                    ),
                  ),
                ),
              ),

              // Text input field
              Expanded(
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(
                    8,
                    22,
                    8,
                    22,
                  ),
                  child: TextField(
                    controller: _messageController,
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    decoration: InputDecoration(
                      hintText: localization.writeAMessage,
                      hintStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    maxLines: 4,
                    minLines: 1,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
              ),

              // Send button - always shows send icon (no mic icon for HR chat)
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: _hasText
                      ? _sendMessage
                      : null, // Only active when there's text
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Icon(
                      SolarIconsOutline.plain,
                      size: 24,
                      color: colors.primary,
                      // _hasText
                      //     ? colors.primary
                      //     : colors
                      //         .tertiaryText, // Visual feedback for enabled/disabled state
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
