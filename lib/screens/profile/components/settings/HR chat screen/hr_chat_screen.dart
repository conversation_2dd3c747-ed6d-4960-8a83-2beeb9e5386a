import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_approval_message.dart';
import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_messages_list.dart';
import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_chat_input_field.dart';
import 'package:ako_basma/screens/profile/components/settings/HR%20chat%20screen/hr_chat_header.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'dart:io';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

/// HR Chat Screen
class HrChatScreen extends StatefulWidget {
  const HrChatScreen({super.key});

  @override
  State<HrChatScreen> createState() => _HrChatScreenState();
}

class _HrChatScreenState extends State<HrChatScreen> {
  bool _showAttachmentMenu = false;

  static const double inputFieldHeight = 60.0;
  static const double attachmentMenuPadding = 50.0;

  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  void _handleSendMessage(String message) {
    print('Message sent to HR: $message');
  }

  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu();

      final File? image = await ImageService.captureFromCamera();

      if (image != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image captured successfully for HR chat!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture image: ${e.toString()}');
      }
    }
  }

  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu();

      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully for HR chat!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu();

      final File? document = await DocumentService.selectDocument();

      if (document != null) {
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('HR Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');

        if (mounted) {
          DocumentService.showSuccessMessage(context,
              'Document "$fileName" selected successfully for HR chat!');
        }
      }
    } catch (e) {
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  void _handleContactPress() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return ContactSelectionModal(
          onContactsSelected: (selectedContacts) {
            print('HR Contacts selected: ${selectedContacts.length}');
            for (var contact in selectedContacts) {
              print('Contact: ${contact.name} - ID: ${contact.id}');
            }
          },
        );
      },
    );
  }

  void _handleLocationPress() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return LocationSharingModal(
          onLocationSelected: (locationType, locationData) {
            print('HR Location type: $locationType');
            print('Location data: $locationData');
            if (locationData.containsKey('latitude') &&
                locationData.containsKey('longitude')) {
              print(
                  'Coordinates: ${locationData['latitude']}, ${locationData['longitude']}');
            }
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return GestureDetector(
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            Column(
              children: [
                HrChatHeader(
                  onBackPressed: () => Navigator.pop(context),
                ),
                const Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        HrApprovalMessage(),
                        HrMessagesList(),
                      ],
                    ),
                  ),
                ),
                HrChatInputField(
                  onSendMessage: _handleSendMessage,
                  onAttachmentPressed: _toggleAttachmentMenu,
                ),
              ],
            ),
            if (_showAttachmentMenu)
              PositionedDirectional(
                bottom: MediaQuery.of(context).viewInsets.bottom +
                    inputFieldHeight +
                    attachmentMenuPadding,
                start: MediaQuery.of(context).size.width * 0.02,
                end: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed: _handleCameraPress,
                  onRecordPressed: () {
                    _toggleAttachmentMenu();
                    print('Record pressed in HR chat');
                  },
                  onContactPressed: () {
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed: _handleGalleryPress,
                  onLocationPressed: () {
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed: _handleDocumentPress,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
