import 'package:ako_basma/screens/profile/components/settings/chat_with_hr.dart';
import 'package:ako_basma/screens/profile/components/settings/custom_settings_tile.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart';
import 'package:ako_basma/providers/language/language_provider.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hugeicons/hugeicons.dart';

class Settings extends ConsumerStatefulWidget {
  const Settings({super.key});

  @override
  ConsumerState<Settings> createState() => _SettingsState();
}

class _SettingsState extends ConsumerState<Settings> {
  bool _notificationsEnabled = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localization.settings,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
            child: Column(
              children: [
                CustomSettingsTile(
                  title: localization.notifications,
                  hasToggle: true,
                  toggleValue: _notificationsEnabled,
                  onToggleChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    final isDarkMode = ref.watch(isDarkModeProvider);

                    return CustomSettingsTile(
                      title: localization.themeMode,
                      hasToggle: true,
                      toggleValue: isDarkMode,
                      useCustomSwitch: true,
                      tileHeight: 65,
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 20, 12),
                      activeTrackImage: 'assets/images/day.svg',
                      inactiveTrackImage: 'assets/images/night.svg',
                      activeThumbImage: 'assets/images/sun.svg',
                      inactiveThumbImage: 'assets/images/moon.svg',
                      onToggleChanged: (value) async {
                        await ref
                            .read(themeModeProvider.notifier)
                            .toggleTheme(value);
                      },
                    );
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    final languageNotifier =
                        ref.watch(languageProvider.notifier);
                    final currentLanguage = ref.watch(languageProvider);

                    return Container(
                      height: 56,
                      padding:
                          const EdgeInsetsDirectional.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.primaryVariant,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              localization.language,
                              style: textStyles.body2.copyWith(
                                color: colors.secondaryText,
                              ),
                            ),
                          ),
                          DropdownButton<String>(
                            value: currentLanguage.languageCode,
                            icon: Icon(
                              HugeIcons.strokeRoundedArrowDown01,
                              color: colors.primary,
                            ),
                            underline: Container(),
                            style: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                            dropdownColor: colors.backgroundContainer,
                            borderRadius: BorderRadius.circular(8),
                            elevation: 8,
                            items: [
                              DropdownMenuItem(
                                value: 'ar',
                                child: Text(
                                  'العربية',
                                  style: textStyles.body2.copyWith(
                                    color: currentLanguage.languageCode == 'ar'
                                        ? colors.primary
                                        : colors.tertiaryText,
                                  ),
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'ku',
                                child: Text(
                                  'كوردى',
                                  style: textStyles.body2.copyWith(
                                    color: currentLanguage.languageCode == 'ku'
                                        ? colors.primary
                                        : colors.tertiaryText,
                                  ),
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'en',
                                child: Text(
                                  'English',
                                  style: textStyles.body2.copyWith(
                                    color: currentLanguage.languageCode == 'en'
                                        ? colors.primary
                                        : colors.tertiaryText,
                                  ),
                                ),
                              ),
                            ],
                            onChanged: (String? newValue) async {
                              if (newValue != null) {
                                await languageNotifier.changeLanguage(newValue);
                              }
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                CustomSettingsTile(
                  title: localization.reportAProblem,
                  onTap: () {
                    print('Report A Problem tapped');
                  },
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(top: 8),
                ),
                CustomSettingsTile(
                  title: localization.chatWithHr,
                  onTap: () {
                    showAppDialog(
                      context,
                      (ctx, sc) => const ChatWithHR(),
                      useRootNavigator: true,
                      contentPadding: const EdgeInsets.fromLTRB(24, 24, 24, 20),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
