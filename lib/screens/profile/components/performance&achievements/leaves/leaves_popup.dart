import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/leaves/leaves_card.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/leaves/leave_status.dart';

class LeavesPopup extends StatefulWidget {
  final VoidCallback? onBack;

  const LeavesPopup({super.key, this.onBack});

  @override
  State<LeavesPopup> createState() => _LeavesPopupState();
}

class _LeavesPopupState extends State<LeavesPopup> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    DirectionHelpers.getBackArrowIcon(context),
                    color: colors.primaryText,
                    size: 32,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(12, 0, 0, 0),
              ),
              Text(
                localization.leavesTaken,
                style: textStyles.headline3.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              children: List.generate(
                6,
                (index) {
                  final statusSequence = [
                    LeaveStatus.confirmed,
                    LeaveStatus.pending,
                    LeaveStatus.confirmed,
                    LeaveStatus.rejected,
                    LeaveStatus.pending,
                    LeaveStatus.confirmed,
                  ];
                  return buildLeaveCard(
                    context,
                    status: statusSequence[index],
                  );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
