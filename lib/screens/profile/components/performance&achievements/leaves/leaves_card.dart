import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/leaves/leave_status.dart';

LeaveStatusStyle _statusStyle(BuildContext context, LeaveStatus status) {
  final colors = Theme.of(context).extension<AppColors>()!;
  final localization = AppLocalizations.of(context)!;

  switch (status) {
    case LeaveStatus.confirmed:
      return LeaveStatusStyle(
        colors.success,
        colors.successContainer,
        localization.confirmed,
      );
    case LeaveStatus.pending:
      return LeaveStatusStyle(
        colors.warning,
        colors.warningContainer,
        localization.pending,
      );
    case LeaveStatus.rejected:
      return LeaveStatusStyle(
        colors.error,
        colors.errorContainer,
        localization.rejected,
      );
  }
}

Widget buildLeaveCard(BuildContext context, {required LeaveStatus status}) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  final localization = AppLocalizations.of(context)!;

  final LeaveStatusStyle statusStyle = _statusStyle(context, status);

  return Container(
    width: double.infinity,
    height: 165,
    margin: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 4),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Align(
        alignment: AlignmentDirectional.topStart,
        child: Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CircleAvatar(
                    radius: 20,
                    backgroundImage: AssetImage('assets/images/person.png'),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Jane Cooper',
                        style: textStyles.body2.copyWith(
                          color: colors.primaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Sick Leave',
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 12),
                child: Row(
                  children: [
                    Icon(
                      SolarIconsOutline.calendarMinimalistic,
                      size: 16,
                      color: colors.secondaryText,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      localization.leaveFrom,
                      style: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      formatDateDmyText(DateTime(2025, 1, 2), context,
                          shortMonth: true),
                      style: textStyles.body2.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 12),
                child: Row(
                  children: [
                    Icon(
                      SolarIconsOutline.calendarMinimalistic,
                      size: 16,
                      color: colors.secondaryText,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      localization.leaveTo,
                      style: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      formatDateDmyText(DateTime(2025, 1, 3), context,
                          shortMonth: true),
                      style: textStyles.body2.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 8),
                height: 24,
                child: Row(
                  children: [
                    Icon(
                      SolarIconsOutline.calendarMinimalistic,
                      size: 16,
                      color: colors.secondaryText,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      localization.days,
                      style: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '1',
                      style: textStyles.body2.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                    const Spacer(),
                    OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        foregroundColor: statusStyle.foreground,
                        backgroundColor: statusStyle.background,
                        minimumSize: const Size(63, 23),
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
                        side: BorderSide(
                          width: 1,
                          color: statusStyle.background,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {},
                      child: Text(
                        statusStyle.label,
                        style: textStyles.body3.copyWith(
                          color: statusStyle.foreground,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
