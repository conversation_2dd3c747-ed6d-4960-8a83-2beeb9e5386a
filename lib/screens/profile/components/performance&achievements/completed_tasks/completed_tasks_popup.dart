import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/screens/tasks/components/task_search.dart';

class CompletedTasksPopup extends StatefulWidget {
  final VoidCallback? onBack;

  const CompletedTasksPopup({super.key, this.onBack});

  @override
  State<CompletedTasksPopup> createState() => _CompletedTasksPopupState();
}

class _CompletedTasksPopupState extends State<CompletedTasksPopup> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 8),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    DirectionHelpers.getBackArrowIcon(context),
                    color: colors.primaryText,
                    size: 32,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(12, 0, 0, 0),
              ),
              Text(
                localization.completedTasks,
                style: textStyles.headline3.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: TaskSearchPopup(
            showCancel: false,
            title: localization.completedTasks,
          ),
        ),
      ],
    );
  }
}
