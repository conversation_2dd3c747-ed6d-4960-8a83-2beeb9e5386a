import 'package:ako_basma/screens/home/<USER>/components/salary_popup.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/expenses/specific_expense_card_popup.dart';
import 'package:ako_basma/util/ui/popups.dart';

class ExpensesCard extends StatefulWidget {
  final String status;

  const ExpensesCard({super.key, required this.status});

  @override
  State<ExpensesCard> createState() => _ExpensesCardState();
}

class _ExpensesCardState extends State<ExpensesCard> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;

    final bool isUnpaid = widget.status.toLowerCase() == 'unpaid';
    final bool isRejected = widget.status.toLowerCase() == 'rejected';

    return GestureDetector(
      onTap: () {
        showAdaptivePopup(
          context,
          (ctx, sc) => SpecificExpenseCardPopup(
            status: widget.status,
            onBack: () => Navigator.pop(ctx),
          ),
          isDismissible: false,
          scrollable: true,
          contentPadding: EdgeInsets.zero,
          topRadius: 0,
          fullScreen: true,
          useRootNavigator: true,
        );
      },
      child: Container(
        width: screenWidth - 16,
        margin: const EdgeInsetsDirectional.fromSTEB(8, 0, 8, 0),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.strokeColor,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section with title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(14, 12, 20, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Figma Subscription',
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 12),
                  ),
                ],
              ),
            ),

            // Amount section
            Align(
              alignment: AlignmentDirectional.centerStart,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(30, 10, 30, 10),
                decoration: BoxDecoration(
                  color: colors.primaryVariant,
                  borderRadius: const BorderRadiusDirectional.only(
                    topEnd: Radius.circular(8),
                    bottomEnd: Radius.circular(8),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      // formatCurrency(15000, context),
                      'IQD',
                      style: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                        fontSize: 12,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 8),
                    ),
                    Text(
                      '15,000',
                      style: textStyles.headline.copyWith(
                        color: colors.primary,
                        fontSize: 32,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom section with date and status
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(20, 12, 20, 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    formatDateDmyDisplay(DateTime(2024, 12, 1), context),
                    style: textStyles.body.copyWith(
                      color: colors.tertiaryText,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsetsDirectional.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: isRejected
                          ? colors.errorContainer
                          : isUnpaid
                              ? colors.warningContainer
                              : colors.successContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      getLabelTranslation(widget.status, context),
                      style: textStyles.body3.copyWith(
                        color: isRejected
                            ? colors.error
                            : isUnpaid
                                ? colors.warning
                                : colors.success,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
