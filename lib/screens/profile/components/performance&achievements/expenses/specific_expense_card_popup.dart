import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:io';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:ako_basma/components/attachment/attachment_grid_card.dart';
import 'package:ako_basma/components/attachment/attachment_placeholder_card.dart';

class SpecificExpenseCardPopup extends StatefulWidget {
  final String status; // 'paid' or 'unpaid'
  const SpecificExpenseCardPopup(
      {super.key, required this.status, this.onBack});
  final VoidCallback? onBack;

  @override
  State<SpecificExpenseCardPopup> createState() =>
      _SpecificExpenseCardPopupState();
}

class _SpecificExpenseCardPopupState extends State<SpecificExpenseCardPopup> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController dateController = TextEditingController();
  final List<File> _attachments = [];
  final _attachmentButtonKey = GlobalKey(debugLabel: 'expenseAttachmentKey');
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Only initialize once to avoid repeated calls
    if (!_hasInitialized) {
      _hasInitialized = true;

      // Pre-fill data for both paid and unpaid expenses
      titleController.text = 'Figma subscription';
      descriptionController.text = 'Monthly subscription for Figma design tool';
      amountController.text = formatCurrency(15000, context);
      dateController.text =
          formatDateDmy(DateTime(2025, 3, 15), context); // DD/MM/YYYY format

      // Add sample PDF attachments for preview demonstration
      // Note: In a real app, these would be actual File objects from the device
      // For demo purposes, we're using dummy file paths to show the preview UI
      setState(() {
        // Add sample attachments to demonstrate PDF preview - showing dummy.pdf twice
        _attachments.addAll([
          File(
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'),
          File(
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'),
        ]);
      });
    }
  }

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    amountController.dispose();
    dateController.dispose();
    super.dispose();
  }

  Future<void> _pickAttachment() async {
    final res = await showLocalPickerMenu(
      buttonKey: _attachmentButtonKey,
      context: context,
      allowedTypes: ['image', 'pdf'], // Allow images and PDF files
      allowMultiple: true,
      maxSizeInMB: 10,
    );

    if (res != null) {
      setState(() {
        if (res is File) {
          _attachments.add(res);
        }
        if (res is List<File>) {
          _attachments.addAll(res);
        }
      });
      // Files successfully selected and added to attachments list
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final bool isPaid = widget.status.toLowerCase() == 'paid';
    final bool isEditable = !isPaid;

    return Container(
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          // Header with back button and title
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (widget.onBack != null) {
                      widget.onBack!();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      DirectionHelpers.getBackArrowIcon(context),
                      color: colors.primaryText,
                      size: 32,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.fromSTEB(12, 0, 0, 0),
                ),
                Text(
                  'Figma Subscription',
                  style: textStyles.headline3.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          // Main content - scrollable
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(8, 0, 8, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
                      child: Column(
                        children: [
                          // Status banner
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                              16,
                              10,
                              16,
                              10,
                            ),
                            decoration: BoxDecoration(
                              color: widget.status.toLowerCase() == 'paid'
                                  ? colors.successContainer
                                  : widget.status.toLowerCase() == 'rejected'
                                      ? colors.errorContainer
                                      : colors.warningContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  margin: const EdgeInsetsDirectional.only(
                                      start: 4),
                                ),
                                Expanded(
                                  child: Text(
                                    getStatusTranslation(
                                        widget.status.toLowerCase(), context),
                                    style: textStyles.body2.copyWith(
                                      color:
                                          widget.status.toLowerCase() == 'paid'
                                              ? colors.success
                                              : widget.status.toLowerCase() ==
                                                      'rejected'
                                                  ? colors.error
                                                  : colors.warning,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            height: 16,
                          ),

                          // Title field
                          TextField(
                            controller: titleController,
                            enabled: isEditable,
                            decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsetsDirectional.fromSTEB(
                                      16, 12, 16, 12),
                              labelText: localization.title,
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),

                          Container(
                            height: 10,
                          ),

                          // Description field
                          TextField(
                            controller: descriptionController,
                            maxLines: 3,
                            enabled: isEditable,
                            decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsetsDirectional.fromSTEB(
                                      16, 12, 16, 12),
                              labelText: localization.description,
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),

                          Container(
                            height: 10,
                          ),

                          // Amount field
                          TextField(
                            controller: amountController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly
                            ],
                            enabled: isEditable,
                            decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsetsDirectional.fromSTEB(
                                      16, 12, 16, 12),
                              labelText: localization
                                  .amount, // TODO: change to localization
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),

                          const SizedBox(height: 10),

                          // Date field
                          TextField(
                            controller: dateController,
                            enabled: isEditable,
                            readOnly: true,
                            decoration: InputDecoration(
                              contentPadding:
                                  const EdgeInsetsDirectional.fromSTEB(
                                      16, 12, 16, 12),
                              labelText: localization.date,
                              floatingLabelBehavior: FloatingLabelBehavior.auto,
                              labelStyle: textStyles.body2.copyWith(
                                color: colors.tertiaryText,
                              ),
                              filled: true,
                              fillColor: colors.backgroundContainer,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.primary,
                                  width: 1,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: colors.strokeColor,
                                  width: 1,
                                ),
                              ),
                            ),
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                            onTap: isEditable
                                ? () async {
                                    // Show date picker when editable
                                    final DateTime? picked =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(2020),
                                      lastDate: DateTime.now()
                                          .add(const Duration(days: 365)),
                                    );
                                    if (picked != null) {
                                      setState(() {
                                        dateController.text =
                                            '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
                                      });
                                    }
                                  }
                                : null,
                          ),
                        ],
                      ),
                    ),

                    // file picker (only if editable)
                    if (isEditable)
                      Container(
                        margin: const EdgeInsetsDirectional.fromSTEB(
                            10, 10, 10, 16),
                        child: InkWell(
                          key: _attachmentButtonKey,
                          onTap: _pickAttachment,
                          child: DottedBorder(
                            color: colors.strokeColor,
                            strokeWidth: 1,
                            dashPattern: const [8, 2],
                            borderType: BorderType.RRect,
                            radius: const Radius.circular(8),
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0, 24, 0, 24),
                              decoration: BoxDecoration(
                                color: colors.backgroundContainer,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: colors.background,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/icons/workspace_screen/upload.svg',
                                          width: 24,
                                          height: 24,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    localization.clickToUpload,
                                    style: textStyles.body2.copyWith(
                                      color: colors.primary,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    localization.maxFileSizeInMB(25),
                                    style: textStyles.body2.copyWith(
                                      color: colors.tertiaryText,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),

                    // Display selected attachments - Grid layout for all expenses
                    if (_attachments.isNotEmpty)
                      Container(
                        margin:
                            const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${localization.attachment} (${_attachments.length})',
                              style: textStyles.body2.copyWith(
                                color: colors.primaryText,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 12),

                            // Attachment grid with consistent layout
                            ...List.generate((_attachments.length / 2).ceil(),
                                (rowIndex) {
                              final startIndex = rowIndex * 2;
                              final endIndex = (startIndex + 2)
                                  .clamp(0, _attachments.length);
                              final filesInRow =
                                  _attachments.sublist(startIndex, endIndex);

                              return Container(
                                margin: EdgeInsets.only(
                                  bottom: rowIndex <
                                          (_attachments.length / 2).ceil() - 1
                                      ? 16
                                      : 0,
                                ),
                                child: Row(
                                  children: [
                                    // First file in row
                                    Expanded(
                                      child: AttachmentPlaceholderCard(
                                        filePath: filesInRow[0]
                                                .path
                                                .startsWith('http')
                                            ? null
                                            : filesInRow[0].path,
                                        url: filesInRow[0]
                                                .path
                                                .startsWith('http')
                                            ? filesInRow[0].path
                                            : null,
                                        previewWidth: 114,
                                        previewHeight: 100,
                                        onDelete: isEditable
                                            ? () => setState(() {
                                                  _attachments
                                                      .removeAt(startIndex);
                                                })
                                            : null,
                                      ),
                                    ),

                                    // Second file in row (if exists)
                                    if (filesInRow.length > 1) ...[
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: AttachmentPlaceholderCard(
                                          filePath: filesInRow[1]
                                                  .path
                                                  .startsWith('http')
                                              ? null
                                              : filesInRow[1].path,
                                          url: filesInRow[1]
                                                  .path
                                                  .startsWith('http')
                                              ? filesInRow[1].path
                                              : null,
                                          previewWidth: 114,
                                          previewHeight: 100,
                                          onDelete: isEditable
                                              ? () => setState(() {
                                                    _attachments.removeAt(
                                                        startIndex + 1);
                                                  })
                                              : null,
                                        ),
                                      ),
                                    ] else ...[
                                      const SizedBox(width: 16),
                                      const Expanded(child: SizedBox()),
                                    ],
                                  ],
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

String getStatusTranslation(String label, BuildContext context) {
  final localization = AppLocalizations.of(context);
  final today = DateTime.now();
  switch (label.toLowerCase()) {
    case 'paid':
      return localization.paymentMadeOnDate(
        formatDateDmy(today, context),
      );
    case 'unpaid':
      return localization.waitingForManagerApproval;
    case 'rejected':
      return 'Expense got rejected';
    default:
      return label; // Fallback to original label if unknown.
  }
}
