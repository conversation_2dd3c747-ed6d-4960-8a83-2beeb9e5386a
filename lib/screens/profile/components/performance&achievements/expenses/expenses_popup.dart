import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/search bar/search_bar.dart';
import 'package:ako_basma/screens/profile/components/performance&achievements/expenses/expenses_card.dart';
import 'package:ako_basma/screens/home/<USER>/components/request_expenses.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class ExpensesPopup extends StatefulWidget {
  final VoidCallback? onBack;

  const ExpensesPopup({super.key, this.onBack});

  @override
  State<ExpensesPopup> createState() => _ExpensesPopupState();
}

class _ExpensesPopupState extends State<ExpensesPopup> {
  void _showTopSnackbar(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: localization.requestSubmittedSuccessfully,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove
    Future.delayed(const Duration(seconds: 3), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  void _showRequestExpensePopup(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => RequestExpenses(
        onSuccess: () => _showTopSnackbar(context),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        // Header with back button and title
        Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  if (widget.onBack != null) {
                    widget.onBack!();
                  } else {
                    Navigator.of(context).pop();
                  }
                },
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    DirectionHelpers.getBackArrowIcon(context),
                    color: colors.primaryText,
                    size: 32,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(12, 0, 0, 0),
              ),
              Expanded(
                child: Text(
                  localization.expenses,
                  style: textStyles.headline3.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => _showRequestExpensePopup(context),
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Iconsax.add_copy,
                    color: colors.primary,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Main content - scrollable
        Expanded(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Container(
              padding: const EdgeInsetsDirectional.fromSTEB(8, 0, 8, 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  CustomSearchBar(
                    bgColor: colors.backgroundContainer,
                    hintText: localization.search,
                  ),
                  Container(height: 10),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 5,
                    separatorBuilder: (context, index) => Container(
                      margin: const EdgeInsetsDirectional.fromSTEB(0, 5, 0, 5),
                    ),
                    itemBuilder: (context, index) {
                      String status;
                      if (index == 0) {
                        status = 'unpaid';
                      } else if (index == 1) {
                        status = 'rejected';
                      } else {
                        status = 'paid';
                      }
                      return ExpensesCard(status: status);
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
