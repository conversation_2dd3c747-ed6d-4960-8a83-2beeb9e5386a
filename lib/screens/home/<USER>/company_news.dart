import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/news_detail_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_card.dart';

class CompanyNews extends StatefulWidget {
  const CompanyNews({super.key});

  @override
  State<CompanyNews> createState() => _CompanyNewsState();
}

class _CompanyNewsState extends State<CompanyNews> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // 4 company news images – second slide shows a different image
    final List<String> items = List.generate(
      4,
      (index) => index == 1
          ? 'assets/images/company_news_card2.jpg'
          : 'assets/images/company_news_card1.jpg',
    );

    return Container(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header aligned with workspace cards using an 8px horizontal margin
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.companyNews,
                  style: textStyles.headline4.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                GestureDetector(
                  onTap: () => _showAllCompanyNews(context),
                  child: Text(
                    localization.showAll,
                    style: textStyles.body3.copyWith(
                      color: colors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Company news carousel with scroll indicator
          // CarouselSlider in try-catch to handle loading errors
          Builder(
            builder: (context) {
              try {
                return Stack(
                  children: [
                    CarouselSlider(
                      items: items.asMap().entries.map((entry) {
                        final index = entry.key;
                        final path = entry.value;
                        return GestureDetector(
                          onTap: () => _showNewsDetail(context),
                          child: CompanyNewsCard(
                            imagePath: path,
                            margin: const EdgeInsetsDirectional.only(
                              start: 0,
                              end: 0,
                              top: 4.0,
                              bottom: 4.0,
                            ),
                            time: DateTime.now(),
                          ),
                        );
                      }).toList(),
                      options: CarouselOptions(
                        height: 225,
                        viewportFraction: 0.78, // Adjusted for better centering
                        enlargeCenterPage: true, // Center the current page
                        enableInfiniteScroll: false,

                        autoPlay: items.length > 1,
                        autoPlayInterval: const Duration(seconds: 5),
                        pauseAutoPlayOnManualNavigate: true,
                        pauseAutoPlayOnTouch: true,
                        padEnds: true, // Enable padding for centering
                        scrollPhysics: items.length > 1
                            ? const BouncingScrollPhysics()
                            : const NeverScrollableScrollPhysics(), // Prevent scrolling if only 1 item
                        // onPageChanged to update current index
                        onPageChanged: (index, reason) {
                          setState(() {
                            _currentIndex = index;
                          });
                        },
                      ),
                    ),
                    // Scroll indicator positioned on the carousel
                    // show indicator if there are multiple slides
                    if (items.length > 1)
                      PositionedDirectional(
                        bottom: 16,
                        start: MediaQuery.sizeOf(context).width * 0.125 + 6,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onSurface.withOpacity(
                                0.4), // ask tushar sir about the exact color
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: AnimatedSmoothIndicator(
                            activeIndex: _currentIndex,
                            count: items.length,
                            effect: SlideEffect(
                              dotHeight: 6,
                              dotWidth: 6,
                              spacing: 6,
                              activeDotColor: theme.colorScheme.onPrimary,
                              dotColor: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              } catch (e) {
                // Fallback UI in case CarouselSlider fails to load
                return Container(
                  height: 205,
                  margin: const EdgeInsetsDirectional.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: items.isNotEmpty
                        ? Image.asset(
                            items.first,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          )
                        : Center(
                            child: Icon(
                              Icons.image,
                              color: colors.tertiaryText,
                              size: 48,
                            ),
                          ),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _showAllCompanyNews(BuildContext context) {
    // Using root navigator to bypass bottom navigation bar for true full screen experience
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => AllCompanyNewsScreen(
          onBack: () {
            // Safe navigation check before popping
            if (Navigator.canPop(context)) {
              Navigator.of(context, rootNavigator: true).pop();
            }
          },
        ),
      ),
    );
  }

  void _showNewsDetail(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    showAdaptivePopup(
      context,
      (ctx, sc) => NewsDetailPopup(
        onBack: () => Navigator.pop(ctx),
        title: 'Employee of the Month',
        publishedBy: localization.publishedBy,
        time: '${localization.today} 08:00 ${localization.am}',
        imagePath: 'assets/images/employee.png',
        employeeName: 'Howard Theodore',
        description:
            "We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.\n\n"
            "As part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:\n\n"
            "• Enhanced hospital coverage, including specialist care and private rooms\n"
            "• Increased outpatient benefits and faster claim processing\n"
            "• Coverage for mental health and wellness services\n"
            "• Improved support for family members (spouse and children)\n"
            "• A simplified registration and claim procedure via the HR portal",
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }
}
