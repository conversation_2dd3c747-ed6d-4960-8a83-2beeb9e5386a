import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/clock_out/clock_out_button.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ako_basma/providers/shift_timer_provider.dart';
import 'package:solar_icons/solar_icons.dart';

class ClockOut extends ConsumerStatefulWidget {
  final VoidCallback? onClockOutPressed;

  const ClockOut({
    super.key,
    this.onClockOutPressed,
  });

  ConsumerState<ClockOut> createState() => _ClockOutState();
}

class _ClockOutState extends ConsumerState<ClockOut> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      margin:
          const EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 8),
      width: MediaQuery.of(context).size.width - 32,
      height: 240,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: const EdgeInsetsDirectional.only(
                  top: 10, end: 10, bottom: 10, start: 10),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsetsDirectional.all(4),
                    decoration: BoxDecoration(
                      color: colors.primaryVariant,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      SolarIconsOutline.mapPoint,
                      color: colors.primary,
                      size: 20,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 8),
                    child: Text(
                      '10 Blackstone Street, London, UK',
                      style: textStyles.body3.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: const EdgeInsetsDirectional.symmetric(
                horizontal: 24, vertical: 4),
            child: ClockOutButton(
              onPressed: () {
                ref.read(shiftTimerProvider.notifier).reset();
                widget.onClockOutPressed?.call();
              },
            ),
          ),
        ],
      ),
    );
  }
}
