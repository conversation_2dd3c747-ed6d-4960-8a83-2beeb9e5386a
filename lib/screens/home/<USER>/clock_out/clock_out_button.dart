import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'package:solar_icons/solar_icons.dart';

class ClockOutButton extends StatefulWidget {
  final VoidCallback? onPressed;

  const ClockOutButton({
    super.key,
    this.onPressed,
  });

  @override
  State<ClockOutButton> createState() => _ClockOutButtonState();
}

class _ClockOutButtonState extends State<ClockOutButton> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Column(
      children: [
        Transform(
          alignment: Alignment.center,
          transform: Matrix4.rotationZ(
            45 * math.pi / 180, // 45 degrees rotation
          ),
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 24, horizontal: 28),
            width: 120,
            height: 120,
            child: ElevatedButton(
              onPressed: widget.onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: colors.error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(4),
              ),
              child: Center(
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.rotationZ(
                    -45 * math.pi / 180, // Counter-rotate content
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        SolarIconsOutline.clockCircle,
                        color: theme.colorScheme.onPrimary,
                        size: 28,
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        child: Text(
                          localization.clockOut,
                          style: textStyles.button.copyWith(
                            color: theme.colorScheme.onPrimary,
                            fontSize: 18,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
