import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in_details.dart';
import 'package:ako_basma/screens/home/<USER>/clock_out/clock_out.dart';
import 'package:ako_basma/components/FAB/ai_floating_action_button.dart';
import 'package:ako_basma/screens/home/<USER>/greeting.dart';
import 'package:ako_basma/screens/home/<USER>/location.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/shift_details.dart';
import 'package:ako_basma/components/switch/switch_button_group.dart';
import 'package:ako_basma/screens/home/<USER>/workspace.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ako_basma/providers/shift_timer_provider.dart';
import 'package:ako_basma/providers/break_timer_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _showClockInDetails = false;
  bool _showClockOut = false;
  bool _showWorkspace = false;

  // Track the last clock state to maintain persistence
  String _lastClockState = 'initial'; // 'initial', 'clockedIn', 'clockOut'
  bool _shiftEnded = false; // Track if shift was ended but not clocked out yet

  @override
  void initState() {
    super.initState();
    // Initialize state based on current providers when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeClockState();
    });
  }

  void _initializeClockState() {
    if (!mounted) return;

    // Use a Consumer to get the current state
    final container = ProviderScope.containerOf(context);
    final shiftTimerState = container.read(shiftTimerProvider);
    final breakTimerState = container.read(breakTimerProvider);

    setState(() {
      if (shiftTimerState.isShiftEnded) {
        // Shift was ended, show clock out
        _showClockInDetails = false;
        _showClockOut = true;
        _lastClockState = 'clockOut';
        _shiftEnded = true;
      } else if (shiftTimerState.isRunning || breakTimerState.isBreakActive) {
        // Active shift or break, show clock in details
        _showClockInDetails = true;
        _showClockOut = false;
        _lastClockState = 'clockedIn';
      } else if (shiftTimerState.elapsed.inSeconds > 0) {
        // Has elapsed time but not running, could be paused
        _showClockInDetails = true;
        _showClockOut = false;
        _lastClockState = 'clockedIn';
      } else {
        // Initial state
        _showClockInDetails = false;
        _showClockOut = false;
        _lastClockState = 'initial';
        _shiftEnded = false;
      }
    });
  }

  void toggleClockInDetails(bool show) {
    setState(() {
      _showClockInDetails = show;
      _showClockOut = false;
      if (show) {
        _lastClockState = 'clockedIn';
      }
    });
  }

  void toggleClockOut(bool show) {
    setState(() {
      _showClockInDetails = false;
      _showClockOut = show;
      if (show) {
        _lastClockState = 'clockOut';
        _shiftEnded = true;
      }
    });
  }

  void toggleWorkspace(bool show) {
    setState(() {
      _showWorkspace = show;
      // Don't reset clock states when switching to workspace
      // This allows returning to the previous clock state
    });
  }

  void _onAIPressed() {
    context.push('/ai-chat');
  }

  void _showShiftDetailsPopup(BuildContext context, WidgetRef ref) {
    showAdaptivePopup(
      context,
      (ctx, sc) => ShiftDetails(
        onBack: () {
          Navigator.of(ctx).pop();
        },
        resetToClockIn: () {
          // Reset to clock in screen and reset all providers
          ref.read(shiftTimerProvider.notifier).reset();
          ref.read(breakTimerProvider.notifier).reset();
          setState(() {
            _showClockInDetails = false;
            _showClockOut = false;
            _showWorkspace = false;
            _lastClockState = 'initial';
            _shiftEnded = false;
          });
        },
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final localization = AppLocalizations.of(context)!;

    return Consumer(
      builder: (context, ref, child) {
        final shiftTimerState = ref.watch(shiftTimerProvider);
        final breakTimerState = ref.watch(breakTimerProvider);

        return SafeArea(
          child: Stack(
            children: [
              // Main content
              SingleChildScrollView(
                child: Column(
                  children: [
                    const Greeting(),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 10),
                      child: SwitchButtonGroup(
                        labels: [
                          localization.timeClock,
                          localization.workspace
                        ],
                        onTap: (index) {
                          setState(() {
                            final isTimeClockSelected = index == 0;
                            _showWorkspace = !isTimeClockSelected;
                            if (isTimeClockSelected) {
                              // Determine the correct state to show based on current conditions
                              if (shiftTimerState.isShiftEnded || _shiftEnded) {
                                // User ended shift but hasn't clocked out yet
                                _showClockInDetails = false;
                                _showClockOut = true;
                                _lastClockState = 'clockOut';
                                _shiftEnded = true;
                              } else if (shiftTimerState.isRunning ||
                                  breakTimerState.isBreakActive) {
                                // User is in an active shift or on break
                                _showClockInDetails = true;
                                _showClockOut = false;
                                _lastClockState = 'clockedIn';
                              } else if (shiftTimerState.elapsed.inSeconds >
                                      0 &&
                                  !shiftTimerState.isRunning &&
                                  !breakTimerState.isBreakActive) {
                                // User has elapsed time but shift is not running and not on break
                                // This could mean they're in a paused state, show clock in details
                                _showClockInDetails = true;
                                _showClockOut = false;
                                _lastClockState = 'clockedIn';
                              } else {
                                // No active shift, show initial clock in state
                                _showClockInDetails = false;
                                _showClockOut = false;
                                _lastClockState = 'initial';
                                _shiftEnded = false;
                              }
                            }
                          });
                        },
                      ),
                    ),
                    if (_showWorkspace)
                      const Workspace()
                    else if (_showClockInDetails)
                      ClockInDetailsScreen(
                        onBack: () => toggleClockInDetails(false),
                        onEndShift: () => toggleClockOut(true),
                      )
                    else if (_showClockOut)
                      Column(
                        children: [
                          const Location(),
                          ClockOut(
                            onClockOutPressed: () {
                              _showShiftDetailsPopup(context, ref);
                            },
                          ),
                        ],
                      )
                    else
                      Column(
                        children: [
                          const Location(),
                          ClockIn(
                            onClockInPressed: () => toggleClockInDetails(true),
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              const PositionedDirectional(
                bottom: 0,
                end: 0,
                child: AIFloatingActionButton(),
              ),
            ],
          ),
        );
      },
    );
  }
}
