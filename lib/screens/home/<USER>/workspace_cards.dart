import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';

Widget buildWorkspaceCard(
  BuildContext context, {
  required dynamic icon,
  required String label,
  required VoidCallback onTap,
  EdgeInsetsGeometry? margin,
}) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        width: 160,
        height: 75,
        // Use provided margin or default to symmetric 8 on horizontal edges
        margin: margin ?? const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        padding: const EdgeInsetsDirectional.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon is IconData
                ? Icon(
                    icon,
                    size: 24,
                    color: Theme.of(context).brightness == Brightness.light
                        ? const Color(0xFF006685)
                        : colors.primary,
                  )
                : icon is Widget
                    ? icon
                    : Icon(
                        Icons.error,
                        size: 24,
                        color: Theme.of(context).brightness == Brightness.light
                            ? const Color(0xFF006685)
                            : colors.primary,
                      ),
            Container(
              margin: const EdgeInsets.only(top: 12),
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: textStyles.body3.copyWith(
                  // Use a specific color for light theme, otherwise fallback to theme color
                  color: Theme.of(context).brightness == Brightness.light
                      ? const Color(0xFF006685)
                      : colors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
