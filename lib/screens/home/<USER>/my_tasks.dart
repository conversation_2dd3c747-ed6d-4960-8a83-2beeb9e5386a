import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/components/all_tasks_popup.dart';
import 'package:ako_basma/screens/tasks/components/task_details/task_details_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/task_card.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';

class MyTasks extends StatefulWidget {
  const MyTasks({super.key});

  @override
  State<MyTasks> createState() => _MyTasksState();
}

class _MyTasksState extends State<MyTasks> {
  int _activeIndex = 0;

  void _showAllTasks(BuildContext context) {
    // Using root navigator to bypass bottom navigation bar for true full screen experience
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => AllTasksPopup(
          onBack: () => Navigator.of(context, rootNavigator: true).pop(),
        ),
      ),
    );
  }

  void _showTaskDetails(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) => TaskDetailsPopup(
        onBack: () => Navigator.pop(ctx),
      ),
      isDismissible: true,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Get responsive height for consistent sizing across devices
    final carouselHeight = AppDimensions.getTaskCardHeight(context);

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with horizontal padding only for text content
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.myTasks,
                  style: textStyles.headline4.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                GestureDetector(
                  onTap: () => _showAllTasks(context),
                  child: Text(
                    localization.showAll,
                    style: textStyles.body3.copyWith(
                      color: colors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 2),

          // Task card carousel
          CarouselSlider(
            options: CarouselOptions(
              height: carouselHeight,
              enlargeCenterPage: false,
              viewportFraction: 1 - (24 / MediaQuery.sizeOf(context).width),
              enableInfiniteScroll: false,
              // padEnds: true,
              disableCenter: true,
              onPageChanged: (index, reason) {
                setState(() {
                  _activeIndex = index;
                });
              },
            ),
            items: List.generate(
              3,
              (index) => Padding(
                // padding: EdgeInsetsGeometry.all(0),
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: GestureDetector(
                  onTap: () => _showTaskDetails(context),
                  child: TaskCard(
                    height: carouselHeight,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
