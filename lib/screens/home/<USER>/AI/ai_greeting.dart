import 'package:ako_basma/components/gradient_mask/gradient_mask.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';

/// AI Greeting component with logo and gradient text

class AIGreeting extends StatelessWidget {
  const AIGreeting({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final localization = AppLocalizations.of(context)!;
    return Container(
      width: screenWidth,
      padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Ako Basma Logo
          Container(
            margin: const EdgeInsetsDirectional.only(bottom: 18),
            child: Image.asset(
              'assets/images/ako_basma_logo.png',
              width: 45,
              height: 45,
              fit: BoxFit.contain,
              // errorBuilder: (context, error, stackTrace) {
              //   // Fallback if logo fails to load
              //   return Container(
              //     width: 80,
              //     height: 80,
              //     decoration: BoxDecoration(
              //       shape: BoxShape.circle,
              //       gradient: colors.primaryGradient,
              //     ),
              //     child: Center(
              //       child: Text(
              //         'AI',
              //         style: textStyles.headline.copyWith(
              //           color: Colors.white,
              //           fontWeight: FontWeight.bold,
              //         ),
              //       ),
              //     ),
              //   );
              // },
            ),
          ),

          // Gradient greeting text
          // Gradient text using Text widget with hardcoded blue to red gradient
          GradientMask(
            gradient: const LinearGradient(
              colors: [
                Color(0xFF0090BC), // Blue
                Color(0xFFD45969),
                Color(0xFF65C9E8), // Red
              ],
            ),
            child: Text(
              localization.aiChatGreeting,
              style: textStyles.body2.copyWith(),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
