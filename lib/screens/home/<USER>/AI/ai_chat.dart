import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/image/image_service.dart';
import 'package:ako_basma/util/document/document_service.dart';
import 'package:ako_basma/screens/home/<USER>/AI/ai_chat_header.dart';
import 'package:ako_basma/screens/home/<USER>/AI/ai_greeting.dart';
import 'package:ako_basma/screens/home/<USER>/AI/ai_welcome_container.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/attachment_menu.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/screen/components/attachment%20menu/location_sharing_modal.dart';

class AIChat extends StatefulWidget {
  const AIChat({
    super.key,
  });

  @override
  State<AIChat> createState() => _AIChatState();
}

class _AIChatState extends State<AIChat> {
  bool _showAttachmentMenu = false;

  void _toggleAttachmentMenu() {
    setState(() {
      _showAttachmentMenu = !_showAttachmentMenu;
    });
  }

  Future<void> _handleCameraPress() async {
    try {
      _toggleAttachmentMenu();

      // Use the reusable image service to capture photo
      final File? photo = await ImageService.captureFromCamera();

      if (photo != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Photo captured successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  Future<void> _handleGalleryPress() async {
    try {
      _toggleAttachmentMenu();

      final File? image = await ImageService.selectFromGallery();

      if (image != null) {
        if (mounted) {
          ImageService.showSuccessMessage(
              context, 'Image selected successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        ImageService.showErrorMessage(
            context, 'Failed to select image: ${e.toString()}');
      }
    }
  }

  Future<void> _handleDocumentPress() async {
    try {
      _toggleAttachmentMenu();

      final File? document = await DocumentService.selectDocument();

      if (document != null) {
        final fileName = DocumentService.getFileName(document.path);
        final fileSize = DocumentService.getFileSize(document);
        print('Document selected: ${document.path}');
        print('File name: $fileName, Size: $fileSize');
        // TODO: Process and send the document in chat

        if (mounted) {
          DocumentService.showSuccessMessage(
              context, 'Document "$fileName" selected successfully!');
        }
      }
    } catch (e) {
      if (mounted) {
        DocumentService.showErrorMessage(
            context, 'Failed to select document: ${e.toString()}');
      }
    }
  }

  void _handleContactPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactSelectionModal(
        onContactsSelected: (selectedContacts) {
          if (selectedContacts.isNotEmpty) {
            print(
                'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
            // TODO: Implement contact sharing logic
          }
        },
      ),
    );
  }

  void _handleLocationPress() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => LocationSharingModal(
        onLocationSelected: (locationType, locationData) {
          print('Location type: $locationType');
          print('Location data: $locationData');
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return GestureDetector(
      onTap: () {
        if (_showAttachmentMenu) {
          _toggleAttachmentMenu();
        }
      },
      child: Scaffold(
        backgroundColor: colors.background,
        body: Stack(
          children: [
            Column(
              children: [
                AIChatHeader(
                  onBackPressed: () => context.pop(),
                ),
                const Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        AIGreeting(),
                        AIWelcomeContainer(),
                        ChatMessagesList(),
                      ],
                    ),
                  ),
                ),
                ChatInputField(
                  onSendMessage: (message) {
                    print('AI Message sent: $message');
                  },
                  onAttachmentPressed: _toggleAttachmentMenu,
                  onVoicePressed: () {
                    print('AI Voice message pressed');
                  },
                ),
              ],
            ),
            if (_showAttachmentMenu)
              PositionedDirectional(
                bottom: 100,
                start: MediaQuery.of(context).size.width * 0.02,
                end: MediaQuery.of(context).size.width * 0.02,
                child: AttachmentMenu(
                  onCameraPressed: _handleCameraPress,
                  onRecordPressed: () {
                    _toggleAttachmentMenu();
                    print('Record pressed');
                  },
                  onContactPressed: () {
                    _toggleAttachmentMenu();
                    _handleContactPress();
                  },
                  onGalleryPressed: _handleGalleryPress,
                  onLocationPressed: () {
                    _toggleAttachmentMenu();
                    _handleLocationPress();
                  },
                  onDocumentPressed: _handleDocumentPress,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
