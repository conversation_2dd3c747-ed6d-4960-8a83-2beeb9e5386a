import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

/// AI Chat header component
class AI<PERSON>hatHeader extends StatelessWidget {
  final VoidCallback onBackPressed;

  const AIChatHeader({
    super.key,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final localization = AppLocalizations.of(context)!;
    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsetsDirectional.fromSTEB(
        20, // start
        MediaQuery.of(context).padding.top + 8, // top - blend with status bar
        16, // end
        6, // bottom
      ),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onBackPressed,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
                child: Icon(
                  DirectionHelpers.getBackArrowIcon(context),
                  size: 32,
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // Chat with AI title
          Expanded(
            child: Container(
              margin: const EdgeInsetsDirectional.only(start: 10),
              child: Text(
                localization.chatWithAi,
                style: textStyles.headline3.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
