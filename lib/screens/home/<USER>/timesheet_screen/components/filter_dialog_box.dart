import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';

class FilterDialogBox extends StatefulWidget {
  final Function(String filter, DateTime? fromDate, DateTime? toDate) onSave;
  final Function() onCancel;
  final String initialFilter;
  final DateTime? initialFromDate;
  final DateTime? initialToDate;

  const FilterDialogBox({
    super.key,
    required this.onSave,
    required this.onCancel,
    this.initialFilter = 'This Month',
    this.initialFromDate,
    this.initialToDate,
  });

  @override
  State<FilterDialogBox> createState() => _FilterDialogBoxState();
}

class _FilterDialogBoxState extends State<FilterDialogBox> {
  late String selectedFilter;
  DateTime? fromDate;
  DateTime? toDate;
  final dateFormat = DateFormat('dd/MM/yyyy');

  @override
  void initState() {
    super.initState();
    selectedFilter = widget.initialFilter;
    fromDate = widget.initialFromDate;
    toDate = widget.initialToDate;
  }

  // Using date_picker_plus package for enhanced date picker functionality
  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    final DateTime? picked = await showDatePickerDialog(
      context: context,
      initialDate: isFromDate
          ? (fromDate ?? DateTime.now())
          : (toDate ?? DateTime.now()),
      minDate: DateTime(2000),
      maxDate: DateTime(2101),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          fromDate = picked;
          // If toDate is before fromDate, update toDate
          if (toDate != null && toDate!.isBefore(fromDate!)) {
            toDate = fromDate;
          }
        } else {
          toDate = picked;
          // If fromDate is after toDate, update fromDate
          if (fromDate != null && fromDate!.isAfter(toDate!)) {
            fromDate = toDate;
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localization.filter,
              style: textStyles.body.copyWith(
                color: colors.secondaryText,
              ),
            ),
            Container(margin: const EdgeInsetsDirectional.only(top: 12)),

            // Filter options
            ...[
              localization.thisMonth,
              localization.thisWeek,
              localization.sinceStartOfYear
            ].map(
              (filter) => GestureDetector(
                onTap: () {
                  setState(() {
                    selectedFilter = filter;
                  });
                },
                child: Container(
                  margin: const EdgeInsetsDirectional.only(bottom: 0),
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    dense: true, // reduces ListTile height
                    visualDensity: VisualDensity.compact,
                    contentPadding: EdgeInsets.zero,
                    leading: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: selectedFilter == filter
                              ? colors.primary
                              : colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: selectedFilter == filter
                          ? Center(
                              child: Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: colors.primary,
                                ),
                              ),
                            )
                          : null,
                    ),
                    title: Text(
                      filter,
                      style: textStyles.body2.copyWith(
                        color: selectedFilter == filter
                            ? colors.primary
                            : colors.secondaryText,
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        selectedFilter = filter;
                      });
                    },
                  ),
                ),
              ),
            ),

            Container(
              margin: const EdgeInsetsDirectional.only(top: 16),
            ),

            // Date pickers
            Text(
              localization.customDateRange,
              style: textStyles.body2.copyWith(
                color: colors.primaryText,
              ),
            ),

            Container(margin: const EdgeInsetsDirectional.only(top: 12)),

            // From date picker
            GestureDetector(
              onTap: () => _selectDate(context, true),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            fromDate != null
                                ? dateFormat.format(fromDate!)
                                : localization.from,
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (fromDate != null)
                    Positioned(
                      left: 12,
                      top: -8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        color: colors.backgroundContainer,
                        child: Text(
                          localization.from,
                          style: textStyles.body3.copyWith(
                            color: colors.tertiaryText,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            Container(
              margin: const EdgeInsetsDirectional.only(top: 10),
            ),

            // To date picker
            GestureDetector(
              onTap: () => _selectDate(context, false),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    width: double.infinity,
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            toDate != null
                                ? dateFormat.format(toDate!)
                                : localization.to,
                            style: textStyles.body2.copyWith(
                              color: colors.primaryText,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (toDate != null)
                    Positioned(
                      left: 12,
                      top: -8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        color: colors.backgroundContainer,
                        child: Text(
                          localization.to,
                          style: textStyles.body3.copyWith(
                            color: colors.tertiaryText,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            Container(
              margin: const EdgeInsetsDirectional.only(top: 14),
            ),

            // Buttons
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: widget.onCancel,
                    child: Container(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0, 14, 0, 14),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        localization.cancel,
                        style: textStyles.button.copyWith(
                          color: colors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
                Container(margin: const EdgeInsetsDirectional.only(start: 12)),
                Expanded(
                  flex: 3,
                  child: GestureDetector(
                    onTap: () =>
                        widget.onSave(selectedFilter, fromDate, toDate),
                    child: Container(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0, 14, 0, 14),
                      decoration: BoxDecoration(
                        color: colors.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        localization.save,
                        style: textStyles.buttonMedium.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Helper function
void showFilterDialog({
  required BuildContext context,
  required Function(String filter, DateTime? fromDate, DateTime? toDate) onSave,
  String initialFilter = 'This Month',
  DateTime? initialFromDate,
  DateTime? initialToDate,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return FilterDialogBox(
        onSave: (filter, fromDate, toDate) {
          onSave(filter, fromDate, toDate);
          Navigator.of(context).pop();
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
        initialFilter: initialFilter,
        initialFromDate: initialFromDate,
        initialToDate: initialToDate,
      );
    },
  );
}
