import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_shift_details.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/no_shift_recorded/no_shift_recorded.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';

class TimesheetCard extends StatelessWidget {
  final String location;
  final String title;
  final TextStyle titleTextStyle;
  final String date;
  final String month;
  final String year;
  final String status;
  final Color statusColor;
  final Color statusBgColor;
  final bool showStatus;

  const TimesheetCard({
    super.key,
    required this.location,
    required this.title,
    required this.titleTextStyle,
    required this.date,
    required this.month,
    required this.year,
    required this.status,
    required this.statusColor,
    required this.statusBgColor,
    this.showStatus = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () {
        if (title == "Leave" || title == "00:00:00") {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NoShiftRecorded(
                selectedDate: DateTime(
                  int.parse(year),
                  const [
                        'Jan',
                        'Feb',
                        'Mar',
                        'Apr',
                        'May',
                        'Jun',
                        'Jul',
                        'Aug',
                        'Sep',
                        'Oct',
                        'Nov',
                        'Dec'
                      ].indexOf(month) +
                      1,
                  int.parse(date),
                ),
              ),
            ),
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const TimesheetShiftDetails(),
            ),
          );
        }
      },
      child: Container(
        margin: const EdgeInsetsDirectional.only(top: 12),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.strokeColor,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 10),
              decoration: BoxDecoration(
                color: colors.background,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: titleTextStyle,
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 2, top: 12),
                    child: Text(
                      title == "00:00:00"
                          ? localization.didNotWorkOnThisDay
                          : localization.totalShiftTime,
                      style: textStyles.body3.copyWith(
                        color: colors.secondaryText,
                        fontSize: 10,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Divider
            Container(
              color: colors.background,
            ),

            // Content area
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Location section with padding
                Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(12, 12, 12, 0),
                  child: Row(
                    children: [
                      Container(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                        decoration: BoxDecoration(
                          color: colors.primaryVariant,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          SolarIconsOutline.mapPoint,
                          color: colors.primary,
                          size: 16,
                        ),
                      ),
                      Container(
                        margin: const EdgeInsetsDirectional.only(start: 8),
                      ),
                      Expanded(
                        child: Text(
                          '10 Blackstone Street, London, UK',
                          style: textStyles.body3.copyWith(
                            color: colors.secondaryText,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                Container(
                  margin: const EdgeInsetsDirectional.only(top: 16),
                ),

                // Date and status section - no start padding to allow date container to touch border
                Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 12, 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Date container that touches the start border
                      Container(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            20, 12, 28, 12),
                        decoration: BoxDecoration(
                          color: colors.primaryVariant,
                          borderRadius: const BorderRadiusDirectional.only(
                            topEnd: Radius.circular(8),
                            bottomEnd: Radius.circular(8),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              date,
                              style: textStyles.headline2.copyWith(
                                color: colors.primary,
                                fontSize: 32,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            Container(
                              margin: const EdgeInsetsDirectional.fromSTEB(
                                  2, 8, 0, 8),
                            ),
                            Container(
                              margin: const EdgeInsetsDirectional.only(top: 10),
                              child: Text(
                                "${localization.month} $year",
                                style: textStyles.body3.copyWith(
                                  color: colors.secondaryText,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Status container (only if showStatus is true)
                      if (showStatus)
                        Container(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              10, 8, 10, 8),
                          decoration: BoxDecoration(
                            color: statusBgColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            status,
                            style: textStyles.body2.copyWith(
                              color: statusColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

String getStatusTranslation(String label, BuildContext context) {
  final localization = AppLocalizations.of(context);
  //lowercase to avoid case-sensitive mismatches.
  switch (label.toLowerCase()) {
    case 'approved':
      return localization.approve;
    case 'pending':
      return localization.pending;
    case 'rejected':
      return localization.rejected;
    case 'holiday':
      return localization.holidays;
    default:
      return label; // Fallback to original label if unknown.
  }
}
