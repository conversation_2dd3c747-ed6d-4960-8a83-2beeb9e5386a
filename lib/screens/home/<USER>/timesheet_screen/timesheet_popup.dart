import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/custom_date_pay_card.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/filter_dialog_box.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_card.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class TimesheetPopup extends StatefulWidget {
  const TimesheetPopup({super.key});

  @override
  State<TimesheetPopup> createState() => _TimesheetPopupState();
}

class _TimesheetPopupState extends State<TimesheetPopup> {
  String filter = 'Filter'; // Will be replaced with localized text in initState
  String filterLabel =
      'Filter'; // Will be replaced with localized text in initState
  DateTime? fromDate;
  DateTime? toDate;

  // Helper method to get localized filter text
  String _getLocalizedFilterText(String filterKey, BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    switch (filterKey) {
      case 'Today':
        return localization.today;
      case 'This Week':
        return localization.thisWeek;
      case 'This Month':
        return localization.thisMonthFilter;
      case 'Since Start of Year':
        return localization.sinceStartOfYear;
      case 'Custom Date Range':
        return localization.customDateRange;
      case 'Filter':
        return localization.filter;
      default:
        return filterKey;
    }
  }

  String _monthName(DateTime date) {
    // Use DateFormat if intl is imported, otherwise use a static list
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[date.month - 1];
  }

  // date format helper
  String _formatDateDmy(DateTime start, DateTime end) {
    if (start.year == end.year &&
        start.month == end.month &&
        start.day == end.day) {
      return "${start.day.toString().padLeft(2, '0')} ${_monthName(start)} ${start.year}";
    } else {
      return "${start.day.toString().padLeft(2, '0')} ${_monthName(start)} - ${end.day.toString().padLeft(2, '0')} ${_monthName(end)} ${end.year}";
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize with "This Month" as default filter and set dates to show the custom date pay card
    filter = 'This Month';

    // Set current month's start and end dates to trigger custom date pay card display
    final now = DateTime.now();
    fromDate = DateTime(now.year, now.month, 1); // First day of current month
    toDate = DateTime(now.year, now.month + 1, 0); // Last day of current month

    // Set filter label to show the date range instead of just "This Month"
    filterLabel = _formatDateDmy(fromDate!, toDate!);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      // Safe navigation check before popping
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 16)),
                  Text(
                    localization.timesheet,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
              child: Row(
                children: [
                  // Filter dropdown
                  Expanded(
                    flex: 4,
                    child: GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return FilterDialogBox(
                              initialFilter: filter,
                              initialFromDate: fromDate,
                              initialToDate: toDate,
                              onSave: (selectedFilter, selectedFromDate,
                                  selectedToDate) {
                                setState(() {
                                  // when custom date is selected then filter text will be customDateRange as selectedFromDate and selectedToDate will not be null
                                  if (selectedFromDate != null &&
                                      selectedToDate != null) {
                                    filter = 'Custom Date Range';
                                  } else {
                                    filter = selectedFilter;
                                  }
                                  fromDate = selectedFromDate;
                                  toDate = selectedToDate;
                                  // Update label as the filter text will be customDateRange as selectedFromDate and selectedToDate will not be null
                                  if (fromDate != null && toDate != null) {
                                    filterLabel =
                                        '${formatDateDmyDisplay(fromDate, context)} - ${formatDateDmyDisplay(toDate, context)}';
                                  } else {
                                    // Use localized filter text
                                    filterLabel = _getLocalizedFilterText(
                                        filter, context);
                                  }
                                });
                                // Safe navigation check before popping
                                if (Navigator.canPop(context)) {
                                  Navigator.pop(context);
                                }
                              },
                              onCancel: () {
                                // Safe navigation check before popping
                                if (Navigator.canPop(context)) {
                                  Navigator.pop(context);
                                }
                              },
                            );
                          },
                        );
                      },
                      child: Container(
                        margin: const EdgeInsetsDirectional.only(end: 8),
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                        height: 48,
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              filterLabel, // Display the current filter selection or date range
                              style: textStyles.body2.copyWith(
                                color: colors.secondaryText,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Icon(
                              HugeIcons.strokeRoundedArrowDown01,
                              color: colors.primary,
                              size: 24,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Today button
                  Expanded(
                    flex: 1,
                    child: SizedBox(
                      height: 48,
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          padding:
                              const EdgeInsetsDirectional.fromSTEB(0, 8, 0, 8),
                          side: BorderSide(
                            color: colors.primary,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () {
                          final now = DateTime.now();
                          setState(() {
                            filter = 'Today';
                            fromDate = now;
                            toDate = now;
                            // Use localized date format for today
                            filterLabel = formatDateDmyDisplay(now, context);
                          });
                        },
                        child: Text(
                          localization.today, // Use localized "Today" text
                          style: textStyles.body2.copyWith(
                            color: colors.primary,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Show custom date pay card by default when dates are available
            if (fromDate != null && toDate != null)
              Container(
                margin: const EdgeInsetsDirectional.only(top: 8),
                child: const CustomDatePayCard(),
              ),
            Expanded(
              child: ListView(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                children: [
                  // Approved timesheet card
                  TimesheetCard(
                    location:
                        '10 Blackstone Street, London, UK', // Use localized location
                    title: '10:30:00',
                    titleTextStyle: textStyles.body.copyWith(
                      color: colors.primary,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '07',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: getStatusTranslation('Approved', context),
                    statusColor: colors.success,
                    statusBgColor: colors.successContainer,
                    showStatus: true,
                  ),

                  // Pending timesheet
                  TimesheetCard(
                    location:
                        '10 Blackstone Street, London, UK', // Use different localized location
                    title: (filter == 'Today') ? "Leave" : "10:30:00",
                    titleTextStyle: textStyles.body.copyWith(
                      color: colors.warning,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '12',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: getStatusTranslation('Pending', context),
                    statusColor: colors.warning,
                    statusBgColor: colors.warningContainer,
                    showStatus: true,
                  ),

                  // Rejected timesheet
                  TimesheetCard(
                    location:
                        '10 Blackstone Street, London, UK', // Use third localized location
                    title: "00:00:00",
                    titleTextStyle: textStyles.body.copyWith(
                      color: colors.error,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '12',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: getStatusTranslation('Rejected', context),
                    statusColor: colors.error,
                    statusBgColor: colors.errorContainer,
                    showStatus: true,
                  ),

                  // Holiday timesheet
                  TimesheetCard(
                    location:
                        '10 Blackstone Street, London, UK', // Use first localized location again
                    title: 'Holiday',
                    titleTextStyle: textStyles.body.copyWith(
                      color: colors.info,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '12',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: getStatusTranslation('Holiday', context),
                    statusColor: colors.info,
                    statusBgColor: colors.infoContainer,
                    showStatus: true,
                  ),

                  // Approved timesheet
                  TimesheetCard(
                    location:
                        '10 Blackstone Street, London, UK', // Use second localized location again
                    title: '10:30:00',
                    titleTextStyle: textStyles.body.copyWith(
                      color: colors.primary,
                      fontSize: 32,
                    ),
                    date: fromDate != null
                        ? fromDate!.day.toString().padLeft(2, '0')
                        : '07',
                    month: fromDate != null ? _monthName(fromDate!) : 'Mar',
                    year: fromDate != null ? fromDate!.year.toString() : '2025',
                    status: getStatusTranslation('Approved', context),
                    statusColor: colors.success,
                    statusBgColor: colors.successContainer,
                    showStatus: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
