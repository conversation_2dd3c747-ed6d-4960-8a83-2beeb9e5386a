import 'dart:io';

import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/attachment/attachment_placeholder_card.dart';

class RequestExpenses extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final VoidCallback? onSuccess; // Callback for showing success snackbar

  const RequestExpenses({
    super.key,
    this.onCancel,
    this.onSave,
    this.onSuccess,
  });

  @override
  State<RequestExpenses> createState() => _RequestLeavePopupState();
}

class _RequestLeavePopupState extends State<RequestExpenses> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final List<File> _attachments = [];
  final _attachmentButtonKey = GlobalKey(debugLabel: 'attachmentButtonKey');

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    amountController.dispose();
    super.dispose();
  }

  Future<void> _pickAttachment() async {
    final res = await showLocalPickerMenu(
      buttonKey: _attachmentButtonKey,
      context: context,
      allowedTypes: ['image', 'pdf'],
      allowMultiple: true,
      maxSizeInMB: 10,
    );

    if (res != null) {
      setState(() {
        if (res is File) {
          _attachments.add(res);
        }
        if (res is List<File>) {
          _attachments.addAll(res);
        }
      });
      // Files successfully selected and added to attachments list
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context);
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsetsDirectional.only(bottom: bottomInset),
        child: Container(
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsetsDirectional.fromSTEB(12, 16, 12, 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header title with proper directional alignment
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 10),
                child: Align(
                  alignment: AlignmentDirectional
                      .centerStart, // Proper RTL/LTR alignment
                  child: Text(
                    localization.requestForExpenses,
                    style: textStyles.body.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ),
              ),

              // Text input fields
              Container(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 12, 10, 12),
                child: Column(
                  children: [
                    // Title field
                    TextField(
                      controller: titleController,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsetsDirectional.fromSTEB(
                            16, 12, 16, 12),
                        labelText: localization.title,
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        labelStyle: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                          ),
                        ),
                      ),
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                    ),

                    Container(
                      height: 8,
                    ),

                    // Description field
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsetsDirectional.fromSTEB(
                            16, 12, 16, 12),
                        labelText: localization.description,
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        labelStyle: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                          ),
                        ),
                      ),
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                    ),

                    Container(
                      height: 8,
                    ),

                    // Amount field
                    TextField(
                      controller: amountController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsetsDirectional.fromSTEB(
                            16, 12, 16, 12),
                        labelText: localization.amount,
                        floatingLabelBehavior: FloatingLabelBehavior.auto,
                        labelStyle: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.strokeColor,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: colors.primary,
                          ),
                        ),
                      ),
                      style: textStyles.body2.copyWith(
                        color: colors.primaryText,
                      ),
                    ),
                  ],
                ),
              ),

              // file picker
              Container(
                margin: const EdgeInsetsDirectional.fromSTEB(10, 16, 10, 18),
                child: InkWell(
                  key: _attachmentButtonKey,
                  onTap: _pickAttachment,
                  child: DottedBorder(
                    color: colors.strokeColor,
                    strokeWidth: 1,
                    dashPattern: const [8, 4],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(8),
                    child: Container(
                      width: double.infinity,
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0, 24, 0, 24),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: colors.background,
                              shape: BoxShape.circle,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: Center(
                                child: SvgPicture.asset(
                                  'assets/icons/workspace_screen/upload.svg',
                                  width: 24,
                                  height: 24,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Text(
                            localization.clickToUpload,
                            style: textStyles.body2.copyWith(
                              color: colors.primary,
                            ),
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          Text(
                            localization.maxFileSizeInMB(10),
                            style: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Display selected attachments - Grid layout with previews
              if (_attachments.isNotEmpty)
                Container(
                  margin: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${localization.attachment} (${_attachments.length})',
                        style: textStyles.body2.copyWith(
                          color: colors.primaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Attachment grid with proper previews
                      ...List.generate((_attachments.length / 2).ceil(),
                          (rowIndex) {
                        final startIndex = rowIndex * 2;
                        final endIndex =
                            (startIndex + 2).clamp(0, _attachments.length);
                        final filesInRow =
                            _attachments.sublist(startIndex, endIndex);

                        return Container(
                          margin: EdgeInsets.only(
                            bottom:
                                rowIndex < (_attachments.length / 2).ceil() - 1
                                    ? 16
                                    : 0,
                          ),
                          child: Row(
                            children: [
                              // First file in row
                              Expanded(
                                child: AttachmentPlaceholderCard(
                                  filePath: filesInRow[0].path,
                                  previewWidth: 114,
                                  previewHeight: 100,
                                  onDelete: () => setState(() {
                                    _attachments.removeAt(startIndex);
                                  }),
                                ),
                              ),

                              // Second file in row (if exists)
                              if (filesInRow.length > 1) ...[
                                const SizedBox(width: 16),
                                Expanded(
                                  child: AttachmentPlaceholderCard(
                                    filePath: filesInRow[1].path,
                                    previewWidth: 114,
                                    previewHeight: 100,
                                    onDelete: () => setState(() {
                                      _attachments.removeAt(startIndex + 1);
                                    }),
                                  ),
                                ),
                              ] else ...[
                                const SizedBox(width: 16),
                                const Expanded(child: SizedBox()),
                              ],
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),

              // Buttons
              Container(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 12, 10, 24),
                child: SizedBox(
                  height: 44,
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // Dismiss keyboard first to prevent it from opening after popup closes
                      FocusScope.of(context).unfocus();

                      // Call success callback first to show snackbar
                      if (widget.onSuccess != null) {
                        widget.onSuccess!();
                      }

                      // Close the popup
                      Navigator.pop(context);

                      // Call onSave if provided
                      if (widget.onSave != null) {
                        widget.onSave!();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colors.primary,
                      foregroundColor: colors.primaryText,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      localization.submit,
                      style: textStyles.buttonMedium.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
