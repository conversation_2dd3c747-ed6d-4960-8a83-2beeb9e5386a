import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:solar_icons/solar_icons.dart';

class Schedule extends StatefulWidget {
  final VoidCallback? onBack;

  const Schedule({
    super.key,
    this.onBack,
  });

  @override
  State<Schedule> createState() => _SchedulePopupState();
}

class _SchedulePopupState extends State<Schedule> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 12)),
                  Text(
                    localization.schedule,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: ListView(
                padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                children: [
                  _buildScheduleCard1(context),
                  _buildScheduleCard2(context),
                  _buildScheduleCard1(context),
                  _buildScheduleCard1(context),
                  _buildScheduleCard2(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Schedule Card 1
Widget _buildScheduleCard1(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  final localization = AppLocalizations.of(context)!;

  return Container(
    width: double.infinity,
    height: 138,
    margin: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 4),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: Align(
        alignment: AlignmentDirectional.topStart,
        child: Container(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Innovation In Technology Conference',
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 12),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: Icon(
                        SolarIconsOutline.calendarMinimalistic,
                        size: 16,
                        color: colors.secondaryText,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      child: Text(
                        formatDateDmyDisplay(DateTime(2025, 3, 15), context),
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 12),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: Icon(
                        SolarIconsOutline.clockCircle,
                        size: 16,
                        color: colors.secondaryText,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      child: Text(
                        '10:00 ${localization.am} - 2:00 ${localization.pm}',
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 8),
                width: double.infinity,
                height: 24,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: Icon(
                        SolarIconsOutline.mapPoint,
                        size: 16,
                        color: colors.secondaryText,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      child: Text(
                        'Conference Hall - London',
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                    const Spacer(),
                    OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colors.success,
                        backgroundColor: colors.successContainer,
                        minimumSize: const Size(63, 23),
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
                        side: BorderSide(
                            width: 1, color: colors.successContainer),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {},
                      child: Text(
                        localization
                            .confirmed, // this will get changed as it is dynamic
                        style: textStyles.body3.copyWith(
                          color: colors.success,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

// Schedule Card 2
Widget _buildScheduleCard2(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  final localization = AppLocalizations.of(context)!;
  return Container(
    width: double.infinity,
    height: 188,
    margin: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 4),
    decoration: BoxDecoration(
      color: colors.backgroundContainer,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: colors.strokeColor,
        width: 1,
      ),
    ),
    child: ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Align(
        alignment: AlignmentDirectional.topStart,
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(14, 14, 14, 14),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Innovation In Technology Conference',
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 12),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: Icon(
                        SolarIconsOutline.calendarMinimalistic,
                        size: 16,
                        color: colors.secondaryText,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      child: Text(
                        formatDateDmyDisplay(DateTime(2025, 3, 15), context),
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 12),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: Icon(
                        SolarIconsOutline.clockCircle,
                        size: 16,
                        color: colors.secondaryText,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      child: Text(
                        '10:00 ${localization.am} - 2:00 ${localization.pm}',
                        style: textStyles.body2.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(top: 8),
                width: double.infinity, // Fill (304px)
                height: 24, // Hug (24px)
                child: Row(
                  mainAxisAlignment:
                      MainAxisAlignment.spaceBetween, // space-between
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      child: Icon(
                        SolarIconsOutline.mapPoint,
                        size: 16,
                        color: colors.secondaryText,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      child: Text(
                        'Conference Hall - London',
                        style: textStyles.body2.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Status OutlinedButton
                    OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colors.warning,
                        backgroundColor: colors.warningContainer,
                        minimumSize: const Size(63, 23),
                        padding: const EdgeInsets.fromLTRB(8, 4, 8, 4),
                        side: BorderSide(
                            width: 1, color: colors.warningContainer),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {},
                      child: Text(
                        localization
                            .pending, //  this will get changed as it is dynamic
                        style: textStyles.body3.copyWith(
                          color: colors.warning,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Action buttons - Reject and Approve
              Container(
                margin: const EdgeInsetsDirectional.only(top: 6),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: colors.error,
                          side: BorderSide(color: colors.error, width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          fixedSize: const Size.fromHeight(32),
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16, 8, 16, 8),
                        ),
                        onPressed: () {},
                        child: Text(
                          localization.reject,
                          style: textStyles.body2.copyWith(
                            color: colors.error,
                          ),
                        ),
                      ),
                    ),
                    Container(
                        margin: const EdgeInsetsDirectional.only(start: 8)),
                    Expanded(
                      child: OutlinedButton(
                        style: OutlinedButton.styleFrom(
                          foregroundColor: colors.success,
                          side: BorderSide(color: colors.success, width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          fixedSize: const Size.fromHeight(32),
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16, 8, 16, 8),
                        ),
                        onPressed: () {},
                        child: Text(
                          localization.approve,
                          style: textStyles.body2.copyWith(
                            color: colors.success,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
