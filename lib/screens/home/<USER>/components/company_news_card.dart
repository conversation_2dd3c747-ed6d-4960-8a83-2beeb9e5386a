import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

class CompanyNewsCard extends StatelessWidget {
  const CompanyNewsCard({
    super.key,
    required this.imagePath,
    this.margin,
    required this.time, // default time label
  });

  /// Path of the image to show inside the card.
  final String imagePath;

  /// Optional margin around the card.
  /// items in a carousel so the spacing can be controlled
  final EdgeInsetsGeometry? margin;

  /// Time label shown in the badge.
  final DateTime time;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Background image
            Image.asset(
              imagePath,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => Container(
                color: colors.backgroundContainer,
                alignment: Alignment.center,
                child: Icon(
                  Icons.image,
                  color: colors.tertiaryText,
                  size: 24,
                ),
              ),
            ),
            // Gradient overlay
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Color(0xBF000000), // 75% opacity black
                    Color(0x00000000), // 0% opacity black (transparent)
                  ],
                ),
              ),
            ),
            // Time badge
            PositionedDirectional(
              top: 16,
              end: 16,
              child: Container(
                padding: const EdgeInsetsDirectional.symmetric(
                    horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  formatTime(time, context),
                  style: textStyles.body3.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // Bottom-left description text overlay (two lines)
            PositionedDirectional(
              bottom: 32,
              start: 16,
              end:
                  20, // give some space so the text doesn't collide with scroll indicator on the right
              child: Text(
                'A new version of the employee portal has been released with performance improvements and a new dashboard.',
                maxLines: 2,
                // overflow: TextOverflow.ellipsis,
                style: textStyles.body3.copyWith(
                  // fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onPrimary,
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
