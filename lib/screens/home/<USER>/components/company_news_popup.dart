import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/screens/home/<USER>/components/news_detail_popup.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_card.dart';

class AllCompanyNewsScreen extends StatelessWidget {
  final VoidCallback? onBack;

  const AllCompanyNewsScreen({
    super.key,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: colors.background,
      body: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: onBack ??
                        () => Navigator.of(context, rootNavigator: true).pop(),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    localization.news,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Company News cards list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 16),
                itemCount: 8,
                itemBuilder: (context, index) {
                  //  different image for the second card (index == 1)
                  final imagePath = index == 1
                      ? 'assets/images/company_news_card2.jpg'
                      : 'assets/images/company_news_card1.jpg';

                  return GestureDetector(
                    onTap: () => _showNewsDetail(context),
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 8),
                      width: double.infinity,
                      height: 220,
                      child: CompanyNewsCard(
                        imagePath: imagePath,
                        time: DateTime.now(),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewsCard(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 220,
      child: CompanyNewsCard(
        imagePath: 'assets/images/company_news_card1.jpg',
        time: DateTime.now(),
      ),
    );
  }

  void _showNewsDetail(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    showAdaptivePopup(
      context,
      (ctx, sc) => NewsDetailPopup(
        onBack: () => Navigator.pop(ctx),
        title: 'Employee of the Month',
        publishedBy: localization.publishedBy,
        time: '${localization.today} 08:00 ${localization.am}',
        imagePath: 'assets/images/employee.png',
        employeeName: 'Howard Theodore',
        description:
            "We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.\n\n"
            "As part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:\n\n"
            "• Enhanced hospital coverage, including specialist care and private rooms\n"
            "• Increased outpatient benefits and faster claim processing\n"
            "• Coverage for mental health and wellness services\n"
            "• Improved support for family members (spouse and children)\n"
            "• A simplified registration and claim procedure via the HR portal",
      ),
      isDismissible: false,
      scrollable: true,
      contentPadding: EdgeInsets.zero,
      topRadius: 0,
      fullScreen: true,
      useRootNavigator: true,
    );
  }
}
