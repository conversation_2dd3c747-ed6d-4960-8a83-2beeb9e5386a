import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:hugeicons/hugeicons.dart';

class RequestLeavePopup extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final String? title;

  const RequestLeavePopup({
    super.key,
    this.onCancel,
    this.onSave,
    this.title,
  });

  @override
  State<RequestLeavePopup> createState() => _RequestLeavePopupState();
}

class _RequestLeavePopupState extends State<RequestLeavePopup> {
  final TextEditingController fromTimeController = TextEditingController();
  final TextEditingController toTimeController = TextEditingController();

  late List<String> leaveTypes;
  String selectedLeaveType = '';
  bool isDropdownOpen = false;
  bool isAllDay = false;
  bool isHalfDay = false;

  bool _isInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      // default state - both dates are today's date
      final today = DateTime.now();
      final formattedToday = formatDateDmy(today, context);
      fromTimeController.text = formattedToday;
      toTimeController.text = formattedToday;
      _isInitialized = true;
    }
  }

  @override
  void dispose() {
    fromTimeController.dispose();
    toTimeController.dispose();
    super.dispose();
  }

  // If dates are same then show both toggles half/all day - only one can be selected
  // If dates are different then hide both toggles
  bool _areDatesEqual() {
    if (fromTimeController.text.isEmpty || toTimeController.text.isEmpty) {
      return false;
    }
    return fromTimeController.text.trim() == toTimeController.text.trim();
  }

  /// toggle switch changes for all day leave when on
  void _handleAllDayToggle(bool value) {
    setState(() {
      isAllDay = value;

      if (value) {
        isHalfDay = false;
        // all day on - means both dates are same
        if (fromTimeController.text.isNotEmpty) {
          toTimeController.text = fromTimeController.text;
        } else if (toTimeController.text.isNotEmpty) {
          fromTimeController.text = toTimeController.text;
        } else {
          final today = DateTime.now();
          final formattedToday = formatDateDmy(today, context);
          fromTimeController.text = formattedToday;
          toTimeController.text = formattedToday;
        }
      }
    });
  }

  void _handleHalfDayToggle(bool value) {
    setState(() {
      isHalfDay = value;
      if (value) {
        isAllDay = false;
        // half day on - means both dates are same
        if (fromTimeController.text.isNotEmpty) {
          toTimeController.text = fromTimeController.text;
        } else if (toTimeController.text.isNotEmpty) {
          fromTimeController.text = toTimeController.text;
        } else {
          final today = DateTime.now();
          final formattedToday = formatDateDmy(today, context);
          fromTimeController.text = formattedToday;
          toTimeController.text = formattedToday;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Initialize leave types with localized strings
    leaveTypes = [
      localization.leaveType,
      localization.leaveType,
      localization.leaveType,
    ];
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // height: 333,
      padding: const EdgeInsetsDirectional.fromSTEB(12, 24, 12, 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 10),
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                widget.title ?? localization.requestForLeave,
                style: textStyles.body.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),

          // dropdown button
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 6),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      // isDropdownOpen = !isDropdownOpen;
                    });
                  },
                  child: Container(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          selectedLeaveType.isEmpty
                              ? localization.leaveType
                              : selectedLeaveType,
                          style: textStyles.body2.copyWith(
                            color: selectedLeaveType.isEmpty
                                ? colors.tertiaryText
                                : colors.primaryText,
                          ),
                        ),
                        Icon(
                          HugeIcons.strokeRoundedArrowDown01,
                          color: colors.primary,
                        ),
                      ],
                    ),
                  ),
                ),
                // below code is not given in the figma designs but maybe for future reference I've added it

                if (isDropdownOpen)
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 4),
                    constraints: BoxConstraints(maxHeight: 150),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: colors.strokeColor,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: leaveTypes.length,
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            setState(() {
                              selectedLeaveType = leaveTypes[index];
                              isDropdownOpen = false;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16, 12, 16, 12),
                            child: Text(
                              leaveTypes[index],
                              style: textStyles.body2.copyWith(
                                color: colors.primaryText,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),

          // both toggles only when both dates are equal
          if (_areDatesEqual()) ...[
            // Half day toggle switch
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(10, 6, 10, 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Half Day',
                    style: textStyles.body.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                  Transform.scale(
                    scale: 0.8,
                    child: CupertinoSwitch(
                      value: isHalfDay,
                      onChanged: _handleHalfDayToggle,
                      activeColor: colors.primary,
                      thumbColor: theme.colorScheme.onPrimary,
                      trackColor: colors.strokeColor,
                    ),
                  ),
                ],
              ),
            ),

            // All day toggle switch
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(10, 6, 10, 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localization.allDay,
                    style: textStyles.body.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                  Transform.scale(
                    scale: 0.8,
                    child: CupertinoSwitch(
                      value: isAllDay,
                      onChanged: _handleAllDayToggle,
                      activeColor: colors.primary,
                      thumbColor: theme.colorScheme.onPrimary,
                      trackColor: colors.strokeColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          //  from and to date/time fields
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
            child: Column(
              children: [
                // From time/date field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: fromTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.from,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.strokeColor),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Always read-only as we handle with pickers
                    onTap: () async {
                      final DateTime? picked = await showDatePickerDialog(
                        context: context,
                        initialDate: DateTime.now(),
                        minDate: DateTime.now(),
                        maxDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (picked != null) {
                        setState(() {
                          final formatted = formatDateDmy(picked, context);
                          fromTimeController.text = formatted;
                          if (isAllDay) {
                            toTimeController.text = formatted;
                          }

                          if (!_areDatesEqual()) {
                            isAllDay = false;
                            isHalfDay = false;
                          }
                        });
                      }
                    },
                  ),
                ),

                const SizedBox(height: 8),

                // To time/date field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: toTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.to,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.primary),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Always read-only as we handle with pickers
                    onTap: () async {
                      final DateTime? picked = await showDatePickerDialog(
                        context: context,
                        initialDate: DateTime.now(),
                        minDate: DateTime.now(),
                        maxDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (picked != null) {
                        setState(() {
                          final formatted = formatDateDmy(picked, context);
                          toTimeController.text = formatted;

                          if (!_areDatesEqual()) {
                            isAllDay = false;
                            isHalfDay = false;
                          }
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 16),
            child: Container(
              height: 40,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: widget.onSave ?? () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: colors.primaryText,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  localization.submit,
                  style: textStyles.buttonMedium.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _localizePeriod(String source, BuildContext context) {
    final localization = AppLocalizations.of(context)!;

    final amRegex = RegExp(r'\bAM\b', caseSensitive: false);
    final pmRegex = RegExp(r'\bPM\b', caseSensitive: false);

    return source
        .replaceAll(amRegex, localization.am)
        .replaceAll(pmRegex, localization.pm);
  }
}
