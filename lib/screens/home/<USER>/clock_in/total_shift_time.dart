import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ako_basma/providers/break_timer_provider.dart';
import 'package:ako_basma/providers/shift_timer_provider.dart';

class TotalShiftTime extends ConsumerStatefulWidget {
  const TotalShiftTime({super.key});

  ConsumerState<TotalShiftTime> createState() => _TotalShiftTimeState();
}

class _TotalShiftTimeState extends ConsumerState<TotalShiftTime> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final breakState = ref.watch(breakTimerProvider);
    final bool isBreakActive = breakState.isBreakActive;

    return Column(
      children: [
        Align(
          alignment: AlignmentDirectional.topStart,
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(12, 4, 12, 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: colors.background,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    isBreakActive
                        ? SolarIconsOutline.cup
                        : SolarIconsOutline.clockCircle,
                    color: colors.primary,
                    size: 32,
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getDisplayTime(),
                        style: textStyles.headline.copyWith(
                          color: colors.primaryText,
                          fontSize: 24,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isBreakActive
                            ? localization
                                .breakTime // Break Time text during break
                            : localization
                                .totalShiftTime, // Total Shift Time text during work
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _getDisplayTime() {
    final breakState = ref.watch(breakTimerProvider);
    if (breakState.isBreakActive) {
      return _formatDuration(breakState.elapsed);
    }
    final shiftState = ref.watch(shiftTimerProvider);
    return _formatDuration(shiftState.elapsed);
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }
}
