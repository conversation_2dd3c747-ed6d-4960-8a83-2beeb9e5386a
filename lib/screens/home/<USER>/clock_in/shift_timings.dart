import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

class ShiftTimings extends StatelessWidget {
  const ShiftTimings({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _buildTimeCard(
            context: context,
            icon: SolarIconsOutline.login,
            iconColor: colors.success,
            label: localization.startWork,
            // Use localized AM period instead of hard-coded string
            time: '8:00 ${localization.am}', // string + localization.am
          ),
        ),
        Container(
          width: 12,
        ),
        Expanded(
          child: _buildTimeCard(
            context: context,
            icon: SolarIconsOutline.logout,
            iconColor: colors.primary,
            label: localization.endWork,
            // Use localized PM period instead of hard-coded string
            time: '4:00 ${localization.pm}', // string + localization.pm
          ),
        ),
      ],
    );
  }

  Widget _buildTimeCard({
    required BuildContext context,
    required dynamic icon,
    required Color iconColor,
    required String label,
    required String time,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      width: MediaQuery.of(context).size.width * 0.4,
      padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 8),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsetsDirectional.all(6),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 16,
                ),
              ),
              Container(
                margin: const EdgeInsetsDirectional.only(start: 4),
                child: Text(
                  label,
                  style: textStyles.body3.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ),
            ],
          ),
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8, start: 2),
            child: Text(
              time,
              style: textStyles.body.copyWith(color: colors.primaryText),
            ),
          ),
        ],
      ),
    );
  }
}
