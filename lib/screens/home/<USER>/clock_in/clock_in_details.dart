import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/shift_buttons.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/shift_timings.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/total_shift_time.dart';

import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ako_basma/providers/shift_timer_provider.dart';
import 'package:solar_icons/solar_icons.dart';

class ClockInDetailsScreen extends ConsumerStatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? onEndShift;

  const ClockInDetailsScreen({
    super.key,
    this.onBack,
    this.onEndShift,
  });

  ConsumerState<ClockInDetailsScreen> createState() =>
      _ClockInDetailsScreenState();
}

Widget _buildDivider(ThemeData theme) {
  final colors = theme.extension<AppColors>()!;
  return Padding(
    padding: const EdgeInsetsDirectional.symmetric(horizontal: 12.0),
    child: Divider(height: 1, thickness: 1, color: colors.strokeColor),
  );
}

class _ClockInDetailsScreenState extends ConsumerState<ClockInDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // timer starts when screen is opened
    Future.microtask(() {
      ref.read(shiftTimerProvider.notifier).startShift();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth - 32; // 16px margin on each side

    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(16, 14, 16, 14),
      width: containerWidth,

      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        minHeight: 300,
      ),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      // location text
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: AlignmentDirectional.topStart,
              child: Padding(
                padding: const EdgeInsetsDirectional.all(13.0),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsetsDirectional.all(5),
                      decoration: BoxDecoration(
                        color: colors.primaryVariant,
                        borderRadius: BorderRadius.circular(13),
                      ),
                      width: 24,
                      height: 24,
                      child: Icon(
                        SolarIconsOutline.mapPoint,
                        color: colors.primary,
                        size: 14,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 10),
                      child: Text(
                        '10 Blackstone Street, London, UK',
                        style: textStyles.body3.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            _buildDivider(theme),
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(2, 2, 2, 2),
              child: const Row(
                children: [TotalShiftTime()],
              ),
            ),
            _buildDivider(theme),
            const SizedBox(height: 8),
            _buildTimeDuration(context, theme, localization.overtimeHoursLabel,
                '3${localization.h} 30${localization.m}'),
            _buildTimeDuration(
                context, theme, localization.breakTime, '30${localization.m}'),
            _buildTimeDuration(
                context, theme, localization.delay, '5${localization.m}'),
            Container(
              width: double.infinity,
              margin: const EdgeInsetsDirectional.fromSTEB(13, 8, 13, 12),
              child: const ShiftTimings(),
            ),
            Container(
              margin: const EdgeInsetsDirectional.fromSTEB(
                  6, 8, 6, 8), // Reduced vertical margins
              child: Column(
                children: [
                  ShiftButtons(
                    onEndShiftPressed: widget.onEndShift,
                  ),
                  // ClockOutButton(
                  //   onPressed: () {},
                  // ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildTimeDuration(BuildContext context, ThemeData theme,
    String timeDurationText, String actualTime) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsetsDirectional.fromSTEB(13, 4, 13, 4),
    width: double.infinity,
    child: Container(
      padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        color: colors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              timeDurationText,
              style: textStyles.body3.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ),
          Text(
            actualTime,
            style: textStyles.body3.copyWith(color: colors.secondaryText),
          ),
        ],
      ),
    ),
  );
}
