import 'package:ako_basma/labels.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/clock_in_button.dart';
import 'package:ako_basma/screens/home/<USER>/clock_in/shift_timings.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';

class ClockIn extends StatefulWidget {
  final VoidCallback? onClockInPressed;

  const ClockIn({
    super.key,
    this.onClockInPressed,
  });

  @override
  State<ClockIn> createState() => _ClockInState();
}

class _ClockInState extends State<ClockIn> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth =
        screenWidth - 32; // Full width minus margins (16px on each side)

    return Container(
      margin:
          const EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 8),
      width: containerWidth,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Location section
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: const EdgeInsetsDirectional.only(
                  top: 12, end: 14, bottom: 10, start: 14),
              child: Row(
                children: [
                  // Reduced icon container size for better visual balance
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: colors.primaryVariant,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      SolarIconsOutline.mapPoint,
                      color: colors.primary,
                      size: 20,
                    ),
                  ),
                  // Enhanced text section with better spacing and size
                  Expanded(
                    child: Container(
                      margin:
                          const EdgeInsetsDirectional.only(start: 8, end: 8),
                      child: Text(
                        '10 Blackstone Street, London, UK',
                        style: textStyles.body3.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Clock in button with increased margin for better visual spacing
          Container(
            margin: const EdgeInsetsDirectional.symmetric(
                horizontal: 20, vertical: 8),
            child: ClockInButton(
              onPressed: widget.onClockInPressed,
            ),
          ),
          // Shift timings with enhanced bottom spacing
          Container(
            margin: const EdgeInsetsDirectional.only(
                top: 20, bottom: 12, start: 16, end: 16),
            child: const ShiftTimings(),
          ),
        ],
      ),
    );
  }
}
