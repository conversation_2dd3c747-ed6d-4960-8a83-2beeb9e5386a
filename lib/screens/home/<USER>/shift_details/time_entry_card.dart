import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/components/time_picker/rounded_time_picker.dart';
import 'package:ako_basma/components/blur_map/blurred_map_background.dart';

class TimeEntryCard extends StatelessWidget {
  final String title;
  final String time;
  final String location;
  final VoidCallback? onEdit;
  final Function(String)? onTimeChanged;

  const TimeEntryCard({
    super.key,
    required this.title,
    required this.time,
    required this.location,
    this.onEdit,
    this.onTimeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width: screenWidth - 32, // Fill (328px)
      // let content determine height
      constraints: const BoxConstraints(minHeight: 100),
      margin: const EdgeInsetsDirectional.only(bottom: 8),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(width: 1, color: colors.primaryVariant),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and edit icon
          Container(
            padding: const EdgeInsetsDirectional.only(
              start: 12,
              top: 8,
              end: 12,
              bottom: 2, // Reduced gap to prevent overflow
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    final selectedTime = await showRoundedTimePicker(
                      context,
                      helpText: _getHelpTextForTitle(title, localization),
                    );
                    if (selectedTime != null && onTimeChanged != null) {
                      final formattedTime = _formatTime(selectedTime, context);
                      onTimeChanged!(formattedTime);
                    }
                  },
                  child: Icon(
                    SolarIconsOutline.pen2,
                    color: colors.primary,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          // Time
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
              child: Text(
                _localizePeriod(time, context),
                style: textStyles.body.copyWith(
                  color: colors.primaryText,
                  height: 1.2,
                ),
              ),
            ),
          ),

          // Reduced spacing
          const SizedBox(height: 4),

          // Location
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 8,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Location icon
                Container(
                  padding: const EdgeInsetsDirectional.all(4),
                  decoration: BoxDecoration(
                    color: colors.primaryVariant,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    SolarIconsOutline.mapPoint,
                    color: colors.primary,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    location,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ),

                GestureDetector(
                  onTap: () {
                    _showLocationMapPopup(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: colors.primary,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          SolarIconsOutline.mapPoint,
                          color: colors.primary,
                          size: 14,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          'View',
                          style: textStyles.body3.copyWith(
                            color: colors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(TimeOfDay time, BuildContext context) {
    // Retrieve localization based on context
    final localization = AppLocalizations.of(context)!;

    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period =
        time.period == DayPeriod.am ? localization.am : localization.pm;
    return '$hour:$minute $period';
  }

  String _getHelpTextForTitle(String title, AppLocalizations localization) {
    final helpTextMap = {
      localization.clockIn: localization.clockIn,
      localization.clockOut: localization.clockOut,
      localization.breakTime: localization.breakTimeLabel,
      localization.breakTimeLabel: localization.breakTimeLabel,
    };
    return helpTextMap[title] ?? title;
  }

  String _getLocationTitle(String title, AppLocalizations localization) {
    final locationTitleMap = {
      localization.clockIn: '${localization.clockIn} ${localization.location}',
      localization.clockOut:
          '${localization.clockOut} ${localization.location}',
      localization.breakTime:
          '${localization.breakTimeLabel} ${localization.location}',
      localization.breakTimeLabel:
          '${localization.breakTimeLabel} ${localization.location}',
    };
    return locationTitleMap[title] ?? '${localization.location}';
  }

  // Replace hard-coded period strings (AM/PM) in the supplied time string with
  // their localized equivalents. This allows callers to keep passing simple
  // strings (e.g. "8:00 AM") while still getting the correct translation for
  // the current locale. If the string already contains the localized values it
  // will remain unchanged.
  String _localizePeriod(String source, BuildContext context) {
    final localization = AppLocalizations.of(context)!;

    // Use a case-insensitive regex to match whole words AM / PM.
    final amRegex = RegExp(r'\bAM\b', caseSensitive: false);
    final pmRegex = RegExp(r'\bPM\b', caseSensitive: false);

    return source
        .replaceAll(amRegex, localization.am)
        .replaceAll(pmRegex, localization.pm);
  }

  void _showLocationMapPopup(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        final theme = Theme.of(context);
        final colors = theme.extension<AppColors>()!;
        final textStyles = theme.extension<TextStyles>()!;
        final localization = AppLocalizations.of(context)!;

        return Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
            color: colors.background,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // drag handle
              Container(
                padding: const EdgeInsets.only(top: 6, bottom: 8),
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: colors.strokeColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              // Header with close button
              Container(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        _getLocationTitle(title, localization),
                        style: textStyles.headline3.copyWith(
                          color: colors.primaryText,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: Icon(
                          Icons.close,
                          color: colors.error,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Map container
              Expanded(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 36),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: BlurredMapBackground(
                      child: Container(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
