import 'dart:io';

import 'package:ako_basma/l10n/ku/kurdish_cupertino_localization_delegate.dart';
import 'package:ako_basma/l10n/ku/kurdish_material_localization_delegate.dart';
import 'package:ako_basma/l10n/ku/kurdish_widget_localization_delegate.dart';
import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart'
    as libPhoneNumber;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';

import 'l10n/generated/app_localizations.dart';

import 'styles/theme.dart';
import 'providers/theme/theme_provider.dart';
import 'providers/language/language_provider.dart';

import 'util/router/router.dart';
import 'package:ako_basma/screens/splash/splash.dart';
import 'package:flutter_animate/flutter_animate.dart';

late final BaseDeviceInfo kDeviceInfo;
PackageInfo? kPackageInfo;

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Remove the hardcoded system UI overlay style that was causing flicker
  // This will be set dynamically based on theme in the builder

  // Hive.registerAdapter(ProfileAdapter());
  await Hive.initFlutter();
  await Hive.openBox(HiveUtils.accBoxKey);

  // to get device ids if needed.
  DeviceInfoPlugin infoPlugin = DeviceInfoPlugin();
  kDeviceInfo = Platform.isAndroid
      ? await infoPlugin.androidInfo
      : await infoPlugin.iosInfo;

  // for firebase init
  // await Firebase.initializeApp(
  //   options: DefaultFirebaseOptions.currentPlatform,
  // );

  // for phone number formatter lib
  await libPhoneNumber.init();
  await initializeDateFormatting('en');

  // for package info, to get things like eg. app name and version
  try {
    kPackageInfo = await PackageInfo.fromPlatform();
  } catch (e) {
    print('Error in Package info - $e');
  }

  runApp(const ProviderScope(child: MyApp()));
  FlutterNativeSplash.remove();
  // Don't remove native splash immediately
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});
  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  bool _splashVisible = true;
  bool _contentVisible = false;
  bool _showBackgroundContent = false;
  bool _nativeSplashRemoved = false;
  bool _initialSystemUISet = false;

  @override
  void initState() {
    super.initState();
    // Ensure theme is loaded before removing native splash
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 16), () {
        if (!_nativeSplashRemoved && mounted) {
          // Set the correct system UI before removing native splash
          _setInitialSystemUI();

          // Small additional delay to ensure UI is set
          Future.delayed(const Duration(milliseconds: 16), () {
            if (mounted && !_nativeSplashRemoved) {
              FlutterNativeSplash.remove();
              setState(() {
                _nativeSplashRemoved = true;
              });
            }
          });
        }
      });
    });
  }

  void _setInitialSystemUI() {
    if (_initialSystemUISet) return;
    _initialSystemUISet = true;

    bool shouldUseDark = false;

    try {
      // hive
      if (Hive.isBoxOpen(HiveUtils.accBoxKey)) {
        final box = Hive.box(HiveUtils.accBoxKey);
        final savedThemeMode = box.get('theme_mode');

        if (savedThemeMode == 'dark') {
          shouldUseDark = true;
        } else if (savedThemeMode == 'light') {
          shouldUseDark = false;
        } else {
          // For 'system' , check platform
          final platformBrightness =
              WidgetsBinding.instance.platformDispatcher.platformBrightness;
          shouldUseDark = platformBrightness == Brightness.dark;
        }
      } else {
        final platformBrightness =
            WidgetsBinding.instance.platformDispatcher.platformBrightness;
        shouldUseDark = platformBrightness == Brightness.dark;
        print('Hive box not ready, using platform brightness');
      }
    } catch (e) {
      // Fallback to platform brightness if there's an error
      final platformBrightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      shouldUseDark = platformBrightness == Brightness.dark;
      print('Error reading theme preference, using platform brightness: $e');
    }

    // Set system UI overlay style based on determined theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
        statusBarIconBrightness:
            shouldUseDark ? Brightness.light : Brightness.dark,
        systemNavigationBarIconBrightness:
            shouldUseDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: shouldUseDark ? Brightness.dark : Brightness.light,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    MaterialTheme theme = const MaterialTheme();
    final router = ref.watch(routerProvider);

    final themeMode = ref.watch(themeModeProvider);
    final locale = ref.watch(languageProvider);

    return MaterialApp.router(
      title: 'Ako Basma',
      debugShowCheckedModeBanner: false,
      themeMode: themeMode,
      darkTheme: theme.dark(),
      theme: theme.light(),
      routerConfig: router,
      locale: locale, // Use locale from language provider
      // Localization support
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        KurdishCupertinoLocalizations.delegate,
        KurdishMaterialLocalizations.delegate,
        KurdishWidgetLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      builder: (context, child) {
        // Get current theme and background color
        final isDark = Theme.of(context).brightness == Brightness.dark;
        final backgroundColor = isDark ? Colors.black : Colors.white;

        return Container(
          color: backgroundColor,
          child: Stack(
            children: [
              if (child != null && _showBackgroundContent)
                Opacity(
                  opacity: _contentVisible ? 1 : 0,
                  child: child,
                ),
              if (_splashVisible)
                AnimatedOpacity(
                  opacity: _contentVisible ? 0 : 1,
                  duration: 1000.milliseconds,
                  child: SplashScreen(
                    contentReady: () {
                      if (mounted) {
                        setState(() {
                          // Preload app content while splash is still visible
                          _showBackgroundContent = true;
                        });
                      }
                    },
                    onFadeStart: () {
                      if (mounted) {
                        setState(() {
                          _contentVisible = true;
                        });
                      }
                    },
                    onComplete: () {
                      if (mounted) {
                        setState(() {
                          _splashVisible = false;
                        });
                      }
                    },
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
