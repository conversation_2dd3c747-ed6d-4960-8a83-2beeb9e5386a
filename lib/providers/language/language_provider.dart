import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Language provider for managing app locale
class LanguageNotifier extends StateNotifier<Locale> {
  LanguageNotifier() : super(const Locale('en', 'US')) {
    _loadLanguage();
  }
  static const defaultLocale = Locale('en', 'gb');
  static const locales = {
    'en': Locale('en', 'gb'),
    'ar': Locale('ar'),
    'ku': Locale('ku'),
  };

  static const String _languageKey = 'selected_language';

  Future<void> _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(_languageKey) ?? 'en';
    state = locales[languageCode] ?? defaultLocale;
  }

  Future<void> changeLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
    state = locales[languageCode] ?? defaultLocale;
  }

  // String get currentLanguageCode => state.languageCode;

  // String get currentLanguageName {
  //   switch (state.languageCode) {
  //     case 'ar':
  //       return 'العربية';
  //     case 'en':
  //     default:
  //       return 'English';
  //   }
  // }

  // Note: App layout is always LTR for consistent UI
  // Only text content changes based on language selection
  // bool get isRTLLanguage => state.languageCode == 'ar';
}

// Provider for language management
final languageProvider = StateNotifierProvider<LanguageNotifier, Locale>((ref) {
  return LanguageNotifier();
});
