import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:ako_basma/util/hive/hive_util.dart';

class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  static const String _themeModeKey = 'theme_mode';

  // Initialize with system theme to avoid flash, then load saved preference
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeModeSync();
    // _setupSystemBrightnessListener();
  }

  // void _setupSystemBrightnessListener() {
  //   WidgetsBinding.instance.platformDispatcher.onPlatformBrightnessChanged =
  //       () {
  //     // Only update when using system theme to avoid overriding user choice
  //     if (state == ThemeMode.system) {
  //       // Force rebuild to reflect system brightness changes
  //       state = ThemeMode.system;
  //     }
  //   };
  // }

  void _loadThemeModeSync() {
    try {
      // Use synchronous access since Hive box should already be opened in main()
      final box = Hive.box(HiveUtils.accBoxKey);
      final savedThemeMode = box.get(_themeModeKey);

      if (savedThemeMode != null) {
        // Restore user's theme preference from persistent storage
        switch (savedThemeMode) {
          case 'dark':
            state = ThemeMode.dark;
            break;
          // case 'light':
          //   state = ThemeMode.light;
          //   break;
          // case 'system':
          default:
            state = ThemeMode.light;
            break;
        }
      } else {
        // Default to system theme if nothing is saved
        state = ThemeMode.light;
      }
    } catch (e) {
      // Fallback to system theme if there's any error
      state = ThemeMode.light;
      print('Error loading theme mode: $e');
    }
  }

  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    try {
      final box = Hive.box(HiveUtils.accBoxKey);
      String themeModeString;

      switch (themeMode) {
        case ThemeMode.dark:
          themeModeString = 'dark';
          break;
        default:
          themeModeString = 'light';
          break;
      }

      await box.put(_themeModeKey, themeModeString);
    } catch (e) {
      print('Error saving theme mode: $e');
    }
  }

  Future<void> toggleTheme(bool isDarkMode) async {
    final newThemeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    state = newThemeMode;
    await _saveThemeMode(newThemeMode);
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = themeMode;
    await _saveThemeMode(themeMode);
  }

  bool get isDarkMode => state == ThemeMode.dark;
  bool get isLightMode => state == ThemeMode.light;
}

final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>(
  (ref) => ThemeModeNotifier(),
);

final isDarkModeProvider = Provider<bool>((ref) {
  final themeMode = ref.watch(themeModeProvider);
  return themeMode == ThemeMode.dark;
});
