import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Holds the break timer state.
class BreakTimerState {
  final bool isBreakActive;
  final Duration elapsed;

  const BreakTimerState({
    required this.isBreakActive,
    required this.elapsed,
  });

  BreakTimerState copyWith({bool? isBreakActive, Duration? elapsed}) {
    return BreakTimerState(
      isBreakActive: isBreakActive ?? this.isBreakActive,
      elapsed: elapsed ?? this.elapsed,
    );
  }

  static const initial = BreakTimerState(
    isBreakActive: false,
    elapsed: Duration.zero,
  );
}

class BreakTimerNotifier extends StateNotifier<BreakTimerState> {
  BreakTimerNotifier() : super(BreakTimerState.initial);

  Timer? _timer;

  void startBreak() {
    _timer?.cancel();
    state = state.copyWith(isBreakActive: true, elapsed: Duration.zero);

    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      // Increment elapsed every second
      final updatedElapsed = state.elapsed + const Duration(seconds: 1);
      state = state.copyWith(elapsed: updatedElapsed);
    });
  }

  void endBreak() {
    _timer?.cancel();
    _timer = null;
    state = state.copyWith(isBreakActive: false);
  }

  void reset() {
    _timer?.cancel();
    _timer = null;
    state = BreakTimerState.initial;
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

final breakTimerProvider =
    StateNotifierProvider<BreakTimerNotifier, BreakTimerState>(
  (ref) => BreakTimerNotifier(),
);
