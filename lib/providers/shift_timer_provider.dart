import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

class ShiftTimerState {
  final bool isRunning;
  final Duration elapsed;
  final bool isShiftEnded; // Track if shift was ended

  const ShiftTimerState({
    required this.isRunning,
    required this.elapsed,
    this.isShiftEnded = false,
  });

  ShiftTimerState copyWith(
      {bool? isRunning, Duration? elapsed, bool? isShiftEnded}) {
    return ShiftTimerState(
      isRunning: isRunning ?? this.isRunning,
      elapsed: elapsed ?? this.elapsed,
      isShiftEnded: isShiftEnded ?? this.isShiftEnded,
    );
  }

  static const initial = ShiftTimerState(
      isRunning: false, elapsed: Duration.zero, isShiftEnded: false);
}

class ShiftTimerNotifier extends StateNotifier<ShiftTimerState> {
  ShiftTimerNotifier() : super(ShiftTimerState.initial);

  Timer? _timer;

  void _startTicker() {
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      state =
          state.copyWith(elapsed: state.elapsed + const Duration(seconds: 1));
    });
  }

  /// clock-in button triggers this func
  void startShift() {
    if (state.isRunning) return; // Already running
    state = const ShiftTimerState(
        isRunning: true, elapsed: Duration.zero, isShiftEnded: false);
    _timer?.cancel();
    _startTicker();
  }

  void resume() {
    if (state.isRunning) return;
    state = state.copyWith(isRunning: true);
    _timer?.cancel();
    _startTicker();
  }

  void pause() {
    if (!state.isRunning) return;
    _timer?.cancel();
    state = state.copyWith(isRunning: false);
  }

  void endShift() {
    _timer?.cancel();
    state = state.copyWith(isRunning: false, isShiftEnded: true);
  }

  void reset() {
    _timer?.cancel();
    state = ShiftTimerState.initial;
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

final shiftTimerProvider =
    StateNotifierProvider<ShiftTimerNotifier, ShiftTimerState>(
  (ref) => ShiftTimerNotifier(),
);
