import 'package:flutter/material.dart';
import 'colors.dart';
import 'util.dart'; // Import the util.dart file for AppTextStyles

class MaterialTheme {
  const MaterialTheme();
// wont really be using these now...
  static ColorScheme lightScheme() {
    return const ColorScheme(
      brightness: Brightness.light,
      primary: DesignColors.lightPrimary,
      onPrimary: Color(0xDEFFFFFF),
      primaryContainer: DesignColors.lightPrimaryVariant,
      onPrimaryContainer: DesignColors.lightPrimaryText,
      secondary: DesignColors.lightSecondary,
      onSecondary: Color(0xDEFFFFFF),
      secondaryContainer: DesignColors.lightSecondaryVariant,
      onSecondaryContainer: DesignColors.lightPrimaryText,
      error: DesignColors.error,
      onError: Color(0xDEFFFFFF),
      errorContainer: DesignColors.errorContainerLight,
      onErrorContainer: DesignColors.lightPrimaryText,
      surface: DesignColors.lightSurface,
      onSurface: DesignColors.lightPrimaryText,
      onSurfaceVariant: DesignColors.lightSecondaryText,
      outline: DesignColors.lightStrokeColor,
      outlineVariant: DesignColors.lightDisabled,
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xDE2F2F2F),
      inversePrimary: DesignColors.lightPrimaryVariant,
      surfaceDim: DesignColors.lightBackground,
      surfaceBright: DesignColors.lightSurface,
      surfaceContainerLowest: DesignColors.lightSurface,
      surfaceContainerLow: DesignColors.lightBackground,
      surfaceContainer: DesignColors.lightBackground,
      surfaceContainerHigh: DesignColors.lightSurface,
      surfaceContainerHighest: DesignColors.lightSurface,
    );
  }

  ThemeData light() {
    return theme(lightScheme());
  }

// wont really be using these now...
  static ColorScheme darkScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: DesignColors.darkPrimary,
      onPrimary: DesignColors.darkPrimaryText,
      primaryContainer: DesignColors.darkPrimaryVariant,
      onPrimaryContainer: DesignColors.darkPrimaryText,
      secondary: DesignColors.darkSecondary,
      onSecondary: DesignColors.darkPrimaryText,
      secondaryContainer: DesignColors.darkSecondaryVariant,
      onSecondaryContainer: DesignColors.darkPrimaryText,
      error: DesignColors.error,
      onError: DesignColors.darkPrimaryText,
      errorContainer: DesignColors.errorContainerDark,
      onErrorContainer: DesignColors.darkPrimaryText,
      surface: DesignColors.darkBackground,
      onSurface: DesignColors.darkPrimaryText,
      onSurfaceVariant: DesignColors.darkSecondaryText,
      outline: DesignColors.darkStrokeColor,
      outlineVariant: DesignColors.darkDisabled,
      shadow: Color(0xFF000000),
      scrim: Color(0xFF000000),
      inverseSurface: Color(0xFFFFFFFF),
      inversePrimary: DesignColors.darkPrimary,
      surfaceDim: Colors.black,
      surfaceBright: DesignColors.darkBackground,
      surfaceContainerLowest: Colors.black,
      surfaceContainerLow: Colors.black,
      surfaceContainer: Colors.black,
      surfaceContainerHigh: DesignColors.darkSurface,
      surfaceContainerHighest: DesignColors.darkSurface,
    );
  }

  ThemeData dark() {
    return theme(darkScheme());
  }

  // Text theme configuration
  static TextTheme createTextTheme() {
    // wont really be using these now...
    return TextTheme(
      // Headline styles
      displayLarge: AppTextStyles.headline1, // Headline 1
      displayMedium: AppTextStyles.headline2, // Headline 2
      displaySmall: AppTextStyles.headline3, // Headline 3
      headlineLarge: AppTextStyles.headline4, // Headline 4

      // Body styles
      bodyLarge: AppTextStyles.body1, // Body 1
      bodyMedium: AppTextStyles.body2, // Body 2
      bodySmall: AppTextStyles.body3, // Body 3

      // Button styles
      titleLarge: AppTextStyles.buttonNormal, // Button Normal
      titleMedium: AppTextStyles.buttonMedium, // Button Medium
      titleSmall: AppTextStyles.buttonSmall, // Button Small

      // Field styles
      labelLarge: AppTextStyles.fieldText1, // Field Text 1
      labelMedium: AppTextStyles.fieldText2, // Field Text 2
      labelSmall: AppTextStyles.fieldHint, // Field Hint
    );
  }

  ThemeData theme(ColorScheme colorScheme) => ThemeData(
        useMaterial3: true,
        brightness: colorScheme.brightness,
        colorScheme: colorScheme,
        textTheme: createTextTheme().apply(
          bodyColor: colorScheme.onSurface,
          displayColor: colorScheme.onSurface,
        ),
        extensions: [
          TextStyles.create(colorScheme),
          AppColors.create(colorScheme),
        ],
        scaffoldBackgroundColor: colorScheme.surface,
        canvasColor: colorScheme.brightness == Brightness.dark
            ? Colors.black
            : colorScheme.surface,
      );

  // List<ExtendedColor> get extendedColors => [];
}

extension HexColor on Color {
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}

class TextStyles extends ThemeExtension<TextStyles> {
  final TextStyle headline;
  final TextStyle headline2;
  final TextStyle headline3;
  final TextStyle headline4;
  final TextStyle body;
  final TextStyle body2;
  final TextStyle body3;
  final TextStyle button;
  final TextStyle buttonMedium;
  final TextStyle textButton;
  final TextStyle buttonSmall;
  final TextStyle fieldText;
  final TextStyle fieldText2;
  final TextStyle fieldLabel;
  final TextStyle fieldInput;
  final TextStyle fieldHint;

  const TextStyles({
    required this.headline,
    required this.headline2,
    required this.headline3,
    required this.headline4,
    required this.body,
    required this.body2,
    required this.body3,
    required this.button,
    required this.buttonMedium,
    required this.textButton,
    required this.buttonSmall,
    required this.fieldText,
    required this.fieldText2,
    required this.fieldLabel,
    required this.fieldInput,
    required this.fieldHint,
  });

  static TextStyles of(BuildContext context) {
    return Theme.of(context).extension<TextStyles>()!;
  }

  static TextStyles create(ColorScheme colorScheme) {
    return TextStyles(
      headline: AppTextStyles.headline1.copyWith(color: colorScheme.onSurface),
      headline2: AppTextStyles.headline2.copyWith(color: colorScheme.onSurface),
      headline3: AppTextStyles.headline3.copyWith(color: colorScheme.onSurface),
      headline4: AppTextStyles.headline4.copyWith(color: colorScheme.onSurface),
      body: AppTextStyles.body1.copyWith(color: colorScheme.onSurface),
      body2: AppTextStyles.body2.copyWith(color: colorScheme.onSurface),
      body3: AppTextStyles.body3.copyWith(color: colorScheme.onSurface),
      textButton: AppTextStyles.textButton.copyWith(color: colorScheme.primary),
      button: AppTextStyles.buttonNormal.copyWith(color: colorScheme.onSurface),
      buttonMedium:
          AppTextStyles.buttonMedium.copyWith(color: colorScheme.onSurface),
      buttonSmall:
          AppTextStyles.buttonSmall.copyWith(color: colorScheme.onSurface),
      fieldText:
          AppTextStyles.fieldText1.copyWith(color: colorScheme.onSurface),
      fieldText2:
          AppTextStyles.fieldText2.copyWith(color: colorScheme.onSurface),
      fieldLabel:
          AppTextStyles.fieldLabel.copyWith(color: colorScheme.onSurface),
      fieldInput:
          AppTextStyles.fieldInput.copyWith(color: colorScheme.onSurface),
      fieldHint:
          AppTextStyles.fieldHint.copyWith(color: colorScheme.onSurfaceVariant),
    );
  }

  @override
  ThemeExtension<TextStyles> lerp(ThemeExtension<TextStyles>? other, double t) {
    if (other is! TextStyles) {
      return this;
    }
    return TextStyles(
      headline: TextStyle.lerp(headline, other.headline, t)!,
      headline2: TextStyle.lerp(headline2, other.headline2, t)!,
      headline3: TextStyle.lerp(headline3, other.headline3, t)!,
      headline4: TextStyle.lerp(headline4, other.headline4, t)!,
      body: TextStyle.lerp(body, other.body, t)!,
      body2: TextStyle.lerp(body2, other.body2, t)!,
      body3: TextStyle.lerp(body3, other.body3, t)!,
      button: TextStyle.lerp(button, other.button, t)!,
      textButton: TextStyle.lerp(textButton, other.textButton, t)!,
      buttonMedium: TextStyle.lerp(buttonMedium, other.buttonMedium, t)!,
      buttonSmall: TextStyle.lerp(buttonSmall, other.buttonSmall, t)!,
      fieldText: TextStyle.lerp(fieldText, other.fieldText, t)!,
      fieldText2: TextStyle.lerp(fieldText2, other.fieldText2, t)!,
      fieldLabel: TextStyle.lerp(fieldLabel, other.fieldLabel, t)!,
      fieldInput: TextStyle.lerp(fieldInput, other.fieldInput, t)!,
      fieldHint: TextStyle.lerp(fieldHint, other.fieldHint, t)!,
    );
  }

  @override
  ThemeExtension<TextStyles> copyWith() => this;
}

class AppColors extends ThemeExtension<AppColors> {
  final Color primary;
  final Color primaryVariant;
  final Color secondary;
  final Color secondaryVariant;
  final Color background;
  final Color surface;
  final Color disabled;
  final Color primaryText;
  final Color secondaryText;
  final Color tertiaryText;
  final Color strokeColor;
  final Color backgroundContainer;
  final Color success;
  final Color successContainer;
  final Color warning;
  final Color warningContainer;
  final Color error;
  final Color errorContainer;
  final Color info;
  final Color infoContainer;
  final LinearGradient primaryGradient;

  const AppColors({
    required this.primary,
    required this.primaryVariant,
    required this.secondary,
    required this.secondaryVariant,
    required this.background,
    required this.surface,
    required this.disabled,
    required this.primaryText,
    required this.secondaryText,
    required this.tertiaryText,
    required this.strokeColor,
    required this.backgroundContainer,
    required this.success,
    required this.successContainer,
    required this.warning,
    required this.warningContainer,
    required this.error,
    required this.errorContainer,
    required this.info,
    required this.infoContainer,
    required this.primaryGradient,
  });

  static AppColors of(BuildContext context) {
    return Theme.of(context).extension<AppColors>()!;
  }

  static AppColors create(ColorScheme colorScheme) {
    if (colorScheme.brightness == Brightness.light) {
      return const AppColors(
        primary: DesignColors.lightPrimary,
        primaryVariant: DesignColors.lightPrimaryVariant,
        secondary: DesignColors.lightSecondary,
        secondaryVariant: DesignColors.lightSecondaryVariant,
        background: DesignColors.lightBackground,
        surface: DesignColors.lightSurface,
        disabled: DesignColors.lightDisabled,
        primaryText: DesignColors.lightPrimaryText,
        secondaryText: DesignColors.lightSecondaryText,
        tertiaryText: DesignColors.lightTertiaryText,
        strokeColor: DesignColors.lightStrokeColor,
        backgroundContainer: DesignColors.lightBackgroundContainer,
        success: DesignColors.success,
        successContainer: DesignColors.successContainerLight,
        warning: DesignColors.warning,
        warningContainer: DesignColors.warningContainerLight,
        error: DesignColors.error,
        errorContainer: DesignColors.errorContainerLight,
        info: DesignColors.info,
        infoContainer: DesignColors.infoContainerLight,
        primaryGradient: DesignColors.lightPrimaryGradient,
      );
    } else {
      return const AppColors(
        primary: DesignColors.darkPrimary,
        primaryVariant: DesignColors.darkPrimaryVariant,
        secondary: DesignColors.darkSecondary,
        secondaryVariant: DesignColors.darkSecondaryVariant,
        background: DesignColors.darkBackground,
        surface: DesignColors.darkSurface,
        disabled: DesignColors.darkDisabled,
        primaryText: DesignColors.darkPrimaryText,
        secondaryText: DesignColors.darkSecondaryText,
        tertiaryText: DesignColors.darkTertiaryText,
        strokeColor: DesignColors.darkStrokeColor,
        backgroundContainer: DesignColors.darkBackgroundContainer,
        success: DesignColors.success,
        successContainer: DesignColors.successContainerDark,
        warning: DesignColors.warning,
        warningContainer: DesignColors.warningContainerDark,
        error: DesignColors.error,
        errorContainer: DesignColors.errorContainerDark,
        info: DesignColors.info,
        infoContainer: DesignColors.infoContainerDark,
        primaryGradient: DesignColors.darkPrimaryGradient,
      );
    }
  }

  @override
  ThemeExtension<AppColors> lerp(ThemeExtension<AppColors>? other, double t) {
    if (other is! AppColors) {
      return this;
    }
    return AppColors(
      primary: Color.lerp(primary, other.primary, t)!,
      primaryVariant: Color.lerp(primaryVariant, other.primaryVariant, t)!,
      secondary: Color.lerp(secondary, other.secondary, t)!,
      secondaryVariant:
          Color.lerp(secondaryVariant, other.secondaryVariant, t)!,
      background: Color.lerp(background, other.background, t)!,
      surface: Color.lerp(surface, other.surface, t)!,
      disabled: Color.lerp(disabled, other.disabled, t)!,
      primaryText: Color.lerp(primaryText, other.primaryText, t)!,
      secondaryText: Color.lerp(secondaryText, other.secondaryText, t)!,
      tertiaryText: Color.lerp(tertiaryText, other.tertiaryText, t)!,
      strokeColor: Color.lerp(strokeColor, other.strokeColor, t)!,
      backgroundContainer:
          Color.lerp(backgroundContainer, other.backgroundContainer, t)!,
      success: Color.lerp(success, other.success, t)!,
      successContainer:
          Color.lerp(successContainer, other.successContainer, t)!,
      warning: Color.lerp(warning, other.warning, t)!,
      warningContainer:
          Color.lerp(warningContainer, other.warningContainer, t)!,
      error: Color.lerp(error, other.error, t)!,
      errorContainer: Color.lerp(errorContainer, other.errorContainer, t)!,
      info: Color.lerp(info, other.info, t)!,
      infoContainer: Color.lerp(infoContainer, other.infoContainer, t)!,
      primaryGradient:
          LinearGradient.lerp(primaryGradient, other.primaryGradient, t)!,
    );
  }

  @override
  ThemeExtension<AppColors> copyWith() => this;
}

class AppDimensions {
  // Standard horizontal margins for consistency across all screens
  static const double horizontalMargin = 16.0;
  static const double containerHorizontalPadding = 16.0;

  // Standard container width calculation (screen width minus 16px margins on each side)
  static double getStandardContainerWidth(BuildContext context) {
    return MediaQuery.of(context).size.width - (horizontalMargin * 2);
  }

  // Standard horizontal margin EdgeInsets
  static const EdgeInsets standardHorizontalMargin =
      EdgeInsets.symmetric(horizontal: horizontalMargin);

  // Standard horizontal padding EdgeInsets
  static const EdgeInsets standardHorizontalPadding =
      EdgeInsets.symmetric(horizontal: containerHorizontalPadding);

  // Standard vertical spacing between components
  static const double componentSpacing = 8.0;
  static const double sectionSpacing = 16.0;

  // Carousel card consistency
  static const double taskCardCarouselHeight = 165.0;
  static const double companyNewsCarouselHeight = 225.0;

  // height
  static double getTaskCardHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final responsiveHeight = screenHeight * 0.18;
    return responsiveHeight.clamp(165.0, 180.0);
  }

  static double getCompanyNewsHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final responsiveHeight = screenHeight * 0.25;
    return responsiveHeight.clamp(200.0, 250.0);
  }
}
