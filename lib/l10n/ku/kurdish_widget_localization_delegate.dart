import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class _KurdishMaterialLocalizationsDelegate
    extends LocalizationsDelegate<WidgetsLocalizations> {
  const _KurdishMaterialLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => locale.languageCode == 'ku';

  @override
  Future<WidgetsLocalizations> load(Locale locale) async {
    return SynchronousFuture<WidgetsLocalizations>(
      KurdishWidgetLocalizations(),
    );
  }

  @override
  bool shouldReload(_KurdishMaterialLocalizationsDelegate old) => false;
}

class KurdishWidgetLocalizations extends WidgetsLocalizations {
  static const LocalizationsDelegate<WidgetsLocalizations> delegate =
      _KurdishMaterialLocalizationsDelegate();

  @override
  TextDirection get textDirection => TextDirection.rtl;

  @override
  String get reorderItemDown => throw UnimplementedError();

  @override
  String get reorderItemLeft => throw UnimplementedError();

  @override
  String get reorderItemRight => throw UnimplementedError();

  @override
  String get reorderItemToEnd => throw UnimplementedError();

  @override
  String get reorderItemToStart => throw UnimplementedError();

  @override
  String get reorderItemUp => throw UnimplementedError();
    
    @override
String get copyButtonLabel => 'لەبەرگرتن'; // "Copy" in Kurdish

@override
String get cutButtonLabel => 'بڕین'; // "Cut" in Kurdish

@override
String get lookUpButtonLabel => 'گەڕان'; // "Look up" in Kurdish

@override
String get pasteButtonLabel => 'لکاندن'; // "Paste" in Kurdish

@override
String get searchWebButtonLabel => 'گەڕان لە وێب'; // "Search web" in Kurdish

@override
String get selectAllButtonLabel => 'هەموو هەلبژێرە'; // "Select all" in Kurdish

@override
String get shareButtonLabel => 'هاوبەشکردن'; // "Share" in Kurdish
}
