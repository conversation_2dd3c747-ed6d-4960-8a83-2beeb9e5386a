// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Kurdish (`ku`).
class AppLocalizationsKu extends AppLocalizations {
  AppLocalizationsKu([String locale = 'ku']) : super(locale);

  @override
  String get about => 'نزیکەی';

  @override
  String get aboutUs => 'دەربارەی ئێمە';

  @override
  String get absenceDeduction => 'داشکاندنی ئامادەنەبوون';

  @override
  String get accountInfo => 'زانیاری ئەکاونت';

  @override
  String get active => 'چالاک';

  @override
  String get activity => 'چالاکی';

  @override
  String get add => 'زیادکردن';

  @override
  String get addAComment => 'کۆمێنت زیاد بکە';

  @override
  String get addAnnouncement => 'ڕیکلامێک زیاد بکە';

  @override
  String get addATask => 'ئەرکێک زیاد بکە';

  @override
  String get addBreak => 'پشوویەک زیاد بکە';

  @override
  String get addComment => 'کۆمێنتێک زیاد بکە';

  @override
  String get addDepartment => 'بەشێک زیاد بکە';

  @override
  String get addedSuccessfully => 'بە سەرکەوتوویی زیاد کرا';

  @override
  String get addEmployee => 'کارمەندێک زیاد بکە';

  @override
  String get additionalNotes => 'تێبینی زیادە';

  @override
  String get addLeave => 'پشوویەک زیاد بکە';

  @override
  String get addNewOffice => 'ئۆفیسێکی نوێ زیاد بکە';

  @override
  String get addNote => 'تێبینییەک زیاد بکە';

  @override
  String get addPolicy => 'سیاسەتێک زیاد بکە';

  @override
  String get address => 'ناونیشان';

  @override
  String get addressInformation => 'زانیاری ناونیشان';

  @override
  String get addShift => 'شۆفتێک زیاد بکە';

  @override
  String get addTask => 'زیادکردنی گرنگ';

  @override
  String get adminContactMessage =>
      'ئەگەر دەتەوێت دەستکاری بکەیت، تکایە پەیوەندی بە ئیدارەوە بکە.';

  @override
  String get ai => 'AI';

  @override
  String get aiCapablityLine1 => 'حەز دەکەیت ئەرکێکت بۆ ڕێکبخەم؟';

  @override
  String get aiCapablityLine2 => 'بەدوای زانیاری کارمەنددا دەگەڕێیت؟';

  @override
  String get aiCapablityLine3 => 'یان شتێکت بۆ بکەم؟';

  @override
  String get aiCapablityLine4 =>
      'تەنها پێم بڵێ چیت پێویستە، من لێرەم بۆ یارمەتیدان!';

  @override
  String get aiChatGreeting => 'بەخێربێن بۆ ئەکو بەسمە AI!';

  @override
  String get aiGreetingText => 'ئەمڕۆ چۆن بتوانم یارمەتیت بدەم؟';

  @override
  String get akoBasma => 'ئەکو بەسمە';

  @override
  String get all => 'هەموو کەسێک';

  @override
  String get allDay => 'بە درێژایی ڕۆژ';

  @override
  String get allEmployees => 'سەرجەم فەرمانبەران';

  @override
  String get allOffices => 'هەموو فەرمانگەکان';

  @override
  String get allowances => 'تەرخانکردنەکان';

  @override
  String get allProjects => 'هەموو پڕۆژەکان';

  @override
  String get allTime => 'هەموو کاتێک';

  @override
  String get amount => 'بڕە پارەکە';

  @override
  String get amountOfDecrease => 'کەم و کوڕی';

  @override
  String get amountOfIncrease => 'بڕی زیادبوونی';

  @override
  String get amountOfIncreaseOrDecrease => 'بڕی زیادبوون یان کەمبوونەوەی';

  @override
  String get announcement => 'ڕێکلام';

  @override
  String get approve => 'ڕەزامەندی';

  @override
  String get approvedBy => 'پەسەند کراوە لەلایەن';

  @override
  String get approveTask => 'پەسەندکردنی ئەرکەکان';

  @override
  String approximateToDistanceMetres(Object distance) {
    return 'نزیکەی $distance مەتر لێیەوە دوورە';
  }

  @override
  String get arabic => 'عەرەبی';

  @override
  String get archive => 'ئەرشیفکردن';

  @override
  String get archiveEmployee => 'ئەرشیفکردنی کارمەند';

  @override
  String get archiveEmployeeWarning =>
      'ئەگەر فەرمانبەرێک ئەرشیف کرا، ئەکاونتەکەی لەکاردەخرێت!';

  @override
  String get archiveEmployeeWarningSubtitle =>
      'ئایا دڵنیای کە دەتەوێت کارمەندەکە ئەرشیف بکەیت؟';

  @override
  String get archives => 'ئەرشیفەکان';

  @override
  String get asiaBaghdad => 'ئاسیا/بەغدا';

  @override
  String get assign => 'بەکرێگرتن';

  @override
  String get assignee => 'ڕاسپێردراو';

  @override
  String get assignToMe => 'بۆ من دەستنیشان بکە';

  @override
  String get attachment => 'هاوپێچ';

  @override
  String get attendance => 'ئامادەبوون';

  @override
  String get availableOnWeekends => 'لە کۆتایی هەفتەدا بەردەستە';

  @override
  String get back => 'گەڕانەوە';

  @override
  String get backlog => 'پاشەکەوت';

  @override
  String get basicSalary => 'مووچەی بنەڕەتی';

  @override
  String get branch => 'لق';

  @override
  String get breakTime => 'کاتی پشوو:';

  @override
  String get breakTimeLabel => 'کاتی پشوو';

  @override
  String get call => 'پەیوەندی';

  @override
  String get camera => 'کامێرا';

  @override
  String get cancel => 'ڕەتکردنەوە';

  @override
  String get canIGetMoreInfo => 'ئایا دەتوانم زانیاری زیاتر بەدەست بهێنم؟';

  @override
  String get changesSavedSuccessfully =>
      'گۆڕانکارییەکان بە سەرکەوتوویی پاشەکەوتکراون';

  @override
  String get chat => 'پەیامەکان';

  @override
  String get chatRequestConfirmation =>
      'ئایا دڵنیای کە دەتەوێت ئەم داواکارییە بنێری؟';

  @override
  String get chatWithAi => 'چات لەگەڵ AI بکە';

  @override
  String get chatWithHr => 'لەگەڵ HR چات بکە';

  @override
  String get checkedOutHelpText => 'چێک ئاوت کراوە';

  @override
  String get checkInTime => 'کاتی هاتنە ژوورەوە';

  @override
  String get checkOutTime => 'کاتی دەرچوون';

  @override
  String get checkYourMessages => 'نامەکانت بپشکنە';

  @override
  String get city => 'شار';

  @override
  String get clear => 'ڕوون';

  @override
  String get clearSelection => 'هەڵبژاردنی پاک بکەرەوە';

  @override
  String get clickToUpload => 'بۆ بارکردن کلیک بکە';

  @override
  String get clockIn => 'کاتژمێر ئین';

  @override
  String get clockInTime => 'کاتی کاتژمێر-ئین';

  @override
  String get clockOut => 'کاتژمێر دەرچوون';

  @override
  String get clockOutTime => 'کاتی دەرچوونی کاتژمێر';

  @override
  String get close => 'داخستن';

  @override
  String get closingTimeMustBeAfterOpeningTime =>
      'کاتی داخستنی دەبێت دوای کاتی کردنەوەی بێت.';

  @override
  String get comments => 'کۆمێنتەکان';

  @override
  String get companyDirector => 'بەڕێوەبەری کۆمپانیا';

  @override
  String get companyInformation => 'زانیاری کۆمپانیا';

  @override
  String get companyJobNumberStart => 'ڕیزبەندی ژمارەی ستافی کۆمپانیا';

  @override
  String get companyName => 'ناوی کۆمپانیا';

  @override
  String get companyNews => 'هەواڵی کۆمپانیا';

  @override
  String get companyPolicy => 'سیاسەتی کۆمپانیا';

  @override
  String get completed => 'تەواو بووە';

  @override
  String get completedTasks => 'ئەرکە تەواوکراوەکان';

  @override
  String get confirm => 'دڵنیاکردنەوە';

  @override
  String get confirmed => 'پشتڕاستکراوەتەوە';

  @override
  String get confirmHours => 'کاتژمێرەکان پشتڕاست بکەرەوە';

  @override
  String get confirmPassword => 'وشەی نهێنی پشتڕاست بکەرەوە';

  @override
  String get contact => 'پەیوەندییەکان';

  @override
  String get contactNumber => 'ژمارەی پەیوەندی';

  @override
  String get contractType => 'جۆری گرێبەست';

  @override
  String get country => 'وڵات';

  @override
  String get create => 'دروستکردن';

  @override
  String get createAProject => 'دروستکردنی پرۆژە';

  @override
  String get createATask => 'دروستکردنی ئەرکێک';

  @override
  String get createProject => 'دروستکردنی پرۆژە';

  @override
  String get createTask => 'دروستکردنی ئەرک';

  @override
  String get critical => 'گرنگ';

  @override
  String get currency => 'دراو';

  @override
  String get currentShift => 'گۆڕانی ئێستا';

  @override
  String get customDateRange => 'مەودای بەرواری تایبەتمەند';

  @override
  String get daily => 'ڕۆژانە';

  @override
  String get dailyShifts => 'شۆفتی ڕۆژانە';

  @override
  String get dark => 'تاریک';

  @override
  String get date => 'ڕێکەوت';

  @override
  String get dateOfBirth => 'بەرواری لە دایک بوون';

  @override
  String get dateOfJoining => 'بەرواری بەشداریکردن';

  @override
  String get dateOfResignation => 'بەرواری دەستلەکارکێشانەوە';

  @override
  String get days => 'ڕۆژان';

  @override
  String get decrease => 'کەم کردنەوە';

  @override
  String get deductions => 'لێبڕینەکان';

  @override
  String get delay => 'دواخستن:';

  @override
  String get delete => 'سڕینەوە';

  @override
  String get deleteEmployee => 'سڕینەوەی کارمەند';

  @override
  String get deleteEmployeeWarning =>
      'ئەم کردارە ناتوانرێت پاشەکشە بکرێت. هەموو داتاکانی کارمەند و زانیارییە پەیوەندیدارەکان لە سیستەمەکە لادەبرێن';

  @override
  String get delivered => 'گەیاندی';

  @override
  String get deadline => 'کاتی کۆتایی';

  @override
  String get department => 'بەش';

  @override
  String get departmentInformation => 'زانیاری بەش';

  @override
  String get departmentName => 'ناوی بەش';

  @override
  String get description => 'وەسف';

  @override
  String get design => 'ديزاين';

  @override
  String get disciplinaryFine => 'غەرامەی دیسپلین';

  @override
  String get document => 'بەڵگەنامە';

  @override
  String get documents => 'بەڵگەنامەکان';

  @override
  String get downloadAll => 'داگرتنی هەموو';

  @override
  String get draft => 'ڕەشنووس';

  @override
  String get drawSignature => 'واژۆ بکێشە';

  @override
  String get dueDate => 'بەرواری کۆتایی هاتنی';

  @override
  String get duration => 'ماوە';

  @override
  String get edit => 'بژارکردن';

  @override
  String get editAccountInfo => 'دەستکاریکردنی زانیاری ئەکاونت';

  @override
  String get editAddress => 'دەستکاری ناونیشان';

  @override
  String get editAnnouncement => 'دەستکاری ڕاگەیاندن';

  @override
  String get editGroup => 'گروپی دەستکاریکردن';

  @override
  String get editInfo => 'دەستکاری زانیاری';

  @override
  String get editMembers => 'دەستکاری ئەندامان';

  @override
  String get editName => 'دەستکاری ناو';

  @override
  String get editPolicies => 'دەستکاری سیاسەتەکان';

  @override
  String get editSalary => 'دەستکاری مووچە';

  @override
  String get email => 'ئیمەیڵ';

  @override
  String get emailAddress => 'ناونیشانی ئیمەیڵ';

  @override
  String get emp => 'ژمارەی کارمەندی';

  @override
  String get employeeAddedSuccessfully => 'کارمەند بە سەرکەوتوویی زیادکرا';

  @override
  String get employeeIdCopySuccessMsg => 'ژمارەی کارمەند کۆپی کراوە بۆ لێدان';

  @override
  String get employeeManagement => 'بەڕێوەبردنی کارمەند';

  @override
  String get employeeOfTheMonth => 'کارمەندی مانگ';

  @override
  String get employeeRatingQuestionTitle =>
      'چۆن هەڵسەنگاندن بۆ ئەم کارمەندە دەکەیت؟';

  @override
  String get employees => 'کارمەندان';

  @override
  String get employeesOfTheMonth => 'کارمەندانی مانگ';

  @override
  String get employeesWorkingToday => 'کارمەندانی ئەمڕۆ کاردەکەن';

  @override
  String get employeeTimeOff => 'کاتی وەستانی کارمەند';

  @override
  String get employeeTracking => 'بەدواداچوونی کارمەند';

  @override
  String get employementType => 'جۆری دامەزراندن';

  @override
  String get endChat => 'کۆتایی هاتنی چات';

  @override
  String get endShift => 'کۆتایی گێڕان';

  @override
  String get endWork => 'کۆتایی هێنان بە کار';

  @override
  String get english => 'ئینگلیزی';

  @override
  String get enterGroupDescription => 'وەسفکردنی گروپەکە دابنێ';

  @override
  String get enterGroupName => 'ناوی گروپ دابنێ';

  @override
  String enterVerificationCode(String phonenumber) {
    return 'تکایە کۆدی پشتڕاستکردنەوە داخڵ بکە کە بۆ $phonenumber نێردراوە';
  }

  @override
  String get enterYourNoteHere => 'لێرەدا تێبینییەکەت بنووسە';

  @override
  String get error => 'هەڵە';

  @override
  String get expenseRequests => 'داواکاری خەرجییەکان';

  @override
  String get expenses => 'خەرجیەکان';

  @override
  String get failedToSelectImage =>
      'شکستی هێنا لە هەڵبژاردنی وێنە. تکایە دووبارە هەوڵبدەرەوە.';

  @override
  String get feedback => 'فیدباک';

  @override
  String get fileSelected => 'فایل هەڵبژێردراوە';

  @override
  String get filter => 'فلتەر';

  @override
  String get firstName => 'ناوی یەکەم';

  @override
  String get fixed => 'جێگیر';

  @override
  String get forgotYourPassword => 'پاسۆردەکەت لەبیر کردووە؟';

  @override
  String get from => 'لە';

  @override
  String get fullAccess => 'دەستڕاگەیشتن بە تەواوی';

  @override
  String get gallery => 'گەلەری';

  @override
  String get generalSettings => 'ڕێکخستنە گشتیەکان';

  @override
  String get generatePayslip => 'لیستی مووچە دروست بکە';

  @override
  String get good => 'باش';

  @override
  String get gross => 'کۆ';

  @override
  String get grossSalary => 'مووچەی گشتی';

  @override
  String get groupDescription => 'وەسفکردنی گروپ';

  @override
  String groupMembersCount(int membercount) {
    return '$membercount ئەندامان';
  }

  @override
  String get groupName => 'ناوی گروپ';

  @override
  String get headquarters => 'بارەگای سەرەکی';

  @override
  String get helloHowCanIHelpYouToday => 'سڵاو! ئەمڕۆ چۆن یارمەتیت بدەم؟';

  @override
  String get helloWorld => 'سڵاو جیهان!';

  @override
  String get high => 'بەرز';

  @override
  String get holidays => 'ڕۆژانی جەژن';

  @override
  String get home => 'ماڵەوە';

  @override
  String get hourly => 'کاتژمێرێک';

  @override
  String get hourlyRate => 'نرخی کاتژمێرێک';

  @override
  String get hours => 'کاتژمێر';

  @override
  String get hrApprovalMessage => 'داواکاریەکەت پەسەند کراوە.';

  @override
  String get hrManager => 'بەڕێوەبەری HR';

  @override
  String get ifYouClockOutNow =>
      'ئەگەر ئێستا کاتژمێر دەرچوویت، ئەوا کاتژمێر دەردەچیت لە';

  @override
  String get increase => 'زیادکردن';

  @override
  String get inProgress => 'لە پێشکەوتندایە';

  @override
  String get iqd => 'IQD';

  @override
  String get iraq => 'عێراق';

  @override
  String get jobInformation => 'زانیاری کار';

  @override
  String get jobTitle => 'ناونیشانی کار';

  @override
  String get joined => 'بەشداری کردووە';

  @override
  String get language => 'زمان';

  @override
  String get lastName => 'ناوی کۆتایی';

  @override
  String lastSeenOn(String dateTime) {
    return 'دوایین جار بینراوە لە $dateTime';
  }

  @override
  String lastSeenOnLastseen(Object lastSeen) {
    return 'دوایین بینین لە $lastSeen';
  }

  @override
  String get lastWorkingDay => 'دوایین ڕۆژی کارکردن';

  @override
  String get leave => 'پشوو';

  @override
  String get leaveFrom => 'جێبهێڵە لە';

  @override
  String get leaveManagement => 'بەڕێوەبردنی مۆڵەت';

  @override
  String get leavesHistory => 'مێژوو بەجێدەهێڵێت';

  @override
  String get leavesTaken => 'گەڵا وەرگیراوە';

  @override
  String get leaveTo => 'جێبهێڵە بۆ';

  @override
  String get leaveType => 'جۆری جێهێشتن';

  @override
  String get light => 'ڕووناکی';

  @override
  String get location => 'شوێن';

  @override
  String get logIn => 'چوونەژوورەوە';

  @override
  String get logo => 'لۆگۆ';

  @override
  String get logout => 'چوونە دەرەوە';

  @override
  String get logOut => 'چوونە دەرەوە';

  @override
  String get logoutConfirmation => 'ئایا دڵنیای کە دەتەوێت بچیتە دەرەوە؟';

  @override
  String get low => 'نزم';

  @override
  String get manageDepartment => 'بەشی بەڕێوەبردن';

  @override
  String get manageEmployees => 'بەڕێوەبردنی کارمەندان';

  @override
  String get management => 'بەڕێوەبردن';

  @override
  String get manageOffices => 'بەڕێوەبردنی ئۆفیسەکان';

  @override
  String get manageProject => 'بەڕێوەبردنی پرۆژە';

  @override
  String get manager => 'بەڕێوەبەر';

  @override
  String get managerInformation => 'زانیاری بەڕێوەبەر';

  @override
  String get managersFeedback => 'فیدباکی بەڕێوەبەر';

  @override
  String get managersOnly => 'تەنها بەڕێوەبەران';

  @override
  String get manageSalaries => 'بەڕێوەبردنی مووچە';

  @override
  String get manageYourEmployees => 'بەڕێوەبردنی کارمەندەکانت';

  @override
  String get manualAdditions => 'زیادکردنی دەستی';

  @override
  String get manualDeductions => 'لێبڕینەکانی دەستی';

  @override
  String maxFileSizeInMB(int sizeInMb) {
    return '(زۆرترین قەبارەی پەڕگە: $sizeInMb مێگابایت)';
  }

  @override
  String maxFileImageVideoSize(int sizeinmb) {
    return '(زۆرترین قەبارەی پەڕگە /وێنە / ڤیدیۆ: $sizeinmb مێگابایت)';
  }

  @override
  String get medium => 'ناوەند';

  @override
  String get members => 'ئەندامان';

  @override
  String membersSelectedOfTotal(int selected, int total) {
    return 'ئەندامان: $selected لە $total';
  }

  @override
  String get middleName => 'ناوی ناوەڕاست';

  @override
  String get month => 'مانگ';

  @override
  String get monthly => 'مانگانە';

  @override
  String get myLocation => 'شوێنی من';

  @override
  String get myTasks => 'ئەرکەکانی من';

  @override
  String get name => 'ناو';

  @override
  String nameClockedOutTime(String name) {
    return '$name کاتژمێرێک لەمەوبەر کاتژمێرەکە دەرچوو';
  }

  @override
  String nameCreatedGroup(String name) {
    return '$name گروپێکی دروستکراوە';
  }

  @override
  String get nationalId => 'ناسنامەی نیشتمانی';

  @override
  String get nearbyPlaces => 'شوێنەکانی نزیک';

  @override
  String get needHelp => 'پێویستی بە یارمەتی هەیە';

  @override
  String get netSalary => 'مووچەی پاک';

  @override
  String get newChannel => 'کەناڵی نوێ';

  @override
  String get newChannelFunctionality => 'کارایی کەناڵی نوێ جێبەجێ دەکرێت';

  @override
  String get newGroup => 'گروپی نوێ';

  @override
  String get newPassword => 'وشەی نهێنی نوێ';

  @override
  String get news => 'هەواڵەکان';

  @override
  String get next => 'داهاتوو';

  @override
  String get no => 'نەخێر';

  @override
  String get noContactsFound => 'هیچ پەیوەندییەک نەدۆزراوەتەوە';

  @override
  String get noData => 'هیچ زانیارییەک لەبەردەستدا نییە';

  @override
  String get noIssues => 'هیچ پرسێک نییە';

  @override
  String get noProjectFound => 'هیچ پڕۆژەیەک نەدۆزراوەتەوە';

  @override
  String get noTasksFound => 'هیچ ئەرکێک بۆ ئەم پڕۆژەیە نەدۆزراوەتەوە';

  @override
  String get notAvailable => 'بەردەست نییە';

  @override
  String get notEditable => 'ناتوانرێت دەستکاری بکرێت';

  @override
  String get notes => 'تێبینی';

  @override
  String get notification => 'ئاگادارکردنەوە';

  @override
  String get notifications => 'ئاگادارکردنەوەکان';

  @override
  String get numberOfEmployees => 'ژمارەی فەرمانبەران';

  @override
  String get officeAddress => 'ناونیشانی ئۆفیس';

  @override
  String get officeContactInformation => 'زانیاری پەیوەندی ئۆفیس';

  @override
  String get officeDetails => 'وردەکاری ئۆفیس';

  @override
  String get officeEmployees => 'کارمەندانی فەرمانگە';

  @override
  String get officeInformation => 'زانیاری ئۆفیس';

  @override
  String get officeLocation => 'شوێنی ئۆفیس';

  @override
  String get officeLocationDescription => 'وەسفکردنی شوێنی ئۆفیس';

  @override
  String get officeName => 'ناوی ئۆفیس';

  @override
  String officeOfficeName(String officename) {
    return 'ئۆفیس: $officename';
  }

  @override
  String get offices => 'فەرمانگەکان';

  @override
  String get officeType => 'جۆری ئۆفیس';

  @override
  String get ok => 'باشە';

  @override
  String get online => 'سەرهێڵ';

  @override
  String get operatingHours => 'کاتی کارکردن';

  @override
  String get optionalNote => 'تێبینی ئیختیاری';

  @override
  String get other => 'ئی تر';

  @override
  String get otherDocuments => 'بەڵگەنامەکانی تر';

  @override
  String get otherInfo => 'زانیاری تر';

  @override
  String overtimeHours(int hours) {
    return '$hours کاتژمێرەکان لەم مانگەدا';
  }

  @override
  String get overtimePay => 'پارەی کاتەکانی زیادە';

  @override
  String get overview => 'تێڕوانینێکی گشتی';

  @override
  String get paid => 'پارەی دا';

  @override
  String get password => 'نهێنوشە';

  @override
  String get payForThisPeriod => 'پارە بدە بۆ ئەم ماوەیە';

  @override
  String get paymentStatus => 'دۆخی پارەدان';

  @override
  String get payNow => 'ئێستا پارە بدە';

  @override
  String get payrollSummary => 'پوختەی مووچە';

  @override
  String payslipConfirmationWarning(Object name) {
    return 'بە کرتەکردن لەسەر ناردن، ئەم پارەیە دەنێردرێت بۆ $name';
  }

  @override
  String get pending => 'هەڵپەسێردراو';

  @override
  String get pendingLeaveRequests => 'داواکاری مۆڵەتی چاوەڕوانکراو';

  @override
  String get pendingRequest => 'داواکاری چاوەڕوانکراو';

  @override
  String get pendingRequests => 'داواکاری چاوەڕوانکراو';

  @override
  String get performance => 'ئەدا';

  @override
  String get performanceAndAchievements => 'ئەنجامدان و دەستکەوتەکان';

  @override
  String get performanceBonus => 'بۆنوسی ئەدای کارکردن';

  @override
  String get permissionsDepartment => 'بەشی مۆڵەتەکان';

  @override
  String get personalInformation => 'زانیاری کەسی';

  @override
  String get phone => 'تەلەفۆن';

  @override
  String get phoneNumber => 'ژمارەی تەلەفۆن';

  @override
  String get phoneNumberOrEmailAddress => 'ژمارەی تەلەفۆن یان ناونیشانی ئیمەیڵ';

  @override
  String get pleaseCreateAProject =>
      'تکایە سەرەتا پرۆژەیەک دروست بکە بۆ زیادکردنی ئەرکەکان';

  @override
  String get pleaseCreateATask => 'تکایە ئەرکێک دروست بکە';

  @override
  String get pleaseDrawSignature => 'تکایە واژۆی خۆت بکێشە';

  @override
  String get pleaseEnterAGroupName => 'تکایە ناوی گروپێک بنووسە';

  @override
  String get policy => 'سیاسەت';

  @override
  String get policyDescription => 'وەسفکردنی سیاسەت';

  @override
  String get policyName => 'ناوی سیاسەت';

  @override
  String get poor => 'هەژار';

  @override
  String get previousSalaries => 'مووچەی پێشوو';

  @override
  String get priority => 'ئەولەویەت';

  @override
  String get proceedConfirmation => 'ئایا دڵنیای کە دەتەوێت بەردەوام بیت؟';

  @override
  String get profile => 'پرۆفایل';

  @override
  String get project => 'پرۆژە';

  @override
  String get projects => 'پڕۆژەکان';

  @override
  String get promoteConfirmationTitle =>
      'دڵنیای کە دەتەوێت ئەم کارمەندە بەرز بکەیتەوە بۆ پۆستە نوێیەکە؟';

  @override
  String get promotion => 'بەرزکردنەوە';

  @override
  String get publish => 'بڵاوکردنەوە';

  @override
  String publishedByNAME(String name) {
    return 'بڵاوکراوەتەوە لەلایەن $name';
  }

  @override
  String get read => 'خوێندنەوە';

  @override
  String get reasonForResignation => 'هۆکاری دەستلەکارکێشانەوە';

  @override
  String get recentlyViewed => 'بەم دواییە بینراوە';

  @override
  String get record => 'تۆمار';

  @override
  String get reject => 'ڕەتکردنەوە';

  @override
  String get rejected => 'ڕەتکرایەوە';

  @override
  String get remainingLeaves => 'گەڵاکانی ماوەتەوە';

  @override
  String get rememberMe => 'لەبیرم بێت';

  @override
  String get reportAProblem => 'ڕاپۆرتی کێشەیەک بکە';

  @override
  String get requestedEdit => 'داواکراوی دەستکاریکردن';

  @override
  String get requestForExpenses => 'داواکاری بۆ خەرجییەکان';

  @override
  String get requestForLeave => 'داواکاری مۆڵەت';

  @override
  String get requestSentSuccessfully => 'داواکاری بە سەرکەوتوویی نێردراوە';

  @override
  String get requestSubmittedSuccessfully =>
      'داواکاریەکەت بە سەرکەوتوویی پێشکەش کراوە';

  @override
  String get requestToChatWithHr => 'داواکاری بۆ چاتکردن لەگەڵ HR';

  @override
  String get resendCode => 'کۆدی دووبارە ناردنەوە';

  @override
  String get resignation => 'دەستلەکارکێشانەوە';

  @override
  String get resignationRequest => 'داواکاری دەستلەکارکێشانەوە';

  @override
  String get resigned => 'دەستی لەکارکێشایەوە';

  @override
  String get restaurant => 'چێشتخانە';

  @override
  String get retry => 'دووبارە هەوڵبدەرەوە';

  @override
  String get review => 'پێداچوونەوە';

  @override
  String get reward => 'خەڵات';

  @override
  String get role => 'ئەرک';

  @override
  String get salary => 'مووچە';

  @override
  String get salaryDetails => 'وردەکاری مووچە';

  @override
  String get salaryHistory => 'مێژووی مووچە';

  @override
  String get salaryManagement => 'بەڕێوەبردنی مووچە';

  @override
  String get salaryPaymentConfirmation => 'پشتڕاستکردنەوەی پێدانی مووچە';

  @override
  String get save => 'هەڵگرتن';

  @override
  String get savedSuccessfully => 'بە سەرکەوتوویی پاشەکەوت کراوە';

  @override
  String get saveSend => 'پاشەکەوتکردن و ناردن';

  @override
  String get schedule => 'خشتە';

  @override
  String get schedules => 'خشتەکان';

  @override
  String get search => 'گەڕان';

  @override
  String get searchForATask => 'گەڕان بەدوای ئەرکێکدا';

  @override
  String get searching => 'گەڕان';

  @override
  String get searchingForATask => 'گەڕان بەدوای ئەرکێکدا';

  @override
  String get selectAudience => 'ئامادەبووان هەڵبژێرە';

  @override
  String get selectClosingTime => 'کاتی داخستنی هەڵبژێرە';

  @override
  String get selectContacts => 'پەیوەندییەکان هەڵبژێرە';

  @override
  String get selectDocument => 'بەڵگەنامەی هەڵبژێرە';

  @override
  String get selectDocuments => 'بەڵگەنامەکان هەڵبژێرە';

  @override
  String get selectOpeningTime => 'کاتی کردنەوەی هەڵبژێرە';

  @override
  String get selectTimeFrom => 'کات لە هەڵبژێرە';

  @override
  String get selectTimeTo => 'کات بۆ هەڵبژێرە';

  @override
  String get send => 'ناردن';

  @override
  String get sendShiftChangeRequest => 'داواکاری گۆڕینی شیفت بنێرە';

  @override
  String get sendYourCurrentLocation => 'شوێنی ئێستات بنێرە';

  @override
  String get sendYourLiveLocation => 'شوێنی لایڤەکەت بنێرە';

  @override
  String get settings => 'ڕێکخستنەکان';

  @override
  String get shiftDetails => 'وردەکارییەکانی شۆفت';

  @override
  String get shiftEdit => 'دەستکاریکردنی شیفت';

  @override
  String get shiftEditRequests => 'داواکاری دەستکاریکردنی گۆڕانکاری';

  @override
  String get shiftHistory => 'مێژووی شۆفت';

  @override
  String get shiftWorkingTime => 'کاتی کارکردنی شۆفت';

  @override
  String get showAll => 'هەموویان پیشان بدە';

  @override
  String get sinceStartOfYear => 'لە سەرەتای ساڵەوە';

  @override
  String get someErrorOcurred => 'هەندێک هەڵە ڕوویدا';

  @override
  String get sorrySomeErrorOccured => 'ببورە، هەندێک هەڵە ڕوویدا';

  @override
  String get startBreak => 'دەستپێکردنی پشوو';

  @override
  String get startDate => 'بەرواری دەستپێکردن';

  @override
  String get startWork => 'دەست بە کار بکە';

  @override
  String get status => 'دۆخ';

  @override
  String get statusUpdate => 'نوێکردنەوەی دۆخ';

  @override
  String get submit => 'پێشکەشکردن';

  @override
  String get submitResignation => 'پێشکەشکردنی دەستلەکارکێشانەوە';

  @override
  String get success => 'سەرکەوتن';

  @override
  String get successfullyCompletedShift =>
      'تۆ بە سەرکەوتوویی شۆفتەکەت تەواو کردووە.';

  @override
  String get successfullyPaid => 'بە سەرکەوتوویی پارەی پێدراوە';

  @override
  String get sundayToFriday => 'یەکشەممە تا هەینی';

  @override
  String get support => 'پشتیوانی';

  @override
  String get systemGeneratedAdditions => 'زیادکردنەکانی دروستکراوی سیستەم';

  @override
  String get systemGeneratedDeductions => 'لێبڕینەکانی دروستکراوی سیستەم';

  @override
  String get taskAddedSuccessfully => 'ئەرک زیادکرا بە سەرکەوتوویی';

  @override
  String get taskCompleted => 'ئەرک تەواو بووە';

  @override
  String get taskDescription => 'وەسفکردنی ئەرک';

  @override
  String get taskDetails => 'وردەکاری ئەرکەکان';

  @override
  String get tasks => 'ئەرکەکان';

  @override
  String get taxes => 'باجەکان';

  @override
  String get team => 'تیم';

  @override
  String get teams => 'تیمەکان';

  @override
  String get termsOfUse => 'مەرج و ڕێساکان';

  @override
  String get theme => 'مەبەستی سەرەکی';

  @override
  String get themeMode => 'دۆخی تەوەر';

  @override
  String get theNewPosition => 'هەڵوێستی نوێ';

  @override
  String get theNewSalary => 'مووچەی نوێ';

  @override
  String get thereAreNoNotifications => 'هیچ ئاگادارکردنەوەیەک نییە';

  @override
  String get theReason => 'هۆکارەکە';

  @override
  String get thisMonth => 'ئەم مانگە';

  @override
  String get thisMonthFilter => 'ئەم مانگە';

  @override
  String get thisMonthsPayroll => 'مووچەی ئەم مانگە';

  @override
  String get thisMonthsSalary => 'مووچەی ئەم مانگە';

  @override
  String get thisMonthsSalaryDetails => 'وردەکاری مووچەی ئەم مانگە';

  @override
  String get thisWeek => 'ئەم هەفتەیە';

  @override
  String get timeClock => 'کاتژمێری کات';

  @override
  String get timeOffRequests => 'داواکاری مۆڵەت';

  @override
  String get timeOffTaken => 'گەڵا وەرگیراوە';

  @override
  String get timesheet => 'تایمشیت';

  @override
  String get timeZone => 'ناوچەی کات';

  @override
  String get timingsError => 'کاتەکان بە هەڵە دەردەکەون';

  @override
  String get title => 'ناونیشان';

  @override
  String get to => 'بۆ';

  @override
  String get today => 'ئەمڕۆ';

  @override
  String get totalAdditions => 'کۆی گشتی زیادکردنەکان';

  @override
  String get totalDeductions => 'کۆی گشتی لێبڕینەکان';

  @override
  String get totalEmployees => 'کۆی گشتی کارمەندان';

  @override
  String get totalHours => 'کۆی گشتی کاتژمێرەکان';

  @override
  String get totalShiftTime => 'کۆی گشتی کاتی شیفت';

  @override
  String get typing => 'تایپکردن...';

  @override
  String get unavailable => 'بەردەست نییە';

  @override
  String get unionFees => 'کرێی یەکێتی';

  @override
  String get unpaid => 'بێ مووچە';

  @override
  String get unread => 'نەخوێنراوەتەوە';

  @override
  String get updatedSuccessfully => 'بە سەرکەوتوویی نوێ کراوەتەوە';

  @override
  String get updatePolicy => 'سیاسەتی نوێکردنەوە';

  @override
  String get uploadDocuments => 'بارکردنی بەڵگەنامەکان';

  @override
  String valueHours(num hours) {
    return '$hours کاتژمێر';
  }

  @override
  String get verificationCode =>
      'تکایە ئەو کۆدەی پشتڕاستکردنەوە داخڵ بکە کە بۆ...';

  @override
  String get viewActivity => 'بینینی چالاکیی';

  @override
  String get viewAll => 'بینینی هەموو';

  @override
  String get waitingForManagerApproval => 'چاوەڕوانی ڕەزامەندی بەڕێوەبەرە';

  @override
  String get website => 'ماڵپەڕ';

  @override
  String get weekly => 'هەفتانە';

  @override
  String get welcome => 'بەخێربێیت';

  @override
  String get workDayYesNo => 'ڕۆژی کارکردن (بەڵێ/نەخێر)';

  @override
  String get workingHours => 'کاتی کارکردن';

  @override
  String get workingSchedule => 'خشتەی کارکردن';

  @override
  String get workspace => 'شوێنی کارکردن';

  @override
  String get writeAMessage => 'پەیامێک بنووسە...';

  @override
  String get writeANoteForTheEmployee => 'تێبینییەک بۆ فەرمانبەرەکە بنووسە';

  @override
  String get writeFeedback => 'فیدباک بنووسە';

  @override
  String get year => 'ساڵ';

  @override
  String get yearly => 'ساڵانە';

  @override
  String get yes => 'بەڵێ';

  @override
  String get yesterday => 'دوێنێ';

  @override
  String get you => 'تۆ';

  @override
  String youCreatedGroup(String name) {
    return '$name ئەم گروپەی دروست کردووە';
  }

  @override
  String get zipCode => 'کۆدی زیپ';

  @override
  String get kurdish => 'کوردی';

  @override
  String get salaries => 'مووچەی فەرمانبەران';

  @override
  String get overtimeHoursLabel => 'کاتژمێری زیادە';

  @override
  String hoursForToday(Object hours) {
    return '$hours کاتژمێر بۆ ئەمڕۆ';
  }

  @override
  String get amountIqd => 'بڕی د.ع';

  @override
  String get am => 'ص.';

  @override
  String get pm => 'م.';

  @override
  String get january => 'کانونی دووەم';

  @override
  String get february => 'شوبات';

  @override
  String get march => 'ڕێپێوان';

  @override
  String get april => 'نیسان';

  @override
  String get may => 'ئایار';

  @override
  String get june => 'حوزەیران';

  @override
  String get july => 'تەموز';

  @override
  String get august => 'ئاب';

  @override
  String get september => 'ئەیلول';

  @override
  String get october => 'تشرینی یەکەم';

  @override
  String get november => 'تشرینی دووەم';

  @override
  String get december => 'کانونی یەکەم';

  @override
  String get halfDay => 'نیو ڕۆژ';

  @override
  String get jan => 'کانون 2';

  @override
  String get feb => 'شوبات';

  @override
  String get mar => 'ئازار';

  @override
  String get apr => 'نیسان';

  @override
  String get jun => 'حوزەیران';

  @override
  String get jul => 'تەمووز';

  @override
  String get aug => 'ئاب';

  @override
  String get sep => 'ئەیلوول';

  @override
  String get oct => 'تشرینی 1';

  @override
  String get nov => 'تشرینی 2';

  @override
  String get dec => 'کانون 1';

  @override
  String hoursLetter(Object hours) {
    return '$hours س';
  }

  @override
  String get approved => 'پەسەندکراو';

  @override
  String get failed => 'شکستی هێنا';

  @override
  String sizeKB(Object size) {
    return '$size KB';
  }

  @override
  String sizeMB(Object size) {
    return '$size MB';
  }

  @override
  String get salaryEdit => 'دەستکاریکردنی مووچە';

  @override
  String get gender => 'زایەند';

  @override
  String get male => 'نێر';

  @override
  String get female => 'مێ';

  @override
  String get canView => 'دەتوانێت ببینێت';

  @override
  String get canEdit => 'دەتوانێت دەستکاری بکات';

  @override
  String singleFileLargeError(Object size) {
    return 'فایلە هەڵبژێردراوەکە زۆر گەورەیە (زۆرترین ${size}MB).';
  }

  @override
  String multiFileLargeError(Object count) {
    return '$count فایلەکان زۆر گەورە بوون و زیاد نەکرابوون.';
  }

  @override
  String get sendForApproval => 'بۆ پەسەندکردن بنێرن';

  @override
  String noShiftOnDate(Object date) {
    return 'هیچ ئامادەبوونێک تۆمار نەکراوە $date';
  }

  @override
  String get policies => 'سیاسەتەکان';

  @override
  String get pickerTakePhoto => 'وێنە بگرە';

  @override
  String get pickerRecordVideo => 'تۆمارکردنی ڤیدیۆ';

  @override
  String get pickerChooseFromGallery => 'لە گەلەری هەڵبژێرە';

  @override
  String get pickerChooseFile => 'فایلێک هەڵبژێرە';

  @override
  String get payslipSuccessNotification =>
      'نووسراوی مووچە بە سەرکەوتوویی بۆ فەرمانبەرەکە نێردراوە.';

  @override
  String get imageOrVideo => 'وێنە / ڤیدیۆ';

  @override
  String paymentMadeOnDate(Object date) {
    return 'پارەدان لە $date ئەنجامدراوە';
  }

  @override
  String get didNotWorkOnThisDay => 'لەم ڕۆژەدا کاری نەکردووە';

  @override
  String get publishedBy => 'بڵاوکراوەتەوە لەلایەن';

  @override
  String get hrHasApprovedChat => 'HR ڕەزامەندی چاتی داوە';

  @override
  String get endBreak => 'کۆتایی پشوو';

  @override
  String get noShiftRecordedOn => 'هیچ شۆفتێک تۆمار نەکراوە لە';

  @override
  String get h => 'ک';

  @override
  String get m => 'خ';
}
