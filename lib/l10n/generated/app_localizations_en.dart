// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get about => 'About';

  @override
  String get aboutUs => 'About Us';

  @override
  String get absenceDeduction => 'Absence Deduction';

  @override
  String get accountInfo => 'Account Information';

  @override
  String get active => 'Active';

  @override
  String get activity => 'Activity';

  @override
  String get add => 'Add';

  @override
  String get addAComment => 'Add a Comment';

  @override
  String get addAnnouncement => 'Add Announcement';

  @override
  String get addATask => 'Add a Task';

  @override
  String get addBreak => 'Add Break';

  @override
  String get addComment => 'Add Comment';

  @override
  String get addDepartment => 'Add Department';

  @override
  String get addedSuccessfully => 'Added Successfully';

  @override
  String get addEmployee => 'Add Employee';

  @override
  String get additionalNotes => 'Additional Notes';

  @override
  String get addLeave => 'Add Leave';

  @override
  String get addNewOffice => 'Add New Office';

  @override
  String get addNote => 'Add a Note';

  @override
  String get addPolicy => 'Add Policy';

  @override
  String get address => 'Address';

  @override
  String get addressInformation => 'Address Information';

  @override
  String get addShift => 'Add Shift';

  @override
  String get addTask => 'Add Task';

  @override
  String get adminContactMessage =>
      'If You Wish to Edit, Please Contact The Administration';

  @override
  String get ai => 'AI';

  @override
  String get aiCapablityLine1 => 'Would You Like Me to Organise a Task?';

  @override
  String get aiCapablityLine2 => 'Look Up An Employee’s Information?';

  @override
  String get aiCapablityLine3 => 'Or Update Something For You?';

  @override
  String get aiCapablityLine4 =>
      'Just Tell Me What You Need, I Am Here To Help!';

  @override
  String get aiChatGreeting => 'Hello and Welcome to Ako Basma AI!';

  @override
  String get aiGreetingText => 'How Can I Help You Today?';

  @override
  String get akoBasma => 'Ako Basma';

  @override
  String get all => 'All';

  @override
  String get allDay => 'All Day';

  @override
  String get allEmployees => 'All Employees';

  @override
  String get allOffices => 'All Offices';

  @override
  String get allowances => 'Allowances';

  @override
  String get allProjects => 'All Projects';

  @override
  String get allTime => 'All Time';

  @override
  String get amount => 'Amount';

  @override
  String get amountOfDecrease => 'Amount of Decrease';

  @override
  String get amountOfIncrease => 'Amount of Increase';

  @override
  String get amountOfIncreaseOrDecrease => 'Amount of Increase or Decrease';

  @override
  String get announcement => 'Announcement';

  @override
  String get approve => 'Approve';

  @override
  String get approvedBy => 'Approved By';

  @override
  String get approveTask => 'Approve Task';

  @override
  String approximateToDistanceMetres(Object distance) {
    return 'Approximately $distance Metres Away';
  }

  @override
  String get arabic => 'Arabic';

  @override
  String get archive => 'Archive';

  @override
  String get archiveEmployee => 'Archive Employee';

  @override
  String get archiveEmployeeWarning =>
      'If The Employee Is Archived, Their Account Will Be Deactivated!';

  @override
  String get archiveEmployeeWarningSubtitle =>
      'ARE YOU SURE YOU WANT TO ARCHIVE THIS EMPLOYEE?';

  @override
  String get archives => 'Archives';

  @override
  String get asiaBaghdad => 'Asia/Baghdad';

  @override
  String get assign => 'Assign';

  @override
  String get assignee => 'Assignee';

  @override
  String get assignToMe => 'Assign to Me';

  @override
  String get attachment => 'Attachment';

  @override
  String get attendance => 'Attendance';

  @override
  String get availableOnWeekends => 'Available on Weekends';

  @override
  String get back => 'Back';

  @override
  String get backlog => 'Backlog';

  @override
  String get basicSalary => 'Basic Salary';

  @override
  String get branch => 'Branch';

  @override
  String get breakTime => 'Break Time:';

  @override
  String get breakTimeLabel => 'Break Time';

  @override
  String get call => 'Call';

  @override
  String get camera => 'Camera';

  @override
  String get cancel => 'Cancel';

  @override
  String get canIGetMoreInfo => 'Can I Get More Information?';

  @override
  String get changesSavedSuccessfully => 'Changes Saved Successfully';

  @override
  String get chat => 'Messages';

  @override
  String get chatRequestConfirmation =>
      'ARE YOU SURE YOU WANT TO SEND THIS REQUEST?';

  @override
  String get chatWithAi => 'Chat with AI';

  @override
  String get chatWithHr => 'Chat with HR';

  @override
  String get checkedOutHelpText => 'Checked Out';

  @override
  String get checkInTime => 'Check-In Time';

  @override
  String get checkOutTime => 'Check-Out Time';

  @override
  String get checkYourMessages => 'Check your messages';

  @override
  String get city => 'City';

  @override
  String get clear => 'Clear';

  @override
  String get clearSelection => 'Clear Selection';

  @override
  String get clickToUpload => 'Click to Upload';

  @override
  String get clockIn => 'Clock In';

  @override
  String get clockInTime => 'Clock-In Time';

  @override
  String get clockOut => 'Clock Out';

  @override
  String get clockOutTime => 'Clock-Out Time';

  @override
  String get close => 'Close';

  @override
  String get closingTimeMustBeAfterOpeningTime =>
      'Closing Time Must be After Opening Time.';

  @override
  String get comments => 'Comments';

  @override
  String get companyDirector => 'Company Director';

  @override
  String get companyInformation => 'Company Information';

  @override
  String get companyJobNumberStart => 'Company Employee Number Sequence';

  @override
  String get companyName => 'Company Name';

  @override
  String get companyNews => 'Company News';

  @override
  String get companyPolicy => 'Company Policy';

  @override
  String get completed => 'Completed';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get confirm => 'Confirm';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get confirmHours => 'Confirm Hours';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get contact => 'Contacts';

  @override
  String get contactNumber => 'Contact Number';

  @override
  String get contractType => 'Contract Type';

  @override
  String get country => 'Country';

  @override
  String get create => 'Create';

  @override
  String get createAProject => 'Create a Project';

  @override
  String get createATask => 'Create a Task';

  @override
  String get createProject => 'Create Project';

  @override
  String get createTask => 'Create Task';

  @override
  String get critical => 'Critical';

  @override
  String get currency => 'Currency';

  @override
  String get currentShift => 'Current Shift';

  @override
  String get customDateRange => 'Custom Date Range';

  @override
  String get daily => 'Daily';

  @override
  String get dailyShifts => 'Daily Shifts';

  @override
  String get dark => 'Dark';

  @override
  String get date => 'Date';

  @override
  String get dateOfBirth => 'Date of Birth';

  @override
  String get dateOfJoining => 'Date of Joining';

  @override
  String get dateOfResignation => 'Date of Resignation';

  @override
  String get days => 'Days';

  @override
  String get decrease => 'Decrease';

  @override
  String get deductions => 'Deductions';

  @override
  String get delay => 'Delay:';

  @override
  String get delete => 'Delete';

  @override
  String get deleteEmployee => 'Delete Employee';

  @override
  String get deleteEmployeeWarning =>
      'This Action Cannot be Undone. All Employee Data and Related Information Will be Removed From The System';

  @override
  String get delivered => 'Delivered';

  @override
  String get deadline => 'Deadline';

  @override
  String get department => 'Department';

  @override
  String get departmentInformation => 'Department Information';

  @override
  String get departmentName => 'Department Name';

  @override
  String get description => 'Description';

  @override
  String get design => 'Design';

  @override
  String get disciplinaryFine => 'Disciplinary Fine';

  @override
  String get document => 'Document';

  @override
  String get documents => 'Documents';

  @override
  String get downloadAll => 'Download All';

  @override
  String get draft => 'Draft';

  @override
  String get drawSignature => 'Draw Signature';

  @override
  String get dueDate => 'Due Date';

  @override
  String get duration => 'Duration';

  @override
  String get edit => 'Edit';

  @override
  String get editAccountInfo => 'Edit Account Information';

  @override
  String get editAddress => 'Edit Address';

  @override
  String get editAnnouncement => 'Edit Announcement';

  @override
  String get editGroup => 'Edit Group';

  @override
  String get editInfo => 'Edit Info';

  @override
  String get editMembers => 'Edit Members';

  @override
  String get editName => 'Edit Name';

  @override
  String get editPolicies => 'Edit Policies';

  @override
  String get editSalary => 'Edit Salary';

  @override
  String get email => 'Email';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get emp => 'Employee No';

  @override
  String get employeeAddedSuccessfully => 'Employee Added Successfully';

  @override
  String get employeeIdCopySuccessMsg => 'Employee No Copied to Clipboard';

  @override
  String get employeeManagement => 'Employee Management';

  @override
  String get employeeOfTheMonth => 'Employee of The Month';

  @override
  String get employeeRatingQuestionTitle => 'How Do You Rate This Employee?';

  @override
  String get employees => 'Employees';

  @override
  String get employeesOfTheMonth => 'Employees of The Month';

  @override
  String get employeesWorkingToday => 'Employees Working Today';

  @override
  String get employeeTimeOff => 'Employee Time-Off';

  @override
  String get employeeTracking => 'Employee Tracking';

  @override
  String get employementType => 'Employment Type';

  @override
  String get endChat => 'End Chat';

  @override
  String get endShift => 'End Shift';

  @override
  String get endWork => 'End Work';

  @override
  String get english => 'English';

  @override
  String get enterGroupDescription => 'Enter Group Description';

  @override
  String get enterGroupName => 'Enter Group Name';

  @override
  String enterVerificationCode(String phonenumber) {
    return 'Please Enter The Verification Code Sent To $phonenumber';
  }

  @override
  String get enterYourNoteHere => 'Enter Your Note Here';

  @override
  String get error => 'Error';

  @override
  String get expenseRequests => 'Expense Requests';

  @override
  String get expenses => 'Expenses';

  @override
  String get failedToSelectImage => 'Failed to Select Image, Please Try Again.';

  @override
  String get feedback => 'Feedback';

  @override
  String get fileSelected => 'File Selected';

  @override
  String get filter => 'Filter';

  @override
  String get firstName => 'First Name';

  @override
  String get fixed => 'Fixed';

  @override
  String get forgotYourPassword => 'Forgot Your Password?';

  @override
  String get from => 'From';

  @override
  String get fullAccess => 'Full Access';

  @override
  String get gallery => 'Gallery';

  @override
  String get generalSettings => 'General Settings';

  @override
  String get generatePayslip => 'Generate Payslip';

  @override
  String get good => 'Good';

  @override
  String get gross => 'Gross';

  @override
  String get grossSalary => 'Gross Salary';

  @override
  String get groupDescription => 'Group Description';

  @override
  String groupMembersCount(int membercount) {
    return '$membercount Members';
  }

  @override
  String get groupName => 'Group Name';

  @override
  String get headquarters => 'Headquarters';

  @override
  String get helloHowCanIHelpYouToday => 'Hello! How Can I Help You Today?';

  @override
  String get helloWorld => 'Hello World!';

  @override
  String get high => 'High';

  @override
  String get holidays => 'Holidays';

  @override
  String get home => 'Home';

  @override
  String get hourly => 'Hourly';

  @override
  String get hourlyRate => 'Hourly Rate';

  @override
  String get hours => 'Hours';

  @override
  String get hrApprovalMessage => 'Your Request Has Been Approved.';

  @override
  String get hrManager => 'HR Manager';

  @override
  String get ifYouClockOutNow =>
      'If You Clock Out Now, You\'ll be Clock Out at';

  @override
  String get increase => 'Increase';

  @override
  String get inProgress => 'In Progress';

  @override
  String get iqd => 'IQD';

  @override
  String get iraq => 'Iraq';

  @override
  String get jobInformation => 'Job Information';

  @override
  String get jobTitle => 'Job Title';

  @override
  String get joined => 'Joined';

  @override
  String get language => 'Language';

  @override
  String get lastName => 'Last Name';

  @override
  String lastSeenOn(String dateTime) {
    return 'Last Seen on $dateTime';
  }

  @override
  String lastSeenOnLastseen(Object lastSeen) {
    return 'Last Seen on $lastSeen';
  }

  @override
  String get lastWorkingDay => 'Last Working Day';

  @override
  String get leave => 'Leave';

  @override
  String get leaveFrom => 'Leave From';

  @override
  String get leaveManagement => 'Leave Management';

  @override
  String get leavesHistory => 'Leaves History';

  @override
  String get leavesTaken => 'Leaves Taken';

  @override
  String get leaveTo => 'Leave To';

  @override
  String get leaveType => 'Leave Type';

  @override
  String get light => 'Light';

  @override
  String get location => 'Location';

  @override
  String get logIn => 'Log In';

  @override
  String get logo => 'Logo';

  @override
  String get logout => 'Log Out';

  @override
  String get logOut => 'Log Out';

  @override
  String get logoutConfirmation => 'Are You Sure You Want to Log Out?';

  @override
  String get low => 'Low';

  @override
  String get manageDepartment => 'Manage Department';

  @override
  String get manageEmployees => 'Manage Employees';

  @override
  String get management => 'Management';

  @override
  String get manageOffices => 'Manage Offices';

  @override
  String get manageProject => 'Manage Project';

  @override
  String get manager => 'Manager';

  @override
  String get managerInformation => 'Manager Information';

  @override
  String get managersFeedback => 'Manager\'s Feedback';

  @override
  String get managersOnly => 'Managers Only';

  @override
  String get manageSalaries => 'Manage Salaries';

  @override
  String get manageYourEmployees => 'Manage Your Employees';

  @override
  String get manualAdditions => 'Manual Additions';

  @override
  String get manualDeductions => 'Manual Deductions';

  @override
  String maxFileSizeInMB(int sizeInMb) {
    return '(Max. File size: $sizeInMb MB)';
  }

  @override
  String maxFileImageVideoSize(int sizeinmb) {
    return '(Max. File /Image / Video size: $sizeinmb MB)';
  }

  @override
  String get medium => 'Medium';

  @override
  String get members => 'Members';

  @override
  String membersSelectedOfTotal(int selected, int total) {
    return 'MEMBERS: $selected of $total';
  }

  @override
  String get middleName => 'Middle Name';

  @override
  String get month => 'Month';

  @override
  String get monthly => 'Monthly';

  @override
  String get myLocation => 'My Location';

  @override
  String get myTasks => 'My Tasks';

  @override
  String get name => 'Name';

  @override
  String nameClockedOutTime(String name) {
    return '$name Clocked Out One Hour Ago';
  }

  @override
  String nameCreatedGroup(String name) {
    return '$name Created Group';
  }

  @override
  String get nationalId => 'National ID';

  @override
  String get nearbyPlaces => 'Nearby Places';

  @override
  String get needHelp => 'Need Help';

  @override
  String get netSalary => 'Net Salary';

  @override
  String get newChannel => 'New Channel';

  @override
  String get newChannelFunctionality =>
      'New Channel Functionality Will be Implemented';

  @override
  String get newGroup => 'New Group';

  @override
  String get newPassword => 'New Password';

  @override
  String get news => 'News';

  @override
  String get next => 'Next';

  @override
  String get no => 'No';

  @override
  String get noContactsFound => 'No Contacts Found';

  @override
  String get noData => 'No Data Available';

  @override
  String get noIssues => 'No Issues';

  @override
  String get noProjectFound => 'No Project Found';

  @override
  String get noTasksFound => 'No Tasks Found for This Project';

  @override
  String get notAvailable => 'Not Available';

  @override
  String get notEditable => 'Not Editable';

  @override
  String get notes => 'Notes';

  @override
  String get notification => 'Notification';

  @override
  String get notifications => 'Notifications';

  @override
  String get numberOfEmployees => 'Number of Employees';

  @override
  String get officeAddress => 'Office Address';

  @override
  String get officeContactInformation => 'Office Contact Information';

  @override
  String get officeDetails => 'Office Details';

  @override
  String get officeEmployees => 'Office Employees';

  @override
  String get officeInformation => 'Office Information';

  @override
  String get officeLocation => 'Office Location';

  @override
  String get officeLocationDescription => 'Office Location Description';

  @override
  String get officeName => 'Office Name';

  @override
  String officeOfficeName(String officename) {
    return 'Office: $officename';
  }

  @override
  String get offices => 'Offices';

  @override
  String get officeType => 'Office Type';

  @override
  String get ok => 'OK';

  @override
  String get online => 'Online';

  @override
  String get operatingHours => 'Operating Hours';

  @override
  String get optionalNote => 'Optional Note';

  @override
  String get other => 'Other';

  @override
  String get otherDocuments => 'Other Documents';

  @override
  String get otherInfo => 'Other Info';

  @override
  String overtimeHours(int hours) {
    return '$hours Hours This Month';
  }

  @override
  String get overtimePay => 'Overtime Pay';

  @override
  String get overview => 'Overview';

  @override
  String get paid => 'Paid';

  @override
  String get password => 'Password';

  @override
  String get payForThisPeriod => 'Pay for this Period';

  @override
  String get paymentStatus => 'Payment Status';

  @override
  String get payNow => 'Pay Now';

  @override
  String get payrollSummary => 'Payroll Summary';

  @override
  String payslipConfirmationWarning(Object name) {
    return 'By Clicking Send, This Payslip Will be Sent To $name';
  }

  @override
  String get pending => 'Pending';

  @override
  String get pendingLeaveRequests => 'Pending Leave Requests';

  @override
  String get pendingRequest => 'Pending Request';

  @override
  String get pendingRequests => 'Pending Requests';

  @override
  String get performance => 'Performance';

  @override
  String get performanceAndAchievements => 'Performance & Achievements';

  @override
  String get performanceBonus => 'Performance Bonus';

  @override
  String get permissionsDepartment => 'Permissions Department';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get phone => 'Phone';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get phoneNumberOrEmailAddress => 'Phone Number or Email Address';

  @override
  String get pleaseCreateAProject =>
      'Please Create A Project First To Add Tasks';

  @override
  String get pleaseCreateATask => 'Please Create a Task';

  @override
  String get pleaseDrawSignature => 'Please Draw Your Signature';

  @override
  String get pleaseEnterAGroupName => 'Please Enter a Group Name';

  @override
  String get policy => 'Policy';

  @override
  String get policyDescription => 'Policy Description';

  @override
  String get policyName => 'Policy Name';

  @override
  String get poor => 'Poor';

  @override
  String get previousSalaries => 'Previous Salaries';

  @override
  String get priority => 'Priority';

  @override
  String get proceedConfirmation => 'ARE YOU SURE YOU WANT TO PROCEED?';

  @override
  String get profile => 'Profile';

  @override
  String get project => 'Project';

  @override
  String get projects => 'Projects';

  @override
  String get promoteConfirmationTitle =>
      'Are You Sure You Want To Promote This Employee To The New Position?';

  @override
  String get promotion => 'Promotion';

  @override
  String get publish => 'Publish';

  @override
  String publishedByNAME(String name) {
    return 'Published By $name';
  }

  @override
  String get read => 'Read';

  @override
  String get reasonForResignation => 'Reason for Resignation';

  @override
  String get recentlyViewed => 'Recently Viewed';

  @override
  String get record => 'Record';

  @override
  String get reject => 'Reject';

  @override
  String get rejected => 'Rejected';

  @override
  String get remainingLeaves => 'Remaining Leaves';

  @override
  String get rememberMe => 'Remember Me';

  @override
  String get reportAProblem => 'Report a Problem';

  @override
  String get requestedEdit => 'Requested Edit';

  @override
  String get requestForExpenses => 'Request for Expenses';

  @override
  String get requestForLeave => 'Request for Leave';

  @override
  String get requestSentSuccessfully => 'Request sent successfully';

  @override
  String get requestSubmittedSuccessfully =>
      'Your Request Has Been Submitted Successfully';

  @override
  String get requestToChatWithHr => 'Request to Chat with HR';

  @override
  String get resendCode => 'Resend Code';

  @override
  String get resignation => 'Resignation';

  @override
  String get resignationRequest => 'Resignation Request';

  @override
  String get resigned => 'Resigned';

  @override
  String get restaurant => 'Restaurant';

  @override
  String get retry => 'Retry';

  @override
  String get review => 'Review';

  @override
  String get reward => 'Reward';

  @override
  String get role => 'Role';

  @override
  String get salary => 'Salary';

  @override
  String get salaryDetails => 'Salary Details';

  @override
  String get salaryHistory => 'Salary History';

  @override
  String get salaryManagement => 'Salary Management';

  @override
  String get salaryPaymentConfirmation => 'Salary Payment Confirmation';

  @override
  String get save => 'Save';

  @override
  String get savedSuccessfully => 'Saved Successfully';

  @override
  String get saveSend => 'Save & Send';

  @override
  String get schedule => 'Schedule';

  @override
  String get schedules => 'Schedules';

  @override
  String get search => 'Search';

  @override
  String get searchForATask => 'Search for a Task';

  @override
  String get searching => 'Searching';

  @override
  String get searchingForATask => 'Searching for a Task';

  @override
  String get selectAudience => 'Select Audience';

  @override
  String get selectClosingTime => 'Select Closing Time';

  @override
  String get selectContacts => 'Select Contacts';

  @override
  String get selectDocument => 'Select Document';

  @override
  String get selectDocuments => 'Select Documents';

  @override
  String get selectOpeningTime => 'Select Opening Time';

  @override
  String get selectTimeFrom => 'Select Time From';

  @override
  String get selectTimeTo => 'Select Time To';

  @override
  String get send => 'Send';

  @override
  String get sendShiftChangeRequest => 'Send Shift Change Request';

  @override
  String get sendYourCurrentLocation => 'Send Your Current Location';

  @override
  String get sendYourLiveLocation => 'Send Your Live Location';

  @override
  String get settings => 'Settings';

  @override
  String get shiftDetails => 'Shift Details';

  @override
  String get shiftEdit => 'Shift Edit';

  @override
  String get shiftEditRequests => 'Shift Edit Requests';

  @override
  String get shiftHistory => 'Shift History';

  @override
  String get shiftWorkingTime => 'Shift Working Time';

  @override
  String get showAll => 'Show All';

  @override
  String get sinceStartOfYear => 'Since Start of Year';

  @override
  String get someErrorOcurred => 'Some Error Occurred';

  @override
  String get sorrySomeErrorOccured => 'Sorry, Some Error Occured';

  @override
  String get startBreak => 'Start Break';

  @override
  String get startDate => 'Start Date';

  @override
  String get startWork => 'Start Work';

  @override
  String get status => 'Status';

  @override
  String get statusUpdate => 'Status Update';

  @override
  String get submit => 'Submit';

  @override
  String get submitResignation => 'Submit Resignation';

  @override
  String get success => 'Success';

  @override
  String get successfullyCompletedShift =>
      'You Have Successfully Completed Your Shift.';

  @override
  String get successfullyPaid => 'Paid Successfully';

  @override
  String get sundayToFriday => 'Sunday to Friday';

  @override
  String get support => 'Support';

  @override
  String get systemGeneratedAdditions => 'System-Generated Additions';

  @override
  String get systemGeneratedDeductions => 'System-Generated Deductions';

  @override
  String get taskAddedSuccessfully => 'Task Added Successfully';

  @override
  String get taskCompleted => 'Task Completed';

  @override
  String get taskDescription => 'Task Description';

  @override
  String get taskDetails => 'Task Details';

  @override
  String get tasks => 'Tasks';

  @override
  String get taxes => 'Taxes';

  @override
  String get team => 'Team';

  @override
  String get teams => 'Teams';

  @override
  String get termsOfUse => 'Terms and Conditions';

  @override
  String get theme => 'Theme';

  @override
  String get themeMode => 'Theme Mode';

  @override
  String get theNewPosition => 'The New Position';

  @override
  String get theNewSalary => 'The New Salary';

  @override
  String get thereAreNoNotifications => 'There Are No Notifications';

  @override
  String get theReason => 'The Reason';

  @override
  String get thisMonth => 'This Month';

  @override
  String get thisMonthFilter => 'This Month';

  @override
  String get thisMonthsPayroll => 'This Month\'s Payroll';

  @override
  String get thisMonthsSalary => 'This Month\'s Salary';

  @override
  String get thisMonthsSalaryDetails => 'This Month\'s Salary Details';

  @override
  String get thisWeek => 'This Week';

  @override
  String get timeClock => 'Time Clock';

  @override
  String get timeOffRequests => 'Leave Requests';

  @override
  String get timeOffTaken => 'Leaves Taken';

  @override
  String get timesheet => 'Timesheet';

  @override
  String get timeZone => 'Time Zone';

  @override
  String get timingsError => 'The Timings Look Incorrect';

  @override
  String get title => 'Title';

  @override
  String get to => 'To';

  @override
  String get today => 'Today';

  @override
  String get totalAdditions => 'Total Additions';

  @override
  String get totalDeductions => 'Total Deductions';

  @override
  String get totalEmployees => 'Total Employees';

  @override
  String get totalHours => 'Total Hours';

  @override
  String get totalShiftTime => 'Total Shift Time';

  @override
  String get typing => 'Typing...';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get unionFees => 'Union Fees';

  @override
  String get unpaid => 'Unpaid';

  @override
  String get unread => 'Unread';

  @override
  String get updatedSuccessfully => 'Updated Successfully';

  @override
  String get updatePolicy => 'Update Policy';

  @override
  String get uploadDocuments => 'Upload Documents';

  @override
  String valueHours(num hours) {
    return '$hours Hours';
  }

  @override
  String get verificationCode => 'Please Enter The Verification Code Sent To';

  @override
  String get viewActivity => 'View Activity';

  @override
  String get viewAll => 'View All';

  @override
  String get waitingForManagerApproval => 'Waiting for Manager Approval';

  @override
  String get website => 'Website';

  @override
  String get weekly => 'Weekly';

  @override
  String get welcome => 'Welcome';

  @override
  String get workDayYesNo => 'Work Day (Yes/No)';

  @override
  String get workingHours => 'Working Hours';

  @override
  String get workingSchedule => 'Working Schedule';

  @override
  String get workspace => 'Workspace';

  @override
  String get writeAMessage => 'Write a Message...';

  @override
  String get writeANoteForTheEmployee => 'Write a Note for The Employee';

  @override
  String get writeFeedback => 'Write Feedback';

  @override
  String get year => 'Year';

  @override
  String get yearly => 'Yearly';

  @override
  String get yes => 'Yes';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get you => 'You';

  @override
  String youCreatedGroup(String name) {
    return '$name Created This Group';
  }

  @override
  String get zipCode => 'Zip Code';

  @override
  String get kurdish => 'Kurdish';

  @override
  String get salaries => 'Salaries';

  @override
  String get overtimeHoursLabel => 'Overtime Hours';

  @override
  String hoursForToday(Object hours) {
    return '$hours Hours for Today';
  }

  @override
  String get amountIqd => 'Amount IQD';

  @override
  String get am => 'AM';

  @override
  String get pm => 'PM';

  @override
  String get january => 'January';

  @override
  String get february => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'October';

  @override
  String get november => 'November';

  @override
  String get december => 'December';

  @override
  String get halfDay => 'Half Day';

  @override
  String get jan => 'Jan';

  @override
  String get feb => 'Feb';

  @override
  String get mar => 'Mar';

  @override
  String get apr => 'Apr';

  @override
  String get jun => 'Jun';

  @override
  String get jul => 'Jul';

  @override
  String get aug => 'Aug';

  @override
  String get sep => 'Sep';

  @override
  String get oct => 'Oct';

  @override
  String get nov => 'Nov';

  @override
  String get dec => 'Dec';

  @override
  String hoursLetter(Object hours) {
    return '$hours H';
  }

  @override
  String get approved => 'Approved';

  @override
  String get failed => 'Failed';

  @override
  String sizeKB(Object size) {
    return '$size KB';
  }

  @override
  String sizeMB(Object size) {
    return '$size MB';
  }

  @override
  String get salaryEdit => 'Salary Edit';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get canView => 'Can View';

  @override
  String get canEdit => 'Can Edit';

  @override
  String singleFileLargeError(Object size) {
    return 'The Selected File Is Too Large (Max ${size}MB).';
  }

  @override
  String multiFileLargeError(Object count) {
    return '$count File(s) Were Too Large and Not Included.';
  }

  @override
  String get sendForApproval => 'Send for Approval';

  @override
  String noShiftOnDate(Object date) {
    return 'No Shift Recorded on $date';
  }

  @override
  String get policies => 'Policies';

  @override
  String get pickerTakePhoto => 'Take Photo';

  @override
  String get pickerRecordVideo => 'Record Video';

  @override
  String get pickerChooseFromGallery => 'Choose From Gallery';

  @override
  String get pickerChooseFile => 'Choose a File';

  @override
  String get payslipSuccessNotification =>
      'Payslip was Sent  to The Employee Successfully';

  @override
  String get imageOrVideo => 'Image / Video';

  @override
  String paymentMadeOnDate(Object date) {
    return 'Payment Made on $date';
  }

  @override
  String get didNotWorkOnThisDay => 'Did Not Work on This Day';

  @override
  String get publishedBy => 'Published By';

  @override
  String get hrHasApprovedChat => 'HR Has Approved Chat';

  @override
  String get endBreak => 'End Break';

  @override
  String get noShiftRecordedOn => 'No Shift Recorded on';

  @override
  String get h => 'H';

  @override
  String get m => 'M';
}
