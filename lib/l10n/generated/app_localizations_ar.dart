// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get about => 'حول';

  @override
  String get aboutUs => 'معلومات عنا';

  @override
  String get absenceDeduction => 'خصم الغياب';

  @override
  String get accountInfo => 'معلومات الحساب';

  @override
  String get active => 'نشط';

  @override
  String get activity => 'النشاط';

  @override
  String get add => 'إضافة';

  @override
  String get addAComment => 'إضافة تعليق';

  @override
  String get addAnnouncement => 'إضافة إعلان';

  @override
  String get addATask => 'إضافة مهمة';

  @override
  String get addBreak => 'إضافة استراحة';

  @override
  String get addComment => 'إضافة تعليق';

  @override
  String get addDepartment => 'إضافة قسم';

  @override
  String get addedSuccessfully => 'تمت الإضافة بنجاح';

  @override
  String get addEmployee => 'إضافة موظف';

  @override
  String get additionalNotes => 'ملاحظات إضافية';

  @override
  String get addLeave => 'إضافة إجازة';

  @override
  String get addNewOffice => 'إضافة مكتب جديد';

  @override
  String get addNote => 'إضافة ملاحظة';

  @override
  String get addPolicy => 'إضافة سياسة';

  @override
  String get address => 'العنوان';

  @override
  String get addressInformation => 'معلومات العنوان';

  @override
  String get addShift => 'إضافة دوام';

  @override
  String get addTask => 'إضافة مهمة';

  @override
  String get adminContactMessage =>
      'إذا كنت ترغب في التعديل، يرجى التواصل مع الإدارة';

  @override
  String get ai => 'AI';

  @override
  String get aiCapablityLine1 => 'هل تودّ أن أنظم لك مهمة؟';

  @override
  String get aiCapablityLine2 => 'أبحث عن معلومات موظف؟';

  @override
  String get aiCapablityLine3 => 'أو أُحدث شيئًا لك؟';

  @override
  String get aiCapablityLine4 => 'فقط أخبرني بما تحتاج, أنا هنا لمساعدتك!';

  @override
  String get aiChatGreeting => 'مرحبًا بك في اكو بصمة AI!';

  @override
  String get aiGreetingText => 'كيف يمكنني مساعدتك اليوم؟';

  @override
  String get akoBasma => 'اكو بصمة';

  @override
  String get all => 'الكل';

  @override
  String get allDay => 'طوال اليوم';

  @override
  String get allEmployees => 'جميع الموظفين';

  @override
  String get allOffices => 'جميع المكاتب';

  @override
  String get allowances => 'المخصصات';

  @override
  String get allProjects => 'جميع المشاريع';

  @override
  String get allTime => 'كل الوقت';

  @override
  String get amount => 'المبلغ';

  @override
  String get amountOfDecrease => 'مقدار النقص';

  @override
  String get amountOfIncrease => 'مقدار الزيادة';

  @override
  String get amountOfIncreaseOrDecrease => 'مقدار الزيادة أو النقص';

  @override
  String get announcement => 'إعلان';

  @override
  String get approve => 'موافقة';

  @override
  String get approvedBy => 'تمت الموافقة بواسطة';

  @override
  String get approveTask => 'الموافقة على المهمة';

  @override
  String approximateToDistanceMetres(Object distance) {
    return 'على بعد $distance أمتار تقريبًا';
  }

  @override
  String get arabic => 'العربية';

  @override
  String get archive => 'أرشفة';

  @override
  String get archiveEmployee => 'أرشفة موظف';

  @override
  String get archiveEmployeeWarning => 'إذا تم أرشفة الموظف، سيتم تعطيل حسابه!';

  @override
  String get archiveEmployeeWarningSubtitle =>
      'هل أنت متأكد أنك تريد أرشفة الموظف؟';

  @override
  String get archives => 'الأرشيف';

  @override
  String get asiaBaghdad => 'آسيا/بغداد';

  @override
  String get assign => 'تعيين';

  @override
  String get assignee => 'المُكلّف';

  @override
  String get assignToMe => 'تعيين لي';

  @override
  String get attachment => 'مرفق';

  @override
  String get attendance => 'الحضور';

  @override
  String get availableOnWeekends => 'متاح في عطلات نهاية الأسبوع';

  @override
  String get back => 'رجوع';

  @override
  String get backlog => 'المهام المتأخرة';

  @override
  String get basicSalary => 'الراتب الأساسي';

  @override
  String get branch => 'الفرع';

  @override
  String get breakTime => 'وقت الاستراحة:';

  @override
  String get breakTimeLabel => 'وقت الاستراحة';

  @override
  String get call => 'اتصال';

  @override
  String get camera => 'كاميرا';

  @override
  String get cancel => 'إلغاء';

  @override
  String get canIGetMoreInfo => 'هل يمكنني الحصول على مزيد من المعلومات؟';

  @override
  String get changesSavedSuccessfully => 'تم حفظ التغييرات بنجاح';

  @override
  String get chat => 'الرسائل';

  @override
  String get chatRequestConfirmation => 'هل أنت متأكد أنك تريد إرسال الطلب؟';

  @override
  String get chatWithAi => 'الدردشة مع AI';

  @override
  String get chatWithHr => 'الدردشة مع الموارد البشرية';

  @override
  String get checkedOutHelpText => 'تم تسجيل الخروج';

  @override
  String get checkInTime => 'وقت الدخول';

  @override
  String get checkOutTime => 'وقت الخروج';

  @override
  String get checkYourMessages => 'تحقق من رسائلك';

  @override
  String get city => 'المدينة';

  @override
  String get clear => 'مسح';

  @override
  String get clearSelection => 'مسح التحديد';

  @override
  String get clickToUpload => 'انقر للرفع';

  @override
  String get clockIn => 'تسجيل دخول';

  @override
  String get clockInTime => 'وقت تسجيل الدخول';

  @override
  String get clockOut => 'تسجيل خروج';

  @override
  String get clockOutTime => 'وقت تسجيل الخروج';

  @override
  String get close => 'إغلاق';

  @override
  String get closingTimeMustBeAfterOpeningTime =>
      'وقت الإغلاق يجب أن يكون بعد وقت الفتح.';

  @override
  String get comments => 'التعليقات';

  @override
  String get companyDirector => 'مدير الشركة';

  @override
  String get companyInformation => 'معلومات الشركة';

  @override
  String get companyJobNumberStart => 'تسلسل أرقام موظفي الشركة';

  @override
  String get companyName => 'اسم الشركة';

  @override
  String get companyNews => 'أخبار الشركة';

  @override
  String get companyPolicy => 'سياسة الشركة';

  @override
  String get completed => 'مكتمل';

  @override
  String get completedTasks => 'المهام المكتملة';

  @override
  String get confirm => 'تأكيد';

  @override
  String get confirmed => 'تم التأكيد';

  @override
  String get confirmHours => 'تأكيد الساعات';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get contact => 'جهات الاتصال';

  @override
  String get contactNumber => 'رقم الاتصال';

  @override
  String get contractType => 'نوع العقد';

  @override
  String get country => 'الدولة';

  @override
  String get create => 'إنشاء';

  @override
  String get createAProject => 'إنشاء مشروع';

  @override
  String get createATask => 'إنشاء مهمة';

  @override
  String get createProject => 'إنشاء مشروع';

  @override
  String get createTask => 'إنشاء مهمة';

  @override
  String get critical => 'شديد الأهمية';

  @override
  String get currency => 'العملة';

  @override
  String get currentShift => 'الدوام الحالي';

  @override
  String get customDateRange => 'نطاق التاريخ المخصص';

  @override
  String get daily => 'يومي';

  @override
  String get dailyShifts => 'الدوامات اليومية';

  @override
  String get dark => 'داكن';

  @override
  String get date => 'التاريخ';

  @override
  String get dateOfBirth => 'تاريخ الميلاد';

  @override
  String get dateOfJoining => 'تاريخ الانضمام';

  @override
  String get dateOfResignation => 'تاريخ الاستقالة';

  @override
  String get days => 'الأيام';

  @override
  String get decrease => 'نقص';

  @override
  String get deductions => 'الخصومات';

  @override
  String get delay => 'تأخير:';

  @override
  String get delete => 'حذف';

  @override
  String get deleteEmployee => 'حذف الموظف';

  @override
  String get deleteEmployeeWarning =>
      'لا يمكن التراجع عن هذا الإجراء. سيتم حذف جميع بيانات الموظف والمعلومات المرتبطة من النظام';

  @override
  String get delivered => 'تم التسليم';

  @override
  String get deadline => 'الموعد النهائي';

  @override
  String get department => 'القسم';

  @override
  String get departmentInformation => 'معلومات القسم';

  @override
  String get departmentName => 'اسم القسم';

  @override
  String get description => 'الوصف';

  @override
  String get design => 'التصميم';

  @override
  String get disciplinaryFine => 'غرامة تأديبية';

  @override
  String get document => 'مستند';

  @override
  String get documents => 'المستندات';

  @override
  String get downloadAll => 'تنزيل الكل';

  @override
  String get draft => 'مسودة';

  @override
  String get drawSignature => 'ارسم التوقيع';

  @override
  String get dueDate => 'تاريخ الاستحقاق';

  @override
  String get duration => 'المدة';

  @override
  String get edit => 'تعديل';

  @override
  String get editAccountInfo => 'تعديل معلومات الحساب';

  @override
  String get editAddress => 'تعديل العنوان';

  @override
  String get editAnnouncement => 'تعديل الإعلان';

  @override
  String get editGroup => 'تعديل المجموعة';

  @override
  String get editInfo => 'تعديل المعلومات';

  @override
  String get editMembers => 'تعديل الأعضاء';

  @override
  String get editName => 'تعديل الاسم';

  @override
  String get editPolicies => 'تعديل السياسات';

  @override
  String get editSalary => 'تعديل الراتب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get emailAddress => 'عنوان البريد الإلكتروني';

  @override
  String get emp => 'الرقم الوظيفي';

  @override
  String get employeeAddedSuccessfully => 'تمت إضافة الموظف بنجاح';

  @override
  String get employeeIdCopySuccessMsg => 'تم نسخ رقم الموظف للصق';

  @override
  String get employeeManagement => 'إدارة الموظفين';

  @override
  String get employeeOfTheMonth => 'موظف الشهر';

  @override
  String get employeeRatingQuestionTitle => 'كيف تقيم هذا الموظف؟';

  @override
  String get employees => 'الموظفون';

  @override
  String get employeesOfTheMonth => 'موظفو الشهر';

  @override
  String get employeesWorkingToday => 'الموظفون العاملون اليوم';

  @override
  String get employeeTimeOff => 'إجازات الموظف';

  @override
  String get employeeTracking => 'تتبع الموظف';

  @override
  String get employementType => 'نوع التوظيف';

  @override
  String get endChat => 'إنهاء الدردشة';

  @override
  String get endShift => 'إنهاء الدوام';

  @override
  String get endWork => 'إنهاء العمل';

  @override
  String get english => 'الإنجليزية';

  @override
  String get enterGroupDescription => 'أدخل وصف المجموعة';

  @override
  String get enterGroupName => 'أدخل اسم المجموعة';

  @override
  String enterVerificationCode(String phonenumber) {
    return 'يرجى إدخال رمز التحقق المرسل إلى $phonenumber';
  }

  @override
  String get enterYourNoteHere => 'أدخل ملاحظتك هنا';

  @override
  String get error => 'خطأ';

  @override
  String get expenseRequests => 'طلبات النفقات';

  @override
  String get expenses => 'النفقات';

  @override
  String get failedToSelectImage =>
      'فشل في تحديد الصورة. يرجى المحاولة مرة أخرى.';

  @override
  String get feedback => 'التعليقات';

  @override
  String get fileSelected => 'تم تحديد الملف';

  @override
  String get filter => 'فلتر';

  @override
  String get firstName => 'الاسم الأول';

  @override
  String get fixed => 'ثابت';

  @override
  String get forgotYourPassword => 'هل نسيت كلمة المرور؟';

  @override
  String get from => 'من';

  @override
  String get fullAccess => 'وصول كامل';

  @override
  String get gallery => 'المعرض';

  @override
  String get generalSettings => 'الإعدادات العامة';

  @override
  String get generatePayslip => 'إنشاء كشف راتب';

  @override
  String get good => 'جيد';

  @override
  String get gross => 'إجمالي';

  @override
  String get grossSalary => 'إجمالي الراتب';

  @override
  String get groupDescription => 'وصف المجموعة';

  @override
  String groupMembersCount(int membercount) {
    return '$membercount أعضاء';
  }

  @override
  String get groupName => 'اسم المجموعة';

  @override
  String get headquarters => 'المقر الرئيسي';

  @override
  String get helloHowCanIHelpYouToday => 'مرحبًا! كيف يمكنني مساعدتك اليوم؟';

  @override
  String get helloWorld => 'مرحبًا بالعالم!';

  @override
  String get high => 'عالي';

  @override
  String get holidays => 'العطلات';

  @override
  String get home => 'الرئيسية';

  @override
  String get hourly => 'بالساعة';

  @override
  String get hourlyRate => 'سعر الساعة';

  @override
  String get hours => 'الساعات';

  @override
  String get hrApprovalMessage => 'تمت الموافقة على طلبك.';

  @override
  String get hrManager => 'مدير الموارد البشرية';

  @override
  String get ifYouClockOutNow => 'إذا سجلت الخروج الآن، فسيتم تسجيل خروجك في';

  @override
  String get increase => 'زيادة';

  @override
  String get inProgress => 'قيد التقدم';

  @override
  String get iqd => 'دينار عراقي';

  @override
  String get iraq => 'العراق';

  @override
  String get jobInformation => 'معلومات الوظيفة';

  @override
  String get jobTitle => 'المسمى الوظيفي';

  @override
  String get joined => 'انضم';

  @override
  String get language => 'اللغة';

  @override
  String get lastName => 'الاسم الأخير';

  @override
  String lastSeenOn(String dateTime) {
    return 'آخر ظهور في $dateTime';
  }

  @override
  String lastSeenOnLastseen(Object lastSeen) {
    return 'آخر ظهور في $lastSeen';
  }

  @override
  String get lastWorkingDay => 'آخر يوم عمل';

  @override
  String get leave => 'إجازة';

  @override
  String get leaveFrom => 'الإجازة من';

  @override
  String get leaveManagement => 'إدارة الإجازات';

  @override
  String get leavesHistory => 'سجل الإجازات';

  @override
  String get leavesTaken => 'الإجازات المأخوذة';

  @override
  String get leaveTo => 'الإجازة إلى';

  @override
  String get leaveType => 'نوع الإجازة';

  @override
  String get light => 'فاتح';

  @override
  String get location => 'الموقع';

  @override
  String get logIn => 'تسجيل الدخول';

  @override
  String get logo => 'الشعار';

  @override
  String get logout => 'تسجيل خروج';

  @override
  String get logOut => 'تسجيل الخروج';

  @override
  String get logoutConfirmation => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get low => 'منخفض';

  @override
  String get manageDepartment => 'إدارة القسم';

  @override
  String get manageEmployees => 'إدارة الموظفين';

  @override
  String get management => 'الإدارة';

  @override
  String get manageOffices => 'إدارة المكاتب';

  @override
  String get manageProject => 'إدارة المشروع';

  @override
  String get manager => 'المدير';

  @override
  String get managerInformation => 'معلومات المدير';

  @override
  String get managersFeedback => 'تعليقات المدير';

  @override
  String get managersOnly => 'للمدراء فقط';

  @override
  String get manageSalaries => 'إدارة الرواتب';

  @override
  String get manageYourEmployees => 'إدارة موظفيك';

  @override
  String get manualAdditions => 'إضافات يدوية';

  @override
  String get manualDeductions => 'خصومات يدوية';

  @override
  String maxFileSizeInMB(int sizeInMb) {
    return '(الحجم الأقصى للملف: $sizeInMb ميجابايت)';
  }

  @override
  String maxFileImageVideoSize(int sizeinmb) {
    return '(الحجم الأقصى للملف/الصورة/الفيديو: $sizeinmb ميجابايت)';
  }

  @override
  String get medium => 'متوسط';

  @override
  String get members => 'الأعضاء';

  @override
  String membersSelectedOfTotal(int selected, int total) {
    return 'الأعضاء: $selected من $total';
  }

  @override
  String get middleName => 'الاسم الأوسط';

  @override
  String get month => 'الشهر';

  @override
  String get monthly => 'شهري';

  @override
  String get myLocation => 'موقعي';

  @override
  String get myTasks => 'مهامي';

  @override
  String get name => 'الاسم';

  @override
  String nameClockedOutTime(String name) {
    return '$name تم تسجيل خروجه قبل ساعة';
  }

  @override
  String nameCreatedGroup(String name) {
    return '$name أنشأ مجموعة';
  }

  @override
  String get nationalId => 'الرقم الوطني';

  @override
  String get nearbyPlaces => 'الأماكن القريبة';

  @override
  String get needHelp => 'هل تحتاج مساعدة؟';

  @override
  String get netSalary => 'صافي الراتب';

  @override
  String get newChannel => 'قناة جديدة';

  @override
  String get newChannelFunctionality => 'سيتم تنفيذ ميزة القناة الجديدة';

  @override
  String get newGroup => 'مجموعة جديدة';

  @override
  String get newPassword => 'كلمة مرور جديدة';

  @override
  String get news => 'الأخبار';

  @override
  String get next => 'التالي';

  @override
  String get no => 'لا';

  @override
  String get noContactsFound => 'لم يتم العثور على جهات اتصال';

  @override
  String get noData => 'لا توجد بيانات متاحة';

  @override
  String get noIssues => 'لا توجد مشاكل';

  @override
  String get noProjectFound => 'لم يتم العثور على مشروع';

  @override
  String get noTasksFound => 'لم يتم العثور على مهام لهذا المشروع';

  @override
  String get notAvailable => 'غير متوفر';

  @override
  String get notEditable => 'غير قابل للتعديل';

  @override
  String get notes => 'الملاحظات';

  @override
  String get notification => 'الإشعار';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get numberOfEmployees => 'عدد الموظفين';

  @override
  String get officeAddress => 'عنوان المكتب';

  @override
  String get officeContactInformation => 'معلومات الاتصال بالمكتب';

  @override
  String get officeDetails => 'تفاصيل المكتب';

  @override
  String get officeEmployees => 'موظفو المكتب';

  @override
  String get officeInformation => 'معلومات المكتب';

  @override
  String get officeLocation => 'موقع المكتب';

  @override
  String get officeLocationDescription => 'وصف موقع المكتب';

  @override
  String get officeName => 'اسم المكتب';

  @override
  String officeOfficeName(String officename) {
    return 'المكتب: $officename';
  }

  @override
  String get offices => 'المكاتب';

  @override
  String get officeType => 'نوع المكتب';

  @override
  String get ok => 'موافق';

  @override
  String get online => 'متصل';

  @override
  String get operatingHours => 'ساعات العمل';

  @override
  String get optionalNote => 'ملاحظة اختيارية';

  @override
  String get other => 'أخرى';

  @override
  String get otherDocuments => 'مستندات أخرى';

  @override
  String get otherInfo => 'معلومات أخرى';

  @override
  String overtimeHours(int hours) {
    return '$hours ساعات هذا الشهر';
  }

  @override
  String get overtimePay => 'أجر العمل الإضافي';

  @override
  String get overview => 'نظرة عامة';

  @override
  String get paid => 'تم الدفع';

  @override
  String get password => 'كلمة المرور';

  @override
  String get payForThisPeriod => 'الدفع لهذه الفترة';

  @override
  String get paymentStatus => 'حالة الدفع';

  @override
  String get payNow => 'ادفع الآن';

  @override
  String get payrollSummary => 'ملخص الرواتب';

  @override
  String payslipConfirmationWarning(Object name) {
    return 'بالنقر على إرسال، سيتم إرسال كشف الراتب هذا إلى $name';
  }

  @override
  String get pending => 'قيد الانتظار';

  @override
  String get pendingLeaveRequests => 'طلبات الإجازة المعلقة';

  @override
  String get pendingRequest => 'طلب معلق';

  @override
  String get pendingRequests => 'الطلبات المعلقة';

  @override
  String get performance => 'الأداء';

  @override
  String get performanceAndAchievements => 'الأداء والإنجازات';

  @override
  String get performanceBonus => 'مكافأة الأداء';

  @override
  String get permissionsDepartment => 'قسم الصلاحيات';

  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get phone => 'الهاتف';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get phoneNumberOrEmailAddress =>
      'رقم الهاتف أو عنوان البريد الإلكتروني';

  @override
  String get pleaseCreateAProject => 'يرجى إنشاء مشروع أولاً لإضافة مهام';

  @override
  String get pleaseCreateATask => 'يرجى إنشاء مهمة';

  @override
  String get pleaseDrawSignature => 'يرجى رسم توقيعك';

  @override
  String get pleaseEnterAGroupName => 'يرجى إدخال اسم المجموعة';

  @override
  String get policy => 'السياسة';

  @override
  String get policyDescription => 'وصف السياسة';

  @override
  String get policyName => 'اسم السياسة';

  @override
  String get poor => 'ضعيف';

  @override
  String get previousSalaries => 'الرواتب السابقة';

  @override
  String get priority => 'أولوية';

  @override
  String get proceedConfirmation => 'هل أنت متأكد أنك تريد المتابعة؟';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get project => 'مشروع';

  @override
  String get projects => 'المشاريع';

  @override
  String get promoteConfirmationTitle =>
      'هل أنت متأكد أنك تريد ترقية هذا الموظف إلى المنصب الجديد؟';

  @override
  String get promotion => 'ترقية';

  @override
  String get publish => 'نشر';

  @override
  String publishedByNAME(String name) {
    return 'تم النشر بواسطة $name';
  }

  @override
  String get read => 'قراءة';

  @override
  String get reasonForResignation => 'سبب الاستقالة';

  @override
  String get recentlyViewed => 'تمت المشاهدة مؤخرًا';

  @override
  String get record => 'سجل';

  @override
  String get reject => 'رفض';

  @override
  String get rejected => 'تم الرفض';

  @override
  String get remainingLeaves => 'الإجازات المتبقية';

  @override
  String get rememberMe => 'تذكرني';

  @override
  String get reportAProblem => 'الإبلاغ عن مشكلة';

  @override
  String get requestedEdit => 'تعديل تم طلبه';

  @override
  String get requestForExpenses => 'طلب النفقات';

  @override
  String get requestForLeave => 'طلب الإجازة';

  @override
  String get requestSentSuccessfully => 'تم إرسال طلبك بنجاح';

  @override
  String get requestSubmittedSuccessfully => 'تم تقديم طلبك بنجاح';

  @override
  String get requestToChatWithHr => 'طلب الدردشة مع الموارد البشرية';

  @override
  String get resendCode => 'إعادة إرسال الرمز';

  @override
  String get resignation => 'الاستقالة';

  @override
  String get resignationRequest => 'طلب الاستقالة';

  @override
  String get resigned => 'مستقيل';

  @override
  String get restaurant => 'مطعم';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get review => 'مراجعة';

  @override
  String get reward => 'مكافأة';

  @override
  String get role => 'الدور';

  @override
  String get salary => 'الراتب';

  @override
  String get salaryDetails => 'تفاصيل الراتب';

  @override
  String get salaryHistory => 'سجل الرواتب';

  @override
  String get salaryManagement => 'إدارة الرواتب';

  @override
  String get salaryPaymentConfirmation => 'تأكيد دفع الراتب';

  @override
  String get save => 'حفظ';

  @override
  String get savedSuccessfully => 'تم الحفظ بنجاح';

  @override
  String get saveSend => 'حفظ وإرسال';

  @override
  String get schedule => 'جدول';

  @override
  String get schedules => 'الجداول';

  @override
  String get search => 'بحث';

  @override
  String get searchForATask => 'ابحث عن مهمة';

  @override
  String get searching => 'جاري البحث';

  @override
  String get searchingForATask => 'جاري البحث عن مهمة';

  @override
  String get selectAudience => 'تحديد الجمهور';

  @override
  String get selectClosingTime => 'اختيار وقت الإغلاق';

  @override
  String get selectContacts => 'اختيار جهات الاتصال';

  @override
  String get selectDocument => 'اختيار مستند';

  @override
  String get selectDocuments => 'اختيار مستندات';

  @override
  String get selectOpeningTime => 'اختيار وقت الفتح';

  @override
  String get selectTimeFrom => 'اختيار الوقت من';

  @override
  String get selectTimeTo => 'اختيار الوقت إلى';

  @override
  String get send => 'إرسال';

  @override
  String get sendShiftChangeRequest => 'إرسال طلب تغيير دوام';

  @override
  String get sendYourCurrentLocation => 'إرسال موقعك الحالي';

  @override
  String get sendYourLiveLocation => 'إرسال موقعك المباشر';

  @override
  String get settings => 'الإعدادات';

  @override
  String get shiftDetails => 'تفاصيل الدوام';

  @override
  String get shiftEdit => 'تعديل الدوام';

  @override
  String get shiftEditRequests => 'طلبات تعديل الدوام';

  @override
  String get shiftHistory => 'سجل الدوام';

  @override
  String get shiftWorkingTime => 'وقت العمل في الدوام';

  @override
  String get showAll => 'عرض الكل';

  @override
  String get sinceStartOfYear => 'منذ بداية السنة';

  @override
  String get someErrorOcurred => 'حدث خطأ ما';

  @override
  String get sorrySomeErrorOccured => 'عذراً، حدث خطأ ما';

  @override
  String get startBreak => 'بدء الاستراحة';

  @override
  String get startDate => 'تاريخ البدء';

  @override
  String get startWork => 'بدء العمل';

  @override
  String get status => 'الحالة';

  @override
  String get statusUpdate => 'تحديث الحالة';

  @override
  String get submit => 'إرسال';

  @override
  String get submitResignation => 'إرسال الاستقالة';

  @override
  String get success => 'نجاح';

  @override
  String get successfullyCompletedShift => 'لقد أكملت دوامك بنجاح.';

  @override
  String get successfullyPaid => 'تم الدفع بنجاح';

  @override
  String get sundayToFriday => 'الأحد إلى الجمعة';

  @override
  String get support => 'الدعم';

  @override
  String get systemGeneratedAdditions => 'إضافات تم إنشاؤها بواسطة النظام';

  @override
  String get systemGeneratedDeductions => 'خصومات تم إنشاؤها بواسطة النظام';

  @override
  String get taskAddedSuccessfully => 'تمت إضافة المهمة بنجاح';

  @override
  String get taskCompleted => 'المهمة مكتملة';

  @override
  String get taskDescription => 'وصف المهمة';

  @override
  String get taskDetails => 'تفاصيل المهمة';

  @override
  String get tasks => 'المهام';

  @override
  String get taxes => 'الضرائب';

  @override
  String get team => 'الفريق';

  @override
  String get teams => 'الفرق';

  @override
  String get termsOfUse => 'شروط الاستخدام';

  @override
  String get theme => 'المظهر';

  @override
  String get themeMode => 'وضع المظهر';

  @override
  String get theNewPosition => 'المنصب الجديد';

  @override
  String get theNewSalary => 'الراتب الجديد';

  @override
  String get thereAreNoNotifications => 'لا توجد إشعارات';

  @override
  String get theReason => 'السبب';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get thisMonthFilter => 'هذا الشهر';

  @override
  String get thisMonthsPayroll => 'رواتب هذا الشهر';

  @override
  String get thisMonthsSalary => 'راتب هذا الشهر';

  @override
  String get thisMonthsSalaryDetails => 'تفاصيل راتب هذا الشهر';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get timeClock => 'ساعات الدوام';

  @override
  String get timeOffRequests => 'طلبات الإجازة';

  @override
  String get timeOffTaken => 'الإجازات المأخوذة';

  @override
  String get timesheet => 'سجل الحضور';

  @override
  String get timeZone => 'المنطقة الزمنية';

  @override
  String get timingsError => 'التوقيتات غير صحيحة';

  @override
  String get title => 'العنوان';

  @override
  String get to => 'إلى';

  @override
  String get today => 'اليوم';

  @override
  String get totalAdditions => 'إجمالي الإضافات';

  @override
  String get totalDeductions => 'إجمالي الخصومات';

  @override
  String get totalEmployees => 'إجمالي الموظفين';

  @override
  String get totalHours => 'إجمالي الساعات';

  @override
  String get totalShiftTime => 'إجمالي وقت الدوام';

  @override
  String get typing => 'يكتب...';

  @override
  String get unavailable => 'غير متاح';

  @override
  String get unionFees => 'رسوم الاتحاد';

  @override
  String get unpaid => 'غير مدفوع';

  @override
  String get unread => 'غير مقروء';

  @override
  String get updatedSuccessfully => 'تم التحديث بنجاح';

  @override
  String get updatePolicy => 'تحديث السياسة';

  @override
  String get uploadDocuments => 'تحميل المستندات';

  @override
  String valueHours(num hours) {
    return '$hours ساعات';
  }

  @override
  String get verificationCode => 'يرجى إدخال رمز التحقق المرسل إلى';

  @override
  String get viewActivity => 'عرض النشاط';

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get waitingForManagerApproval => 'في انتظار موافقة المدير';

  @override
  String get website => 'الموقع الإلكتروني';

  @override
  String get weekly => 'أسبوعي';

  @override
  String get welcome => 'مرحبًا بك';

  @override
  String get workDayYesNo => 'يوم عمل (نعم/لا)';

  @override
  String get workingHours => 'ساعات العمل';

  @override
  String get workingSchedule => 'جدول العمل';

  @override
  String get workspace => 'مساحة العمل';

  @override
  String get writeAMessage => 'اكتب رسالة...';

  @override
  String get writeANoteForTheEmployee => 'اكتب ملاحظة للموظف';

  @override
  String get writeFeedback => 'اكتب تقييم';

  @override
  String get year => 'السنة';

  @override
  String get yearly => 'سنوي';

  @override
  String get yes => 'نعم';

  @override
  String get yesterday => 'أمس';

  @override
  String get you => 'أنت';

  @override
  String youCreatedGroup(String name) {
    return '$name أنشأ هذه المجموعة';
  }

  @override
  String get zipCode => 'الرمز البريدي';

  @override
  String get kurdish => 'کوردی';

  @override
  String get salaries => 'الرواتب';

  @override
  String get overtimeHoursLabel => 'ساعات العمل الإضافية';

  @override
  String hoursForToday(Object hours) {
    return '$hours ساعات اليوم';
  }

  @override
  String get amountIqd => 'المبلغ د.ع';

  @override
  String get am => 'ص.';

  @override
  String get pm => 'م.';

  @override
  String get january => 'كانون الثاني';

  @override
  String get february => 'شباط';

  @override
  String get march => 'آذار';

  @override
  String get april => 'نیسان';

  @override
  String get may => 'أيار';

  @override
  String get june => 'حزيران';

  @override
  String get july => 'تموز';

  @override
  String get august => 'آب';

  @override
  String get september => 'أيلول';

  @override
  String get october => 'تشرين الأول';

  @override
  String get november => 'تشرين الثاني';

  @override
  String get december => 'كانون الاول';

  @override
  String get halfDay => 'نصف يوم';

  @override
  String get jan => 'كانون 2';

  @override
  String get feb => 'شباط';

  @override
  String get mar => 'آذار';

  @override
  String get apr => 'نيسان';

  @override
  String get jun => 'حزيران';

  @override
  String get jul => 'تموز';

  @override
  String get aug => 'آب';

  @override
  String get sep => 'أيلول';

  @override
  String get oct => 'تشرين 1';

  @override
  String get nov => 'تشرين 2';

  @override
  String get dec => 'كانون 1';

  @override
  String hoursLetter(Object hours) {
    return '$hours س';
  }

  @override
  String get approved => 'تمت الموافقة';

  @override
  String get failed => 'فشل';

  @override
  String sizeKB(Object size) {
    return '$size KB';
  }

  @override
  String sizeMB(Object size) {
    return '$size MB';
  }

  @override
  String get salaryEdit => 'تعديل الراتب';

  @override
  String get gender => 'الجنس';

  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثى';

  @override
  String get canView => 'يمكن العرض';

  @override
  String get canEdit => 'يمكن التعديل';

  @override
  String singleFileLargeError(Object size) {
    return 'الملف المحدد كبير جدًا (الحد الأقصى $size MB).';
  }

  @override
  String multiFileLargeError(Object count) {
    return '$count الملفات كانت كبيرة جدًا ولم يتم اضافتها.';
  }

  @override
  String get sendForApproval => 'إرسال للموافقة';

  @override
  String noShiftOnDate(Object date) {
    return 'لم يتم تسجيل أي دوام في $date';
  }

  @override
  String get policies => 'السياسات';

  @override
  String get pickerTakePhoto => 'التقط صورة';

  @override
  String get pickerRecordVideo => 'سجل فيديو';

  @override
  String get pickerChooseFromGallery => 'اختر من المعرض';

  @override
  String get pickerChooseFile => 'اختر ملفًا';

  @override
  String get payslipSuccessNotification =>
      'تم إرسال كشف الراتب إلى الموظف بنجاح';

  @override
  String get imageOrVideo => 'صورة / فيديو';

  @override
  String paymentMadeOnDate(Object date) {
    return 'تم الدفع في $date';
  }

  @override
  String get didNotWorkOnThisDay => 'لم يعمل في هذا اليوم';

  @override
  String get publishedBy => 'نُشر بواسطة';

  @override
  String get hrHasApprovedChat => 'وافقت الموارد البشرية على الدردشة';

  @override
  String get endBreak => 'إنهاء الاستراحة';

  @override
  String get noShiftRecordedOn => 'لم يتم تسجيل أي دوام في';

  @override
  String get h => 'س';

  @override
  String get m => 'د';
}
